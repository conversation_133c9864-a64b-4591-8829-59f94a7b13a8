'use client';

/**
 * Serviço para gerenciar localidades (estados e cidades)
 * Utiliza a API do IBGE para obter dados oficiais
 */
export class LocationService {
  private static instance: LocationService;
  private statesCache: State[] | null = null;
  private citiesCache: Record<string, City[]> = {};

  /**
   * Obtém a instância única do serviço (Singleton)
   */
  public static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  /**
   * Normaliza um texto para busca (remove acentos, converte para minúsculas)
   */
  public normalizeText(text: string): string {
    if (!text) return '';

    return text
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .toLowerCase()
      .trim()
      .replace(/\s+/g, ' '); // Normaliza espaços
  }

  /**
   * Busca todos os estados do Brasil
   */
  public async getStates(): Promise<State[]> {
    try {
      // Usar cache se disponível
      if (this.statesCache) {
        return this.statesCache;
      }

      // Buscar estados do backend (que já implementa cache)
      const response = await fetch('/api/location?country=BR');

      if (!response.ok) {
        throw new Error('Falha ao buscar estados');
      }

      const data = await response.json();

      // Mapear dados do formato do backend para o formato do frontend
      const states: State[] = data.states.map((state: any) => ({
        id: state.code, // Usando o código como ID
        sigla: state.code,
        nome: state.name,
        nomeNormalizado: this.normalizeText(state.name),
      }));

      // Armazenar no cache
      this.statesCache = states;

      return states;
    } catch (error) {
      console.error('Erro ao buscar estados:', error);
      return [];
    }
  }

  /**
   * Busca todas as cidades de um estado
   */
  public async getCitiesByState(stateId: string): Promise<City[]> {
    try {
      // Usar cache se disponível
      if (this.citiesCache[stateId]) {
        return this.citiesCache[stateId];
      }

      // Buscar cidades do backend (que já implementa cache)
      const response = await fetch(`/api/location?country=BR&state=${stateId}`);

      if (!response.ok) {
        throw new Error('Falha ao buscar cidades');
      }

      const data = await response.json();

      // Mapear dados do formato do backend para o formato do frontend
      const cities: City[] = data.cities.map((city: any) => ({
        id: city.id,
        nome: city.name,
        nomeNormalizado: city.normalized_name || this.normalizeText(city.name),
        estadoId: stateId,
      }));

      // Armazenar no cache
      this.citiesCache[stateId] = cities;

      return cities;
    } catch (error) {
      console.error(`Erro ao buscar cidades do estado ${stateId}:`, error);
      return [];
    }
  }

  /**
   * Busca um estado pelo ID
   */
  public async getStateById(stateId: string): Promise<State | null> {
    const states = await this.getStates();
    return states.find((state) => state.id === stateId) || null;
  }

  /**
   * Busca um estado pela sigla
   */
  public async getStateByUF(uf: string): Promise<State | null> {
    const states = await this.getStates();
    return states.find((state) => state.sigla.toLowerCase() === uf.toLowerCase()) || null;
  }

  /**
   * Busca uma cidade pelo ID
   */
  public async getCityById(cityId: string, stateId: string): Promise<City | null> {
    const cities = await this.getCitiesByState(stateId);
    return cities.find((city) => city.id === cityId) || null;
  }

  /**
   * Busca uma cidade pelo nome e estado
   */
  public async getCityByName(cityName: string, stateId: string): Promise<City | null> {
    const cities = await this.getCitiesByState(stateId);
    const normalizedName = this.normalizeText(cityName);

    return cities.find((city) => city.nomeNormalizado === normalizedName) || null;
  }
}

/**
 * Interface para representar um estado
 */
export interface State {
  id: string;
  sigla: string;
  nome: string;
  nomeNormalizado: string;
}

/**
 * Interface para representar uma cidade
 */
export interface City {
  id: string;
  nome: string;
  nomeNormalizado: string;
  estadoId: string;
}
