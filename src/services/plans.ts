// src/services/plans.ts
import { db } from '@/lib/firebase/client';
import { Plan } from '@/types/subscription';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  Timestamp,
  DocumentData
} from 'firebase/firestore';

const plansCollection = 'plans';

// Convert Firestore data to Plan type
const convertPlan = (doc: DocumentData): Plan => {
  const data = doc.data();
  return {
    id: doc.id,
    name: data.name,
    description: data.description,
    price: data.price,
    interval: data.interval,
    moduleAccess: data.modules,
    // Map limits to adLimits for frontend consumption
    // This handles both cases: if adLimits exists directly or if we need to use limits
    adLimits: data.adLimits || data.limits || { services: 0, properties: 0, vehicles: 0, products: 0 },
    features: data.features,
    stripePriceId: data.stripePriceId,
    isPopular: data.popular || false,
    isActive: data.active || false,
    createdAt: data.createdAt ? (data.createdAt as Timestamp).toDate() : new Date(),
    updatedAt: data.updatedAt ? (data.updatedAt as Timestamp).toDate() : new Date(),
  };
};

// Get all active plans
export const getActivePlans = async (): Promise<Plan[]> => {
  const q = query(
    collection(db, plansCollection),
    where('active', '==', true),
    orderBy('price', 'asc')
  );

  const querySnapshot = await getDocs(q);
  return querySnapshot.docs.map(convertPlan);
};

// Get a specific plan by ID
export const getPlanById = async (planId: string): Promise<Plan | null> => {
  const planDoc = await getDoc(doc(db, plansCollection, planId));

  if (!planDoc.exists()) {
    return null;
  }

  return convertPlan(planDoc);
};

// Get a plan by Stripe Price ID
export const getPlanByStripePriceId = async (stripePriceId: string): Promise<Plan | null> => {
  const q = query(
    collection(db, plansCollection),
    where('stripePriceId', '==', stripePriceId),
    where('active', '==', true)
  );

  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    return null;
  }

  return convertPlan(querySnapshot.docs[0]);
};
