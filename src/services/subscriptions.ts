// src/services/subscriptions.ts
import { db } from '@/lib/firebase/client';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { Subscription, SubscriptionStatus } from '@/types/subscription';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  setDoc,
  updateDoc,
  Timestamp,
  DocumentData,
  serverTimestamp,
} from 'firebase/firestore';
import { LogService } from '@/lib/services/log-service';

const subscriptionsCollection = 'subscriptions';

// Convert Firestore data to Subscription type
const convertSubscription = (doc: DocumentData): Subscription => {
  const data = doc.data();
  return {
    id: doc.id,
    userId: data.userId,
    planId: data.planId,
    status: data.status as SubscriptionStatus,
    currentPeriodStart: (data.currentPeriodStart as Timestamp).toDate(),
    currentPeriodEnd: (data.currentPeriodEnd as Timestamp).toDate(),
    cancelAtPeriodEnd: data.cancelAtPeriodEnd,
    stripeSubscriptionId: data.stripeSubscriptionId,
    stripeCustomerId: data.stripeCustomerId,
    createdAt: (data.createdAt as Timestamp).toDate(),
    updatedAt: (data.updatedAt as Timestamp).toDate(),
  };
};

// Get a user's active subscription
export const getUserActiveSubscription = async (userId: string): Promise<Subscription | null> => {
  try {
    // Buscar o documento do usuário
    const userDoc = await getDoc(doc(db, 'users', userId));

    if (!userDoc.exists()) {
      return null;
    }

    const userData = userDoc.data();
    const subscription = userData.subscription;

    // Se não houver assinatura, retornar null
    if (!subscription) {
      return null;
    }

    // Verificar se a assinatura está ativa
    if (!['active', 'trialing'].includes(subscription.status)) {
      return null;
    }

    // Converter timestamps para Date se necessário
    if (
      subscription.currentPeriodStart &&
      typeof subscription.currentPeriodStart.toDate === 'function'
    ) {
      subscription.currentPeriodStart = subscription.currentPeriodStart.toDate();
    }

    if (
      subscription.currentPeriodEnd &&
      typeof subscription.currentPeriodEnd.toDate === 'function'
    ) {
      subscription.currentPeriodEnd = subscription.currentPeriodEnd.toDate();
    }

    if (subscription.createdAt && typeof subscription.createdAt.toDate === 'function') {
      subscription.createdAt = subscription.createdAt.toDate();
    }

    if (subscription.updatedAt && typeof subscription.updatedAt.toDate === 'function') {
      subscription.updatedAt = subscription.updatedAt.toDate();
    }

    return subscription;
  } catch (error) {
    console.error('Error getting active subscription:', error);
    return null;
  }
};

// Get subscription by Stripe subscription ID
export const getSubscriptionByStripeId = async (
  stripeSubscriptionId: string,
): Promise<Subscription | null> => {
  const q = query(
    collection(db, subscriptionsCollection),
    where('stripeSubscriptionId', '==', stripeSubscriptionId),
  );

  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    return null;
  }

  return convertSubscription(querySnapshot.docs[0]);
};

// Create a new subscription
export const createSubscription = async (
  subscriptionData: Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'>,
): Promise<string> => {
  const subscriptionRef = doc(collection(db, subscriptionsCollection));

  await setDoc(subscriptionRef, {
    ...subscriptionData,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
  });

  return subscriptionRef.id;
};

// Update a subscription
export const updateSubscription = async (
  subscriptionId: string,
  data: Partial<Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'>>,
): Promise<void> => {
  const subscriptionRef = doc(db, subscriptionsCollection, subscriptionId);

  await updateDoc(subscriptionRef, {
    ...data,
    updatedAt: serverTimestamp(),
  });
};

// Update subscription from Stripe webhook (server-side)
export const updateSubscriptionFromStripe = async (
  stripeSubscriptionId: string,
  data: Partial<Omit<Subscription, 'id' | 'createdAt' | 'updatedAt'>>,
): Promise<void> => {
  const adminDb = getAdminFirestore();

  const q = query(
    collection(adminDb, subscriptionsCollection),
    where('stripeSubscriptionId', '==', stripeSubscriptionId),
  );

  const querySnapshot = await getDocs(q);

  if (querySnapshot.empty) {
    throw new Error(`Subscription with Stripe ID ${stripeSubscriptionId} not found`);
  }

  const subscriptionRef = doc(adminDb, subscriptionsCollection, querySnapshot.docs[0].id);

  await updateDoc(subscriptionRef, {
    ...data,
    updatedAt: new Date(),
  });
};

/**
 * Verifica se um usuário tem acesso a um recurso com base em sua assinatura
 * @param userId ID do usuário
 * @param resource Nome do recurso (ex: 'services', 'properties', etc.)
 * @returns true se o usuário tem acesso, false caso contrário
 */
export async function hasResourceAccess(userId: string, resource: string): Promise<boolean> {
  try {
    // Obter a assinatura ativa do usuário
    const subscription = await getUserActiveSubscription(userId);

    // Se não tiver assinatura ativa, não tem acesso
    if (!subscription) {
      return false;
    }

    // Verificar se a assinatura tem o plano associado
    if (!subscription.planId) {
      return false;
    }

    // Buscar o plano
    const planDoc = await getDoc(doc(db, 'plans', subscription.planId));

    if (!planDoc.exists()) {
      return false;
    }

    const plan = planDoc.data();

    // Verificar se o plano tem módulos e se o recurso está incluído
    if (plan.modules) {
      // Mapear recursos para módulos
      const resourceToModule: Record<string, string> = {
        services: 'services',
        properties: 'properties',
        vehicles: 'vehicles',
        products: 'products',
      };

      const module = resourceToModule[resource];

      if (module && plan.modules[module]) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking resource access:', error);
    await LogService.logError('check_resource_access_error', error);
    return false;
  }
}

/**
 * Verifica se um usuário atingiu o limite de um recurso com base em sua assinatura
 * @param userId ID do usuário
 * @param resource Nome do recurso (ex: 'services', 'properties', etc.)
 * @param currentCount Contagem atual do recurso
 * @returns Objeto com informações sobre o limite
 */
export async function checkResourceLimit(
  userId: string,
  resource: string,
  currentCount: number = 0,
): Promise<{
  hasAccess: boolean;
  hasReachedLimit: boolean;
  limit?: number;
  remaining?: number;
  planId?: string;
  planName?: string;
}> {
  try {
    // Obter a assinatura ativa do usuário
    const subscription = await getUserActiveSubscription(userId);

    // Se não tiver assinatura ativa, não tem acesso
    if (!subscription) {
      return {
        hasAccess: false,
        hasReachedLimit: true,
      };
    }

    // Verificar se a assinatura tem o plano associado
    if (!subscription.planId) {
      return {
        hasAccess: false,
        hasReachedLimit: true,
      };
    }

    // Buscar o plano
    const planDoc = await getDoc(doc(db, 'plans', subscription.planId));

    if (!planDoc.exists()) {
      return {
        hasAccess: false,
        hasReachedLimit: true,
      };
    }

    const plan = planDoc.data();

    // Verificar se o plano tem módulos e se o recurso está incluído
    if (!plan.modules) {
      return {
        hasAccess: false,
        hasReachedLimit: true,
      };
    }

    // Mapear recursos para módulos
    const resourceToModule: Record<string, string> = {
      services: 'services',
      properties: 'properties',
      vehicles: 'vehicles',
      products: 'products',
    };

    const module = resourceToModule[resource];

    if (!module || !plan.modules[module]) {
      return {
        hasAccess: false,
        hasReachedLimit: true,
      };
    }

    // Verificar se o plano tem limites definidos
    if (!plan.limits || plan.limits[resource] === undefined) {
      return {
        hasAccess: true,
        hasReachedLimit: false,
        limit: Infinity,
        remaining: Infinity,
        planId: subscription.planId,
        planName: plan.name,
      };
    }

    const limit = plan.limits[resource];
    const remaining = limit - currentCount;
    const hasReachedLimit = currentCount >= limit;

    return {
      hasAccess: true,
      hasReachedLimit,
      limit,
      remaining,
      planId: subscription.planId,
      planName: plan.name,
    };
  } catch (error) {
    console.error('Error checking resource limit:', error);
    await LogService.logError('check_resource_limit_error', error);

    // Em caso de erro, assumir que atingiu o limite por segurança
    return {
      hasAccess: false,
      hasReachedLimit: true,
    };
  }
}

/**
 * Conta quantos recursos um usuário tem
 * @param userId ID do usuário
 * @param resource Nome do recurso (ex: 'services', 'properties', etc.)
 * @returns Número de recursos
 */
export async function countUserResources(userId: string, resource: string): Promise<number> {
  try {
    // Mapear recursos para coleções
    const resourceToCollection: Record<string, string> = {
      services: 'services',
      properties: 'properties',
      vehicles: 'vehicles',
      products: 'products',
    };

    const collectionName = resourceToCollection[resource];

    if (!collectionName) {
      throw new Error(`Recurso inválido: ${resource}`);
    }

    // Contar documentos na coleção que pertencem ao usuário
    const q = query(
      collection(db, collectionName),
      where('advertiserId', '==', userId),
      where('status', 'in', ['published', 'draft', 'pending_review']),
    );

    const querySnapshot = await getDocs(q);

    return querySnapshot.size;
  } catch (error) {
    console.error('Error counting user resources:', error);
    await LogService.logError('count_user_resources_error', error);
    return 0;
  }
}
