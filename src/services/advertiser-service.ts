'use client';

import { db } from '@/lib/firebase/client';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  DocumentReference,
  DocumentData,
} from 'firebase/firestore';
import { Advertiser } from '@/types/models/advertiser';
import { v4 as uuidv4 } from 'uuid';
import { LocationService } from './location-service';

export class AdvertiserService {
  private collection = 'advertisers';
  private locationService = LocationService.getInstance();

  /**
   * Obter um anunciante pelo ID
   */
  async getById(id: string): Promise<Advertiser | null> {
    try {
      const docRef = doc(db, this.collection, id);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      return {
        id: docSnap.id,
        ...docSnap.data(),
        createdAt: docSnap.data().createdAt?.toDate(),
        updatedAt: docSnap.data().updatedAt?.toDate(),
        verificationDate: docSnap.data().verificationDate?.toDate(),
      } as Advertiser;
    } catch (error) {
      console.error('Error getting advertiser:', error);
      throw error;
    }
  }

  /**
   * Obter um anunciante pelo ID do usuário
   */
  async getByUserId(userId: string): Promise<Advertiser | null> {
    try {
      const q = query(collection(db, this.collection), where('userId', '==', userId), limit(1));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return null;
      }

      const docSnap = querySnapshot.docs[0];
      return {
        id: docSnap.id,
        ...docSnap.data(),
        createdAt: docSnap.data().createdAt?.toDate(),
        updatedAt: docSnap.data().updatedAt?.toDate(),
        verificationDate: docSnap.data().verificationDate?.toDate(),
      } as Advertiser;
    } catch (error) {
      console.error('Error getting advertiser by user ID:', error);
      throw error;
    }
  }

  /**
   * Normalizar dados de localização
   */
  private async normalizeLocationData(data: Partial<Advertiser>): Promise<Partial<Advertiser>> {
    // Se não houver dados de endereço, retornar os dados originais
    if (!data.address) return data;

    // Clonar os dados para não modificar o objeto original
    const normalizedData = { ...data };

    // Se tiver cidade mas não tiver cityNormalized, normalizar
    if (normalizedData.address.city && !normalizedData.address.cityNormalized) {
      normalizedData.address.cityNormalized = this.locationService.normalizeText(
        normalizedData.address.city,
      );
    }

    return normalizedData;
  }

  /**
   * Criar um novo anunciante
   */
  async create(data: Partial<Advertiser>, userId: string): Promise<Advertiser> {
    try {
      const id = uuidv4();
      const docRef = doc(db, this.collection, id);

      // Normalizar dados de localização
      const normalizedData = await this.normalizeLocationData(data);

      const advertiserData = {
        ...normalizedData,
        userId,
        id,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        createdBy: userId,
      };

      await setDoc(docRef, advertiserData);

      return {
        ...advertiserData,
        id,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as Advertiser;
    } catch (error) {
      console.error('Error creating advertiser:', error);
      throw error;
    }
  }

  /**
   * Atualizar um anunciante existente
   */
  async update(id: string, data: Partial<Advertiser>): Promise<Advertiser> {
    try {
      const docRef = doc(db, this.collection, id);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        throw new Error('Advertiser not found');
      }

      // Normalizar dados de localização
      const normalizedData = await this.normalizeLocationData(data);

      const updateData = {
        ...normalizedData,
        updatedAt: serverTimestamp(),
      };

      await updateDoc(docRef, updateData);

      return {
        id,
        ...docSnap.data(),
        ...data,
        updatedAt: new Date(),
      } as Advertiser;
    } catch (error) {
      console.error('Error updating advertiser:', error);
      throw error;
    }
  }

  /**
   * Verificar se um usuário já é anunciante
   */
  async isAdvertiser(userId: string): Promise<boolean> {
    try {
      const advertiser = await this.getByUserId(userId);
      return advertiser !== null;
    } catch (error) {
      console.error('Error checking if user is advertiser:', error);
      throw error;
    }
  }

  /**
   * Obter ou criar um anunciante para um usuário
   */
  async getOrCreate(userId: string, userData: any): Promise<Advertiser> {
    try {
      const advertiser = await this.getByUserId(userId);

      if (advertiser) {
        return advertiser;
      }

      // Criar um novo anunciante com dados básicos do usuário
      return this.create(
        {
          name: userData.name || '',
          email: userData.email || '',
          photo: userData.image || '',
          address: {
            street: '',
            neighborhood: '',
            city: '',
            state: '',
            zipCode: '',
            country: 'Brasil',
          },
          showExactLocation: false,
          serviceLocationType: 'fixed',
          privacySettings: {
            showPhone: false,
            showEmail: true,
            showAddress: false,
          },
        },
        userId,
      );
    } catch (error) {
      console.error('Error getting or creating advertiser:', error);
      throw error;
    }
  }
}
