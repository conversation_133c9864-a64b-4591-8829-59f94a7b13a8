// src/lib/stripe.ts
// IMPORTANTE: Este arquivo deve ser usado APENAS no servidor

import Stripe from 'stripe';
import { LogService } from '@/lib/services/log-service';

// Verificar se estamos no lado do servidor
if (typeof window !== 'undefined') {
  console.error('ERRO DE SEGURANÇA: O módulo Stripe está sendo importado no cliente!');
}

// Implementar inicialização preguiçosa (lazy initialization) para o cliente Stripe
let _stripe: Stripe | null = null;

export const getStripe = (): Stripe => {
  try {
    if (!_stripe) {
      const stripeKey = process.env.STRIPE_SECRET_KEY;

      if (!stripeKey) {
        throw new Error('Stripe API key not found in environment variables');
      }

      // Log para debug
      LogService.logToFile(
        'stripe_init',
        {
          keyExists: !!stripeKey,
          keyLength: stripeKey.length,
        },
        'debug',
      ).catch(console.error);

      _stripe = new Stripe(stripeKey, {
        apiVersion: '2025-03-31.basil', // Use the latest API version
      });

      // Log para confirmar inicialização
      LogService.logToFile('stripe_initialized', { success: true }, 'debug').catch(console.error);
    }
    return _stripe;
  } catch (error) {
    // Log do erro
    LogService.logError('stripe_init_error', error).catch(console.error);
    console.error('Erro ao inicializar Stripe:', error);
    throw error;
  }
};

// Para compatibilidade com o código existente
// Usamos uma função getter para inicializar o Stripe apenas quando necessário
export const stripe = {
  get checkout() {
    return getStripe().checkout;
  },
  get customers() {
    return getStripe().customers;
  },
  get subscriptions() {
    return getStripe().subscriptions;
  },
  get billingPortal() {
    return getStripe().billingPortal;
  },
  get webhooks() {
    return getStripe().webhooks;
  },
  // Adicione outros serviços do Stripe conforme necessário
};

// Create a Stripe Checkout Session for subscription
export const createCheckoutSession = async ({
  customerId,
  priceId,
  successUrl,
  cancelUrl,
  metadata = {},
}: {
  customerId?: string;
  priceId: string;
  successUrl: string;
  cancelUrl: string;
  metadata?: Record<string, string>;
}) => {
  const session = await stripe.checkout.sessions.create({
    customer: customerId,
    payment_method_types: ['card', 'pix'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    mode: 'subscription',
    success_url: successUrl,
    cancel_url: cancelUrl,
    metadata,
  });

  return session;
};

// Create a Stripe Customer
export const createCustomer = async ({
  email,
  name,
  metadata = {},
}: {
  email: string;
  name?: string;
  metadata?: Record<string, string>;
}) => {
  const customer = await stripe.customers.create({
    email,
    name,
    metadata,
  });

  return customer;
};

// Get a Stripe Subscription
export const getSubscription = async (subscriptionId: string) => {
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  return subscription;
};

// Cancel a Stripe Subscription
export const cancelSubscription = async (subscriptionId: string) => {
  const subscription = await stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: true,
  });
  return subscription;
};

// Resume a canceled Stripe Subscription
export const resumeSubscription = async (subscriptionId: string) => {
  const subscription = await stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: false,
  });
  return subscription;
};

// Create a Stripe Portal Session for managing subscriptions
export const createPortalSession = async ({
  customerId,
  returnUrl,
}: {
  customerId: string;
  returnUrl: string;
}) => {
  const session = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  });

  return session;
};
