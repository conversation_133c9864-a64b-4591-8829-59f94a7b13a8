// src/lib/admin/init-admin.ts
import { auth, db } from '@/lib/firebase/client';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  updateProfile,
  signOut,
} from 'firebase/auth';
import {
  doc,
  setDoc,
  serverTimestamp,
  collection,
  query,
  where,
  getDocs,
} from 'firebase/firestore';

/**
 * Verifica se o usuário administrador existe e o cria se necessário
 * Usa apenas o Firebase Client SDK para evitar problemas de permissão
 */
export async function initializeAdmin() {
  try {
    console.log('Initializing admin...');

    const adminEmail = process.env.NEXT_PUBLIC_ADMIN_EMAIL;
    const adminPassword = process.env.NEXT_PUBLIC_ADMIN_PASSWORD;

    if (!adminEmail || !adminPassword) {
      console.warn('Admin credentials not found in environment variables');
      return;
    }

    // Verificar se o usuário admin já existe no Firestore
    try {
      console.log('Checking if admin user exists...');

      // Verificar se já existe um usuário com o email do admin e role admin
      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('email', '==', adminEmail), where('role', '==', 'admin'));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        console.log('Admin user already exists in Firestore');
        return;
      }

      // Salvar o usuário atual se houver
      const currentUser = auth.currentUser;

      // Tentar fazer login com as credenciais do admin
      try {
        console.log('Trying to sign in as admin...');
        const userCredential = await signInWithEmailAndPassword(auth, adminEmail, adminPassword);

        const user = userCredential.user;
        console.log('Admin user exists in Authentication, creating Firestore record...');

        // Criar documento do usuário admin no Firestore
        await setDoc(doc(db, 'users', user.uid), {
          email: adminEmail,
          name: user.displayName || 'Administrador',
          role: 'admin',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });

        console.log('Admin user record created in Firestore');
      } catch (loginError) {
        // Usuário não existe, criar novo
        console.log('Admin user does not exist, creating new user...');

        try {
          const userCredential = await createUserWithEmailAndPassword(
            auth,
            adminEmail,
            adminPassword,
          );

          const user = userCredential.user;
          console.log('New admin user created:', user.uid);

          // Atualizar o perfil do usuário
          await updateProfile(user, {
            displayName: 'Administrador',
          });

          // Criar documento do usuário admin no Firestore
          await setDoc(doc(db, 'users', user.uid), {
            email: adminEmail,
            name: 'Administrador',
            role: 'admin',
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });

          console.log('Admin user created successfully');
        } catch (createError) {
          console.error('Error creating admin user:', createError);
        }
      } finally {
        // Fazer logout do admin e restaurar o usuário original
        await signOut(auth);

        // Se havia um usuário logado antes, precisaríamos fazer login novamente
        // Mas isso exigiria armazenar a senha, o que não é seguro
        // Então apenas deixamos o usuário deslogado
      }
    } catch (error) {
      console.error('Error checking admin user:', error);
    }
  } catch (error) {
    console.error('Error initializing admin:', error);
    throw error;
  }
}
