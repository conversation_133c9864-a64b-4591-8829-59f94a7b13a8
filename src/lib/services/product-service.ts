// src/lib/services/product-service.ts
import { BaseService, ListOptions, PaginatedResult } from './base-service';
import { Product } from '@/types/models/product';
import { PublishStatus } from '@/types/models/base';
import { db } from '@/lib/firebase/client';
import { 
  collection, 
  query, 
  where, 
  getDocs,
  orderBy,
  limit,
  increment,
  doc,
  updateDoc
} from 'firebase/firestore';

/**
 * Serviço para operações com produtos
 */
export class ProductService extends BaseService<Product> {
  constructor() {
    super('products');
  }
  
  /**
   * Lista produtos publicados
   */
  async listPublished(options?: Omit<ListOptions, 'filters'>): Promise<PaginatedResult<Product>> {
    const filters = [
      { field: 'status', operator: '==', value: 'published' as PublishStatus }
    ];
    
    return this.list({
      ...options,
      filters,
      orderByField: options?.orderByField || 'publishedAt',
      orderByDirection: options?.orderByDirection || 'desc'
    });
  }
  
  /**
   * Lista produtos por categoria
   */
  async listByCategory(categoryId: string, options?: Omit<ListOptions, 'filters'>): Promise<PaginatedResult<Product>> {
    const filters = [
      { field: 'status', operator: '==', value: 'published' as PublishStatus },
      { field: 'categoryId', operator: '==', value: categoryId }
    ];
    
    return this.list({
      ...options,
      filters,
      orderByField: options?.orderByField || 'publishedAt',
      orderByDirection: options?.orderByDirection || 'desc'
    });
  }
  
  /**
   * Lista produtos por anunciante
   */
  async listByAdvertiser(advertiserId: string, options?: Omit<ListOptions, 'filters'>): Promise<PaginatedResult<Product>> {
    const filters = [
      { field: 'advertiserId', operator: '==', value: advertiserId }
    ];
    
    return this.list({
      ...options,
      filters,
      orderByField: options?.orderByField || 'createdAt',
      orderByDirection: options?.orderByDirection || 'desc'
    });
  }
  
  /**
   * Busca produtos por texto
   */
  async search(searchText: string, options?: Omit<ListOptions, 'filters'>): Promise<PaginatedResult<Product>> {
    // Nota: Firestore não suporta busca de texto completo nativamente
    // Para uma implementação real, seria necessário usar Algolia, Elasticsearch ou similar
    // Esta é uma implementação simplificada que busca apenas no título e descrição
    
    // Converter para minúsculas para busca case-insensitive
    const searchLower = searchText.toLowerCase();
    
    // Buscar todos os produtos publicados
    const result = await this.listPublished(options);
    
    // Filtrar localmente
    const filteredItems = result.items.filter(product => 
      product.title.toLowerCase().includes(searchLower) || 
      product.description.toLowerCase().includes(searchLower) ||
      (product.brand && product.brand.toLowerCase().includes(searchLower)) ||
      (product.model && product.model.toLowerCase().includes(searchLower)) ||
      (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchLower)))
    );
    
    return {
      items: filteredItems,
      lastDoc: result.lastDoc,
      hasMore: result.hasMore
    };
  }
  
  /**
   * Incrementa o contador de visualizações
   */
  async incrementViews(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.collectionName, id);
      await updateDoc(docRef, {
        views: increment(1)
      });
    } catch (error) {
      console.error('Error incrementing views:', error);
      throw error;
    }
  }
  
  /**
   * Publica um produto
   */
  async publish(id: string): Promise<Product> {
    return this.update(id, {
      status: 'published',
      publishedAt: new Date()
    } as Partial<Product>);
  }
  
  /**
   * Arquiva um produto
   */
  async archive(id: string): Promise<Product> {
    return this.update(id, {
      status: 'archived'
    } as Partial<Product>);
  }
  
  /**
   * Reporta um produto
   */
  async report(id: string, reason: string): Promise<Product> {
    const product = await this.getById(id);
    
    if (!product) {
      throw new Error(`Product with ID ${id} not found`);
    }
    
    const reports = product.reports || 0;
    const reportReasons = product.reportReasons || [];
    
    return this.update(id, {
      reports: reports + 1,
      reportReasons: [...reportReasons, reason]
    } as Partial<Product>);
  }
  
  /**
   * Adiciona uma avaliação a um produto
   */
  async addReview(
    id: string, 
    userId: string, 
    userName: string, 
    rating: number, 
    comment: string
  ): Promise<Product> {
    const product = await this.getById(id);
    
    if (!product) {
      throw new Error(`Product with ID ${id} not found`);
    }
    
    const reviews = product.reviews || [];
    const newReview = {
      userId,
      userName,
      rating,
      comment,
      createdAt: new Date()
    };
    
    // Calcular nova média de avaliações
    const totalRatings = (product.ratingCount || 0) + 1;
    const currentTotalPoints = (product.rating || 0) * (product.ratingCount || 0);
    const newTotalPoints = currentTotalPoints + rating;
    const newRating = newTotalPoints / totalRatings;
    
    return this.update(id, {
      reviews: [...reviews, newReview],
      rating: newRating,
      ratingCount: totalRatings
    } as Partial<Product>);
  }
}
