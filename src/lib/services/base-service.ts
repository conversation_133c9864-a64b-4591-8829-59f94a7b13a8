import { db } from '@/lib/firebase/client';
import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter,
  DocumentData,
  QueryConstraint,
  serverTimestamp,
  Timestamp,
  DocumentSnapshot,
  QuerySnapshot
} from 'firebase/firestore';
import { BaseModel } from '@/types/models/base';
import { getAuth } from 'firebase/auth';

/**
 * Converte um documento do Firestore para um modelo tipado
 */
export function convertFromFirestore<T>(doc: DocumentData): T {
  const data = doc.data();
  
  // Converter timestamps para Date
  const converted: any = {
    ...data,
    id: doc.id,
  };
  
  // Converter timestamps para Date
  Object.keys(converted).forEach(key => {
    if (converted[key] instanceof Timestamp) {
      converted[key] = converted[key].toDate();
    }
  });
  
  return converted as T;
}

/**
 * Prepara um objeto para ser salvo no Firestore
 */
export function prepareForFirestore<T>(data: Partial<T>): any {
  const prepared = { ...data };
  
  // Remover o id se existir (será usado como id do documento)
  if ('id' in prepared) {
    delete prepared.id;
  }
  
  return prepared;
}

/**
 * Opções para listar documentos
 */
export interface ListOptions {
  filters?: {
    field: string;
    operator: '==' | '!=' | '>' | '>=' | '<' | '<=';
    value: any;
  }[];
  orderByField?: string;
  orderByDirection?: 'asc' | 'desc';
  limit?: number;
  startAfter?: DocumentSnapshot;
}

/**
 * Resultado paginado
 */
export interface PaginatedResult<T> {
  items: T[];
  lastDoc?: DocumentSnapshot;
  hasMore: boolean;
}

/**
 * Serviço base para operações CRUD
 */
export class BaseService<T extends BaseModel> {
  protected collectionName: string;
  
  constructor(collectionName: string) {
    this.collectionName = collectionName;
  }
  
  /**
   * Obtém um documento pelo ID
   */
  async getById(id: string): Promise<T | null> {
    try {
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return convertFromFirestore<T>(docSnap);
      }
      
      return null;
    } catch (error) {
      console.error(`Error getting ${this.collectionName} by ID:`, error);
      throw error;
    }
  }
  
  /**
   * Lista documentos com opções de filtro e paginação
   */
  async list(options?: ListOptions): Promise<PaginatedResult<T>> {
    try {
      const constraints: QueryConstraint[] = [];
      
      // Adicionar filtros
      if (options?.filters) {
        options.filters.forEach(filter => {
          constraints.push(where(filter.field, filter.operator, filter.value));
        });
      }
      
      // Adicionar ordenação
      if (options?.orderByField) {
        constraints.push(orderBy(options.orderByField, options.orderByDirection || 'asc'));
      }
      
      // Adicionar limite
      if (options?.limit) {
        constraints.push(limit(options.limit));
      }
      
      // Adicionar paginação
      if (options?.startAfter) {
        constraints.push(startAfter(options.startAfter));
      }
      
      // Criar query
      const q = query(collection(db, this.collectionName), ...constraints);
      
      // Executar query
      const querySnapshot = await getDocs(q);
      
      // Converter documentos
      const items: T[] = [];
      querySnapshot.forEach(doc => {
        items.push(convertFromFirestore<T>(doc));
      });
      
      // Verificar se há mais documentos
      const hasMore = !options?.limit ? false : querySnapshot.docs.length === options.limit;
      
      // Retornar resultado paginado
      return {
        items,
        lastDoc: querySnapshot.docs[querySnapshot.docs.length - 1],
        hasMore
      };
    } catch (error) {
      console.error(`Error listing ${this.collectionName}:`, error);
      throw error;
    }
  }
  
  /**
   * Cria um novo documento
   */
  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T> {
    try {
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (!user) {
        throw new Error('User not authenticated');
      }
      
      // Criar referência para o novo documento
      const newDocRef = doc(collection(db, this.collectionName));
      
      // Preparar dados para salvar
      const preparedData = prepareForFirestore(data);
      
      // Adicionar campos de auditoria
      const timestamp = serverTimestamp();
      const docData = {
        ...preparedData,
        createdAt: timestamp,
        updatedAt: timestamp,
        createdBy: user.uid
      };
      
      // Salvar documento
      await setDoc(newDocRef, docData);
      
      // Buscar o documento recém-criado para retornar com os campos gerados
      const newDocSnap = await getDoc(newDocRef);
      
      return convertFromFirestore<T>(newDocSnap);
    } catch (error) {
      console.error(`Error creating ${this.collectionName}:`, error);
      throw error;
    }
  }
  
  /**
   * Atualiza um documento existente
   */
  async update(id: string, data: Partial<T>): Promise<T> {
    try {
      const docRef = doc(db, this.collectionName, id);
      
      // Verificar se o documento existe
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        throw new Error(`${this.collectionName} with ID ${id} not found`);
      }
      
      // Preparar dados para atualizar
      const preparedData = prepareForFirestore(data);
      
      // Adicionar campo de atualização
      const updateData = {
        ...preparedData,
        updatedAt: serverTimestamp()
      };
      
      // Atualizar documento
      await updateDoc(docRef, updateData);
      
      // Buscar o documento atualizado para retornar
      const updatedDocSnap = await getDoc(docRef);
      
      return convertFromFirestore<T>(updatedDocSnap);
    } catch (error) {
      console.error(`Error updating ${this.collectionName}:`, error);
      throw error;
    }
  }
  
  /**
   * Exclui um documento
   */
  async delete(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.collectionName, id);
      
      // Verificar se o documento existe
      const docSnap = await getDoc(docRef);
      
      if (!docSnap.exists()) {
        throw new Error(`${this.collectionName} with ID ${id} not found`);
      }
      
      // Excluir documento
      await deleteDoc(docRef);
    } catch (error) {
      console.error(`Error deleting ${this.collectionName}:`, error);
      throw error;
    }
  }
  
  /**
   * Busca documentos por um campo específico
   */
  async findByField(field: string, value: any): Promise<T[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where(field, '==', value)
      );
      
      const querySnapshot = await getDocs(q);
      
      const items: T[] = [];
      querySnapshot.forEach(doc => {
        items.push(convertFromFirestore<T>(doc));
      });
      
      return items;
    } catch (error) {
      console.error(`Error finding ${this.collectionName} by field:`, error);
      throw error;
    }
  }
}
