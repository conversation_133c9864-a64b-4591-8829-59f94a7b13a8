import { BaseService } from './base-service';
import { ServiceCategory } from '@/types/models/service';
import { db } from '@/lib/firebase/client';
import { 
  collection, 
  query, 
  where, 
  getDocs,
  orderBy
} from 'firebase/firestore';

/**
 * Serviço para operações com categorias de serviços
 */
export class ServiceCategoryService extends BaseService<ServiceCategory> {
  constructor() {
    super('serviceCategories');
  }
  
  /**
   * Lista todas as categorias principais (sem parentId)
   */
  async listMainCategories(): Promise<ServiceCategory[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('parentId', '==', null),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      
      const categories: ServiceCategory[] = [];
      querySnapshot.forEach(doc => {
        const category = {
          id: doc.id,
          ...doc.data()
        } as ServiceCategory;
        
        categories.push(category);
      });
      
      return categories;
    } catch (error) {
      console.error('Error listing main categories:', error);
      throw error;
    }
  }
  
  /**
   * Lista subcategorias de uma categoria
   */
  async listSubcategories(parentId: string): Promise<ServiceCategory[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('parentId', '==', parentId),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      
      const subcategories: ServiceCategory[] = [];
      querySnapshot.forEach(doc => {
        const subcategory = {
          id: doc.id,
          ...doc.data()
        } as ServiceCategory;
        
        subcategories.push(subcategory);
      });
      
      return subcategories;
    } catch (error) {
      console.error(`Error listing subcategories for parent ${parentId}:`, error);
      throw error;
    }
  }
  
  /**
   * Obtém uma categoria com suas subcategorias
   */
  async getCategoryWithSubcategories(categoryId: string): Promise<{
    category: ServiceCategory | null;
    subcategories: ServiceCategory[];
  }> {
    try {
      const category = await this.getById(categoryId);
      
      if (!category) {
        return {
          category: null,
          subcategories: []
        };
      }
      
      const subcategories = await this.listSubcategories(categoryId);
      
      return {
        category,
        subcategories
      };
    } catch (error) {
      console.error(`Error getting category with subcategories for ${categoryId}:`, error);
      throw error;
    }
  }
  
  /**
   * Obtém todas as categorias organizadas em uma estrutura hierárquica
   */
  async getCategoryTree(): Promise<{
    mainCategories: ServiceCategory[];
    subcategories: { [parentId: string]: ServiceCategory[] };
  }> {
    try {
      const mainCategories = await this.listMainCategories();
      const subcategories: { [parentId: string]: ServiceCategory[] } = {};
      
      // Para cada categoria principal, buscar suas subcategorias
      for (const category of mainCategories) {
        const subs = await this.listSubcategories(category.id);
        subcategories[category.id] = subs;
      }
      
      return {
        mainCategories,
        subcategories
      };
    } catch (error) {
      console.error('Error getting category tree:', error);
      throw error;
    }
  }
}
