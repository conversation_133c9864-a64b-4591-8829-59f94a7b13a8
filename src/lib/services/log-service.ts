// src/lib/services/log-service.ts

/**
 * Serviço para registrar logs no servidor via API
 */
export class LogService {
  /**
   * Envia um log para o servidor
   * @param type Tipo de log (debug, error, request)
   * @param name Nome do log
   * @param data Dados a serem registrados
   */
  private static async sendLog(type: string, name: string, data: any): Promise<string> {
    try {
      // Registrar no console local primeiro
      console.log(`[${type.toUpperCase()}] ${name}:`, data);

      // Enviar para o endpoint de logs
      // No lado do servidor, não podemos usar caminhos relativos
      // Verificar se estamos no lado do cliente
      if (typeof window !== 'undefined') {
        try {
          const response = await fetch('/api/logs', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type,
              name,
              data,
            }),
          });

          if (!response.ok) {
            console.error(`Erro ao enviar log para o servidor: ${response.status}`);
          }
        } catch (fetchError) {
          console.error('Erro ao enviar log para o servidor:', fetchError);
        }
      }

      // Já tratamos o erro acima

      return name;
    } catch (error) {
      // Se falhar o envio para o servidor, pelo menos registramos no console
      console.error('Erro ao enviar log para o servidor:', error);
      return name;
    }
  }

  /**
   * Registra um objeto de debug
   * @param filename Nome do log
   * @param data Dados a serem registrados
   * @param prefix Prefixo opcional para o log
   */
  public static async logToFile(filename: string, data: any, prefix: string = ''): Promise<string> {
    const type = prefix || 'debug';
    return this.sendLog(type, filename, data);
  }

  /**
   * Registra um erro
   * @param filename Nome do log
   * @param error Erro a ser registrado
   * @param context Contexto adicional opcional
   */
  public static async logError(
    filename: string,
    error: any,
    context: Record<string, any> = {},
  ): Promise<string> {
    const errorData = {
      timestamp: new Date().toISOString(),
      error: {
        message: error.message || 'Erro desconhecido',
        stack: error.stack,
        name: error.name,
        code: error.code,
      },
      context,
    };

    return this.sendLog('error', filename, errorData);
  }

  /**
   * Registra uma requisição e resposta
   * @param filename Nome do log
   * @param request Dados da requisição
   * @param response Dados da resposta
   */
  public static async logRequest(filename: string, request: any, response: any): Promise<string> {
    const requestData = {
      timestamp: new Date().toISOString(),
      request,
      response,
    };

    return this.sendLog('request', filename, requestData);
  }
}
