// src/lib/services/product-category-service.ts
import { BaseService } from './base-service';
import { ProductCategory } from '@/types/models/product';
import { db } from '@/lib/firebase/client';
import { 
  collection, 
  query, 
  where, 
  getDocs,
  orderBy
} from 'firebase/firestore';

/**
 * Serviço para operações com categorias de produtos
 */
export class ProductCategoryService extends BaseService<ProductCategory> {
  constructor() {
    super('productCategories');
  }
  
  /**
   * Lista todas as categorias principais (sem parentId)
   */
  async listMainCategories(): Promise<ProductCategory[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('parentId', '==', null),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      
      const categories: ProductCategory[] = [];
      querySnapshot.forEach(doc => {
        const category = {
          id: doc.id,
          ...doc.data()
        } as ProductCategory;
        
        categories.push(category);
      });
      
      return categories;
    } catch (error) {
      console.error('Error listing main categories:', error);
      throw error;
    }
  }
  
  /**
   * Lista subcategorias de uma categoria
   */
  async listSubcategories(parentId: string): Promise<ProductCategory[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('parentId', '==', parentId),
        orderBy('name')
      );
      
      const querySnapshot = await getDocs(q);
      
      const subcategories: ProductCategory[] = [];
      querySnapshot.forEach(doc => {
        const subcategory = {
          id: doc.id,
          ...doc.data()
        } as ProductCategory;
        
        subcategories.push(subcategory);
      });
      
      return subcategories;
    } catch (error) {
      console.error('Error listing subcategories:', error);
      throw error;
    }
  }
}
