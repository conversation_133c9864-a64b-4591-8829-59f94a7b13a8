// src/lib/services/plan-service.ts
import { db } from '@/lib/firebase/client';
import { collection, doc, getDoc, getDocs, query, where, orderBy } from 'firebase/firestore';
import { Plan, PlanFormValues } from '@/types/models/plan';
import { LogService } from '@/lib/services/log-service';

export class PlanService {
  private collectionName = 'plans';

  /**
   * Cria um novo plano via API
   */
  async create(planData: PlanFormValues): Promise<Plan> {
    try {
      // Log dos dados recebidos
      await LogService.logToFile('plan_create_input', planData, 'debug');

      // Enviar para a API
      const response = await fetch('/api/admin/plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(planData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        await LogService.logError('plan_create_api_error', errorData);
        throw new Error(errorData.error || 'Erro ao criar plano');
      }

      const plan = await response.json();
      await LogService.logToFile('plan_create_api_success', plan, 'debug');

      return plan;
    } catch (error) {
      await LogService.logError('plan_create_error', error);
      console.error('Erro ao criar plano:', error);
      throw error;
    }
  }

  /**
   * Atualiza um plano existente via API
   */
  async update(id: string, planData: PlanFormValues): Promise<Plan> {
    try {
      // Log dos dados recebidos
      await LogService.logToFile('plan_update_input', { id, planData }, 'debug');

      // Enviar para a API
      const response = await fetch(`/api/admin/plans/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(planData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        await LogService.logError('plan_update_api_error', errorData);
        throw new Error(errorData.error || 'Erro ao atualizar plano');
      }

      const plan = await response.json();
      await LogService.logToFile('plan_update_api_success', plan, 'debug');

      return plan;
    } catch (error) {
      await LogService.logError('plan_update_error', error);
      console.error('Erro ao atualizar plano:', error);
      throw error;
    }
  }

  /**
   * Exclui um plano via API
   */
  async delete(id: string): Promise<void> {
    try {
      // Enviar para a API
      const response = await fetch(`/api/admin/plans/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        await LogService.logError('plan_delete_api_error', errorData);
        throw new Error(errorData.error || 'Erro ao excluir plano');
      }

      await LogService.logToFile('plan_delete_api_success', { id }, 'debug');
    } catch (error) {
      await LogService.logError('plan_delete_error', error);
      console.error('Erro ao excluir plano:', error);
      throw error;
    }
  }

  /**
   * Busca um plano pelo ID
   */
  async getById(id: string): Promise<Plan | null> {
    try {
      // Usar o Firestore diretamente para evitar problemas com URLs relativas
      const planRef = doc(db, this.collectionName, id);
      const planDoc = await getDoc(planRef);

      if (!planDoc.exists()) {
        return null;
      }

      const plan = {
        id: planDoc.id,
        ...planDoc.data(),
      } as Plan;

      return JSON.parse(JSON.stringify(plan));
    } catch (error) {
      console.error('Erro ao buscar plano:', error);
      return null;
    }
  }

  /**
   * Lista todos os planos
   */
  async getAll(): Promise<Plan[]> {
    try {
      const q = query(collection(db, this.collectionName), orderBy('order', 'asc'));

      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Plan[];
    } catch (error) {
      console.error('Erro ao listar planos:', error);
      throw error;
    }
  }

  /**
   * Lista apenas os planos ativos
   */
  async getActive(): Promise<Plan[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('active', '==', true),
        orderBy('order', 'asc'),
      );

      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Plan[];
    } catch (error) {
      console.error('Erro ao listar planos ativos:', error);
      throw error;
    }
  }

  /**
   * Lista planos por intervalo (mensal ou anual)
   */
  async getByInterval(interval: 'month' | 'year'): Promise<Plan[]> {
    try {
      const q = query(
        collection(db, this.collectionName),
        where('interval', '==', interval),
        where('active', '==', true),
        orderBy('order', 'asc'),
      );

      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Plan[];
    } catch (error) {
      console.error(`Erro ao listar planos ${interval}:`, error);
      throw error;
    }
  }

  /**
   * Clona um plano existente
   */
  async clone(id: string): Promise<Plan> {
    try {
      // 1. Buscar o plano original
      const originalPlan = await this.getById(id);

      if (!originalPlan) {
        throw new Error('Plano não encontrado');
      }

      // 2. Preparar os dados para o novo plano
      const newPlanData: PlanFormValues = {
        name: `${originalPlan.name} (Cópia)`,
        description: originalPlan.description,
        price: originalPlan.price / 100, // Converter de centavos para reais para o formulário
        interval: originalPlan.interval,
        features: originalPlan.features,
        popular: false, // A cópia não deve ser marcada como popular por padrão
        modules: originalPlan.modules,
        limits: originalPlan.limits,
        active: false, // A cópia é criada como inativa por padrão
        order: originalPlan?.order ?? 0 + 1, // Incrementar a ordem
      };

      // 3. Criar o novo plano
      await LogService.logToFile('plan_clone_input', { originalId: id, newPlanData }, 'debug');

      // 4. Chamar o método create para criar o novo plano
      return await this.create(newPlanData);
    } catch (error) {
      await LogService.logError('plan_clone_error', error);
      console.error('Erro ao clonar plano:', error);
      throw error;
    }
  }
}
