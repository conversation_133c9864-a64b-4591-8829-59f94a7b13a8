import { BaseService, ListOptions, PaginatedResult } from './base-service';
import { Service } from '@/types/models/service';
import { PublishStatus } from '@/types/models/base';
import { db } from '@/lib/firebase/client';
import { 
  collection, 
  query, 
  where, 
  getDocs,
  orderBy,
  limit,
  increment,
  doc,
  updateDoc
} from 'firebase/firestore';

/**
 * Serviço para operações com serviços
 */
export class ServiceService extends BaseService<Service> {
  constructor() {
    super('services');
  }
  
  /**
   * Lista serviços publicados
   */
  async listPublished(options?: Omit<ListOptions, 'filters'>): Promise<PaginatedResult<Service>> {
    const filters = [
      { field: 'status', operator: '==', value: 'published' as PublishStatus }
    ];
    
    return this.list({
      ...options,
      filters,
      orderByField: options?.orderByField || 'publishedAt',
      orderByDirection: options?.orderByDirection || 'desc'
    });
  }
  
  /**
   * Lista serviços por categoria
   */
  async listByCategory(categoryId: string, options?: Omit<ListOptions, 'filters'>): Promise<PaginatedResult<Service>> {
    const filters = [
      { field: 'status', operator: '==', value: 'published' as PublishStatus },
      { field: 'categoryId', operator: '==', value: categoryId }
    ];
    
    return this.list({
      ...options,
      filters,
      orderByField: options?.orderByField || 'publishedAt',
      orderByDirection: options?.orderByDirection || 'desc'
    });
  }
  
  /**
   * Lista serviços por anunciante
   */
  async listByAdvertiser(advertiserId: string, options?: Omit<ListOptions, 'filters'>): Promise<PaginatedResult<Service>> {
    const filters = [
      { field: 'advertiserId', operator: '==', value: advertiserId }
    ];
    
    return this.list({
      ...options,
      filters,
      orderByField: options?.orderByField || 'createdAt',
      orderByDirection: options?.orderByDirection || 'desc'
    });
  }
  
  /**
   * Busca serviços por texto
   */
  async search(searchText: string, options?: Omit<ListOptions, 'filters'>): Promise<PaginatedResult<Service>> {
    // Nota: Firestore não suporta busca de texto completo nativamente
    // Para uma implementação real, seria necessário usar Algolia, Elasticsearch ou similar
    // Esta é uma implementação simplificada que busca apenas no título e descrição
    
    // Converter para minúsculas para busca case-insensitive
    const searchLower = searchText.toLowerCase();
    
    // Buscar todos os serviços publicados
    const result = await this.listPublished(options);
    
    // Filtrar localmente
    const filteredItems = result.items.filter(service => 
      service.title.toLowerCase().includes(searchLower) || 
      service.description.toLowerCase().includes(searchLower) ||
      (service.tags && service.tags.some(tag => tag.toLowerCase().includes(searchLower)))
    );
    
    return {
      items: filteredItems,
      lastDoc: result.lastDoc,
      hasMore: result.hasMore
    };
  }
  
  /**
   * Incrementa o contador de visualizações
   */
  async incrementViews(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.collectionName, id);
      await updateDoc(docRef, {
        views: increment(1)
      });
    } catch (error) {
      console.error('Error incrementing views:', error);
      throw error;
    }
  }
  
  /**
   * Publica um serviço
   */
  async publish(id: string): Promise<Service> {
    return this.update(id, {
      status: 'published',
      publishedAt: new Date()
    } as Partial<Service>);
  }
  
  /**
   * Arquiva um serviço
   */
  async archive(id: string): Promise<Service> {
    return this.update(id, {
      status: 'archived'
    } as Partial<Service>);
  }
  
  /**
   * Reporta um serviço
   */
  async report(id: string, reason: string): Promise<Service> {
    const service = await this.getById(id);
    
    if (!service) {
      throw new Error(`Service with ID ${id} not found`);
    }
    
    const reports = service.reports || 0;
    const reportReasons = service.reportReasons || [];
    
    return this.update(id, {
      reports: reports + 1,
      reportReasons: [...reportReasons, reason]
    } as Partial<Service>);
  }
  
  /**
   * Adiciona uma avaliação a um serviço
   */
  async addReview(
    id: string, 
    userId: string, 
    userName: string, 
    rating: number, 
    comment: string
  ): Promise<Service> {
    const service = await this.getById(id);
    
    if (!service) {
      throw new Error(`Service with ID ${id} not found`);
    }
    
    const reviews = service.reviews || [];
    const newReview = {
      userId,
      userName,
      rating,
      comment,
      createdAt: new Date()
    };
    
    // Calcular nova média de avaliações
    const totalRatings = (service.ratingCount || 0) + 1;
    const currentTotalPoints = (service.rating || 0) * (service.ratingCount || 0);
    const newTotalPoints = currentTotalPoints + rating;
    const newRating = newTotalPoints / totalRatings;
    
    return this.update(id, {
      reviews: [...reviews, newReview],
      rating: newRating,
      ratingCount: totalRatings
    } as Partial<Service>);
  }
}
