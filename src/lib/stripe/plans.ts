// src/lib/stripe/plans.ts

// IDs dos produtos no Stripe
export const STRIPE_PRODUCTS = {
  BASIC: 'prod_S9adEPIySupaT8',
  PRO: 'prod_S9adruKIX8WFti',
  BUSINESS: 'prod_S9adp7unmTkbxP',
};

// IDs dos preços no Stripe (mockados para fins de demonstração)
// Em um ambiente de produção, esses IDs seriam obtidos do Stripe
export const STRIPE_PRICES = {
  // Plano Básico
  BASIC_MONTHLY: 'price_1RFHVpQQvgjoGVJtlejew1BY', // Mensal - R$ 49,90
  BASIC_YEARLY: 'price_basic_yearly',               // Anual - R$ 479,00 (equivalente a R$ 39,92/mês)
  
  // Plano Profissional
  PRO_MONTHLY: 'price_pro_monthly',                 // Mensal - R$ 99,90
  PRO_YEARLY: 'price_pro_yearly',                   // Anual - R$ 959,00 (equivalente a R$ 79,92/mês)
  
  // Plano Empresarial
  BUSINESS_MONTHLY: 'price_business_monthly',       // Mensal - R$ 199,90
  BUSINESS_YEARLY: 'price_business_yearly',         // Anual - R$ 1.919,00 (equivalente a R$ 159,92/mês)
};

// Mapeamento de IDs de planos para IDs de preços do Stripe
export const PLAN_PRICE_MAP: Record<string, string> = {
  'basic-monthly': STRIPE_PRICES.BASIC_MONTHLY,
  'pro-monthly': STRIPE_PRICES.PRO_MONTHLY,
  'business-monthly': STRIPE_PRICES.BUSINESS_MONTHLY,
  'basic-yearly': STRIPE_PRICES.BASIC_YEARLY,
  'pro-yearly': STRIPE_PRICES.PRO_YEARLY,
  'business-yearly': STRIPE_PRICES.BUSINESS_YEARLY,
};

// Detalhes dos planos
export const PLANS = [
  // Planos Mensais
  {
    id: 'basic-monthly',
    name: 'Básico',
    description: 'Ideal para quem está começando',
    price: 4990, // R$ 49,90
    interval: 'month',
    popular: false,
    modules: {
      services: true,
      properties: false,
      vehicles: false,
      products: false,
    },
    limits: {
      services: 5,
      properties: 0,
      vehicles: 0,
      products: 0,
      featured: 1,
    },
    features: [
      { name: 'Até 5 serviços ativos', included: true },
      { name: '1 anúncio em destaque', included: true },
      { name: 'Estatísticas básicas', included: true },
      { name: 'Suporte por email', included: true },
      { name: 'Imóveis', included: false },
      { name: 'Veículos', included: false },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
  },
  {
    id: 'pro-monthly',
    name: 'Profissional',
    description: 'Para profissionais e pequenas empresas',
    price: 9990, // R$ 99,90
    interval: 'month',
    popular: true,
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: false,
    },
    limits: {
      services: 20,
      properties: 10,
      vehicles: 10,
      products: 0,
      featured: 5,
    },
    features: [
      { name: 'Até 20 serviços ativos', included: true },
      { name: 'Até 10 imóveis ativos', included: true },
      { name: 'Até 10 veículos ativos', included: true },
      { name: '5 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte por email e chat', included: true },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
  },
  {
    id: 'business-monthly',
    name: 'Empresarial',
    description: 'Para empresas e grandes anunciantes',
    price: 19990, // R$ 199,90
    interval: 'month',
    popular: false,
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: true,
    },
    limits: {
      services: 50,
      properties: 30,
      vehicles: 30,
      products: 100,
      featured: 15,
    },
    features: [
      { name: 'Até 50 serviços ativos', included: true },
      { name: 'Até 30 imóveis ativos', included: true },
      { name: 'Até 30 veículos ativos', included: true },
      { name: 'Até 100 produtos ativos', included: true },
      { name: '15 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte prioritário', included: true },
      { name: 'API de integração', included: true },
    ],
  },
  
  // Planos Anuais
  {
    id: 'basic-yearly',
    name: 'Básico',
    description: 'Ideal para quem está começando',
    price: 47900, // R$ 479,00 (equivalente a R$ 39,92/mês)
    interval: 'year',
    popular: false,
    modules: {
      services: true,
      properties: false,
      vehicles: false,
      products: false,
    },
    limits: {
      services: 5,
      properties: 0,
      vehicles: 0,
      products: 0,
      featured: 1,
    },
    features: [
      { name: 'Até 5 serviços ativos', included: true },
      { name: '1 anúncio em destaque', included: true },
      { name: 'Estatísticas básicas', included: true },
      { name: 'Suporte por email', included: true },
      { name: 'Imóveis', included: false },
      { name: 'Veículos', included: false },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
  },
  {
    id: 'pro-yearly',
    name: 'Profissional',
    description: 'Para profissionais e pequenas empresas',
    price: 95900, // R$ 959,00 (equivalente a R$ 79,92/mês)
    interval: 'year',
    popular: true,
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: false,
    },
    limits: {
      services: 20,
      properties: 10,
      vehicles: 10,
      products: 0,
      featured: 5,
    },
    features: [
      { name: 'Até 20 serviços ativos', included: true },
      { name: 'Até 10 imóveis ativos', included: true },
      { name: 'Até 10 veículos ativos', included: true },
      { name: '5 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte por email e chat', included: true },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
  },
  {
    id: 'business-yearly',
    name: 'Empresarial',
    description: 'Para empresas e grandes anunciantes',
    price: 191900, // R$ 1.919,00 (equivalente a R$ 159,92/mês)
    interval: 'year',
    popular: false,
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: true,
    },
    limits: {
      services: 50,
      properties: 30,
      vehicles: 30,
      products: 100,
      featured: 15,
    },
    features: [
      { name: 'Até 50 serviços ativos', included: true },
      { name: 'Até 30 imóveis ativos', included: true },
      { name: 'Até 30 veículos ativos', included: true },
      { name: 'Até 100 produtos ativos', included: true },
      { name: '15 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte prioritário', included: true },
      { name: 'API de integração', included: true },
    ],
  },
];
