// src/lib/firebase/admin.ts
import { initializeApp, getApps, cert, App } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { getAuth } from 'firebase-admin/auth';
import { getStorage } from 'firebase-admin/storage';

let adminApp: App;

export const initFirebaseAdmin = () => {
  if (getApps().length === 0) {
    const serviceAccount = JSON.parse(
      process.env.FIREBASE_SERVICE_ACCOUNT_KEY as string
    );

    adminApp = initializeApp({
      credential: cert(serviceAccount),
      storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    });
  } else {
    adminApp = getApps()[0];
  }

  return adminApp;
};

export const getAdminFirestore = () => {
  const app = initFirebaseAdmin();
  return getFirestore(app);
};

export const getAdminAuth = () => {
  const app = initFirebaseAdmin();
  return getAuth(app);
};

export const getAdminStorage = () => {
  const app = initFirebaseAdmin();
  return getStorage(app);
};
