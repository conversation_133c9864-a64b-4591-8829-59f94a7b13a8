// src/lib/subscription/index.ts
import { db } from '@/lib/firebase/client';
import { collection, query, where, getDocs, doc, getDoc, Timestamp } from 'firebase/firestore';

/**
 * Verifica se um usuário tem uma assinatura ativa
 * @param userId ID do usuário
 * @returns true se o usuário tem uma assinatura ativa, false caso contrário
 */
export async function hasActiveSubscription(userId: string): Promise<boolean> {
  try {
    // Verificar se o usuário existe
    const userDoc = await getDoc(doc(db, 'users', userId));

    if (!userDoc.exists()) {
      return false;
    }

    const userData = userDoc.data();
    const subscription = userData.subscription;

    // Se não houver assinatura, retornar false
    if (!subscription) {
      return false;
    }

    // Verificar se a assinatura está ativa
    if (!['active', 'trialing'].includes(subscription.status)) {
      return false;
    }

    // Verificar se a assinatura está expirada
    const now = new Date();

    // Verificar se a assinatura tem data de expiração
    if (subscription.currentPeriodEnd) {
      const expirationDate =
        subscription.currentPeriodEnd instanceof Timestamp
          ? subscription.currentPeriodEnd.toDate()
          : new Date(subscription.currentPeriodEnd);

      // Se a data de expiração for no passado, a assinatura está expirada
      if (expirationDate < now) {
        return false;
      }
    }

    // Se chegou aqui, a assinatura está ativa
    return true;
  } catch (error) {
    console.error('Error checking subscription status:', error);
    // Em caso de erro, assumir que não tem assinatura ativa
    return false;
  }
}

/**
 * Obtém os detalhes da assinatura ativa de um usuário
 * @param userId ID do usuário
 * @returns Detalhes da assinatura ativa ou null se não houver
 */
export async function getActiveSubscription(userId: string): Promise<any | null> {
  try {
    // Verificar se o usuário existe
    const userDoc = await getDoc(doc(db, 'users', userId));

    if (!userDoc.exists()) {
      return null;
    }

    const userData = userDoc.data();
    const subscription = userData.subscription;

    // Se não houver assinatura, retornar null
    if (!subscription) {
      return null;
    }

    // Verificar se a assinatura está ativa
    if (!['active', 'trialing'].includes(subscription.status)) {
      return null;
    }

    // Verificar se a assinatura está expirada
    const now = new Date();

    // Verificar se a assinatura tem data de expiração
    if (subscription.currentPeriodEnd) {
      const expirationDate =
        subscription.currentPeriodEnd instanceof Timestamp
          ? subscription.currentPeriodEnd.toDate()
          : new Date(subscription.currentPeriodEnd);

      // Se a data de expiração for no passado, a assinatura está expirada
      if (expirationDate < now) {
        return null;
      }
    }

    // Converter timestamps para Date se necessário
    if (
      subscription.currentPeriodStart &&
      typeof subscription.currentPeriodStart.toDate === 'function'
    ) {
      subscription.currentPeriodStart = subscription.currentPeriodStart.toDate();
    }

    if (
      subscription.currentPeriodEnd &&
      typeof subscription.currentPeriodEnd.toDate === 'function'
    ) {
      subscription.currentPeriodEnd = subscription.currentPeriodEnd.toDate();
    }

    if (subscription.createdAt && typeof subscription.createdAt.toDate === 'function') {
      subscription.createdAt = subscription.createdAt.toDate();
    }

    if (subscription.updatedAt && typeof subscription.updatedAt.toDate === 'function') {
      subscription.updatedAt = subscription.updatedAt.toDate();
    }

    return subscription;
  } catch (error) {
    console.error('Error getting active subscription:', error);
    return null;
  }
}

/**
 * Verifica se um usuário tem acesso a um determinado recurso com base em sua assinatura
 * @param userId ID do usuário
 * @param resource Nome do recurso (ex: 'services', 'properties', etc.)
 * @returns true se o usuário tem acesso ao recurso, false caso contrário
 */
export async function hasResourceAccess(userId: string, resource: string): Promise<boolean> {
  try {
    // Obter a assinatura ativa do usuário
    const subscription = await getActiveSubscription(userId);

    // Se não tiver assinatura ativa, não tem acesso
    if (!subscription) {
      return false;
    }

    // Verificar se a assinatura tem o plano associado
    if (!subscription.planId) {
      return false;
    }

    // Buscar o plano
    const planDoc = await getDoc(doc(db, 'plans', subscription.planId));

    if (!planDoc.exists()) {
      return false;
    }

    const plan = planDoc.data();

    // Verificar se o plano dá acesso ao recurso
    if (plan.resources && plan.resources.includes(resource)) {
      return true;
    }

    // Verificar se o plano tem módulos e se o recurso está incluído
    if (plan.modules) {
      // Mapear recursos para módulos
      const resourceToModule: Record<string, string> = {
        services: 'services',
        properties: 'properties',
        vehicles: 'vehicles',
        products: 'products',
      };

      const module = resourceToModule[resource];

      if (module && plan.modules[module]) {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('Error checking resource access:', error);
    return false;
  }
}

/**
 * Verifica se um usuário atingiu o limite de um recurso com base em sua assinatura
 * @param userId ID do usuário
 * @param resource Nome do recurso (ex: 'services', 'properties', etc.)
 * @param currentCount Contagem atual do recurso
 * @returns true se o usuário atingiu o limite, false caso contrário
 */
export async function hasReachedResourceLimit(
  userId: string,
  resource: string,
  currentCount: number,
): Promise<boolean> {
  try {
    // Obter a assinatura ativa do usuário
    const subscription = await getActiveSubscription(userId);

    // Se não tiver assinatura ativa, considerar que atingiu o limite
    if (!subscription) {
      return true;
    }

    // Verificar se a assinatura tem o plano associado
    if (!subscription.planId) {
      return true;
    }

    // Buscar o plano
    const planDoc = await getDoc(doc(db, 'plans', subscription.planId));

    if (!planDoc.exists()) {
      return true;
    }

    const plan = planDoc.data();

    // Verificar se o plano tem limites definidos
    if (plan.limits && plan.limits[resource] !== undefined) {
      return currentCount >= plan.limits[resource];
    }

    // Se não tiver limite definido, considerar que não atingiu o limite
    return false;
  } catch (error) {
    console.error('Error checking resource limit:', error);
    // Em caso de erro, assumir que atingiu o limite por segurança
    return true;
  }
}
