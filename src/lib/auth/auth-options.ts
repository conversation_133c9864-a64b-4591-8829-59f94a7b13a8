// src/lib/auth/auth-options.ts
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { FirebaseError } from "firebase/app";
import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "@/lib/firebase/client";
import { getAdminAuth } from "@/lib/firebase/admin";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Senha", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const userCredential = await signInWithEmailAndPassword(
            auth,
            credentials.email,
            credentials.password
          );

          const idToken = await userCredential.user.getIdToken();

          return {
            id: userCredential.user.uid,
            email: userCredential.user.email,
            name: userCredential.user.displayName,
            image: userCredential.user.photoURL,
            idToken,
          };
        } catch (error) {
          if (error instanceof FirebaseError) {
            console.error("Firebase auth error:", error.code);
          }
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // Initial sign in
      if (account && user) {
        if (account.provider === "google") {
          // For Google sign-in, we need to get a Firebase custom token
          const adminAuth = getAdminAuth();
          const firebaseToken = await adminAuth.createCustomToken(user.id);
          
          return {
            ...token,
            id: user.id,
            firebaseToken,
          };
        } else {
          // For credentials sign-in, we already have the idToken
          return {
            ...token,
            id: user.id,
            idToken: (user as any).idToken,
          };
        }
      }
      
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        (session.user as any).id = token.id;
        (session.user as any).idToken = token.idToken;
        (session.user as any).firebaseToken = token.firebaseToken;
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
};
