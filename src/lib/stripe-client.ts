// src/lib/stripe-client.ts
// Este arquivo é para uso APENAS no cliente (frontend)

import { loadStripe } from '@stripe/stripe-js';

// Inicializar o cliente Stripe com a chave pública
let stripePromise: Promise<any> | null = null;

export const getStripeClient = () => {
  if (!stripePromise) {
    const key = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    
    if (!key) {
      console.error('Stripe publishable key not found in environment variables');
      return null;
    }
    
    stripePromise = loadStripe(key);
  }
  
  return stripePromise;
};

// Função para criar uma sessão de checkout via API
export const createCheckoutSession = async (planId: string) => {
  try {
    const response = await fetch('/api/stripe/checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ planId }),
    });
    
    if (!response.ok) {
      throw new Error('Erro ao criar sessão de checkout');
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erro ao criar sessão de checkout:', error);
    throw error;
  }
};

// Função para criar uma sessão de portal de faturamento via API
export const createBillingPortalSession = async () => {
  try {
    const response = await fetch('/api/stripe/portal', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error('Erro ao criar sessão de portal de faturamento');
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Erro ao criar sessão de portal de faturamento:', error);
    throw error;
  }
};
