// src/lib/subscription.ts
import { db } from '@/lib/firebase/client';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { collection, query, where, getDocs, doc, getDoc } from 'firebase/firestore';
import { Plan } from '@/types/models/plan';

/**
 * Verifica se o usuário tem uma assinatura ativa
 */
export async function getActiveSubscription(userId: string) {
  try {
    // Buscar o documento do usuário
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);

    if (!userDoc.exists()) {
      return null;
    }

    const userData = userDoc.data();
    const subscription = userData.subscription;

    // Verificar se o usuário tem uma assinatura ativa
    if (!subscription || !['active', 'trialing'].includes(subscription.status)) {
      return null;
    }

    // Converter timestamps do Firestore para objetos Date
    if (
      subscription.currentPeriodStart &&
      typeof subscription.currentPeriodStart.toDate === 'function'
    ) {
      subscription.currentPeriodStart = subscription.currentPeriodStart.toDate();
    }

    if (
      subscription.currentPeriodEnd &&
      typeof subscription.currentPeriodEnd.toDate === 'function'
    ) {
      subscription.currentPeriodEnd = subscription.currentPeriodEnd.toDate();
    }

    if (subscription.createdAt && typeof subscription.createdAt.toDate === 'function') {
      subscription.createdAt = subscription.createdAt.toDate();
    }

    if (subscription.updatedAt && typeof subscription.updatedAt.toDate === 'function') {
      subscription.updatedAt = subscription.updatedAt.toDate();
    }

    return subscription;
  } catch (error) {
    console.error('Erro ao verificar assinatura:', error);
    return null;
  }
}

/**
 * Busca todos os planos ativos
 */
export async function getActivePlans(): Promise<Plan[]> {
  try {
    const adminDb = getAdminFirestore();

    // Usar a API do Firestore Admin corretamente
    const plansRef = adminDb.collection('plans');
    const plansQuery = plansRef.where('active', '==', true);

    const querySnapshot = await plansQuery.get();

    return querySnapshot.docs.map((doc) => {
      const data = doc.data();

      // Converter timestamps do Firestore para objetos Date
      const createdAt = data.createdAt ? new Date(data.createdAt._seconds * 1000) : new Date();
      const updatedAt = data.updatedAt ? new Date(data.updatedAt._seconds * 1000) : new Date();

      return {
        id: doc.id,
        ...data,
        createdAt,
        updatedAt,
      };
    }) as Plan[];
  } catch (error) {
    console.error('Erro ao buscar planos ativos:', error);
    return [];
  }
}

/**
 * Busca um plano pelo ID
 */
export async function getPlanById(planId: string): Promise<Plan | null> {
  try {
    const adminDb = getAdminFirestore();
    const planRef = adminDb.collection('plans').doc(planId);
    const planDoc = await planRef.get();

    if (!planDoc.exists) {
      return null;
    }

    const data = planDoc.data();

    // Converter timestamps do Firestore para objetos Date
    const createdAt = data.createdAt ? new Date(data.createdAt._seconds * 1000) : new Date();
    const updatedAt = data.updatedAt ? new Date(data.updatedAt._seconds * 1000) : new Date();

    return {
      id: planDoc.id,
      ...data,
      createdAt,
      updatedAt,
    } as Plan;
  } catch (error) {
    console.error('Erro ao buscar plano:', error);
    return null;
  }
}
