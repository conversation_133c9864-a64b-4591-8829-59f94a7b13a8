'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

const steps = [
  {
    number: '01',
    title: 'Crie sua conta',
    description: 'Registre-se gratuitamente em menos de 1 minuto e comece a explorar.',
    color: 'bg-blue-500',
  },
  {
    number: '02',
    title: 'Explore ou anuncie',
    description: 'Encontre o que procura ou crie anúncios para seus produtos e serviços.',
    color: 'bg-green-500',
  },
  {
    number: '03',
    title: 'Conecte-se',
    description: 'Entre em contato diretamente com vendedores ou compradores interessados.',
    color: 'bg-amber-500',
  },
  {
    number: '04',
    title: 'Feche negócios',
    description: 'Finalize suas transações com segurança e praticidade.',
    color: 'bg-purple-500',
  },
];

export default function HowItWorksSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.2 });

  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
            Como funciona
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Simples, rápido e eficiente. Veja como o JáVai pode ajudar você.
          </p>
        </div>

        <div ref={ref} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative"
            >
              <div className="bg-card dark:bg-card/50 p-6 rounded-lg shadow-sm border border-border h-full">
                <div className={`absolute -top-4 -left-4 ${step.color} text-white w-10 h-10 rounded-full flex items-center justify-center font-bold`}>
                  {step.number}
                </div>
                <h3 className="text-xl font-semibold mb-3 mt-4">{step.title}</h3>
                <p className="text-muted-foreground">{step.description}</p>
              </div>
            </motion.div>
          ))}
        </div>

        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Button asChild size="lg" className="rounded-full px-8">
              <Link href="/auth/register">Comece Agora</Link>
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
