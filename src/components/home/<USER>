'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { 
  Home, 
  Car, 
  ShoppingBag, 
  Wrench, 
  Users, 
  Calendar, 
  MessageSquare, 
  Search 
} from 'lucide-react';

const features = [
  {
    icon: Home,
    title: 'Imóveis',
    description: 'Encontre o imóvel dos seus sonhos ou anuncie para milhares de potenciais compradores.',
  },
  {
    icon: Car,
    title: 'Veículos',
    description: 'Compre, venda ou troque seu veículo com facilidade e segurança.',
  },
  {
    icon: ShoppingBag,
    title: 'Produtos',
    description: 'Descubra produtos incríveis ou venda os seus para um público qualificado.',
  },
  {
    icon: Wrench,
    title: 'Serviços',
    description: 'Contrate profissionais qualificados ou ofereça seus serviços para quem precisa.',
  },
  {
    icon: Users,
    title: 'Comunidade',
    description: 'Faça parte de uma comunidade ativa de compradores e vendedores.',
  },
  {
    icon: Calendar,
    title: 'Agendamentos',
    description: 'Sistema de agendamento integrado para serviços e visitas.',
  },
  {
    icon: MessageSquare,
    title: 'Chat Integrado',
    description: 'Comunique-se diretamente com vendedores e compradores.',
  },
  {
    icon: Search,
    title: 'Busca Avançada',
    description: 'Encontre exatamente o que procura com nossos filtros avançados.',
  },
];

export default function FeaturesSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.2 });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section className="py-20 bg-muted/30 dark:bg-muted/10">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
            Tudo o que você precisa em um só lugar
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            JáVai conecta pessoas e oportunidades, oferecendo uma plataforma completa para comprar, vender e contratar.
          </p>
        </div>

        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? 'visible' : 'hidden'}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-card dark:bg-card/50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow border border-border"
            >
              <div className="bg-primary/10 dark:bg-primary/20 p-3 rounded-full w-12 h-12 flex items-center justify-center mb-4">
                <feature.icon className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
              <p className="text-muted-foreground">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
