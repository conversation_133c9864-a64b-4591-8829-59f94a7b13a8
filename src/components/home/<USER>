'use client';

import { useEffect, useState } from 'react';
import Typewriter from 'typewriter-effect';

interface TypingEffectProps {
  phrases: string[];
  className?: string;
}

export default function TypingEffect({ phrases, className = '' }: TypingEffectProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return;
  }

  return (
    <div className={className}>
      <Typewriter
        options={{
          strings: phrases,
          autoStart: true,
          loop: true,
          delay: 75,
          deleteSpeed: 50,
        }}
      />
    </div>
  );
}
