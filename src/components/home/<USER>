'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

export default function CTASection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.2 });

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background with gradient */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-background to-primary/20 dark:from-primary/10 dark:via-background dark:to-primary/10" />
      
      {/* Pattern overlay */}
      <div className="absolute inset-0 bg-[url('/cta-pattern.svg')] bg-repeat opacity-10" />
      
      <div className="container mx-auto px-4 relative z-10">
        <div
          ref={ref}
          className="max-w-4xl mx-auto text-center"
        >
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="text-3xl font-bold tracking-tight sm:text-4xl mb-6"
          >
            Pronto para transformar sua maneira de comprar e vender?
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-lg text-muted-foreground mb-10 max-w-2xl mx-auto"
          >
            Junte-se a milhares de usuários que já estão aproveitando todos os benefícios do JáVai. 
            Seja para encontrar o que procura ou para impulsionar seu negócio, estamos aqui para ajudar.
          </motion.p>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex flex-col sm:flex-row justify-center gap-4"
          >
            <Button asChild size="lg" className="rounded-full px-8">
              <Link href="/auth/register">Criar Conta Grátis</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="rounded-full px-8">
              <Link href="/subscription">Ver Planos para Anunciantes</Link>
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
