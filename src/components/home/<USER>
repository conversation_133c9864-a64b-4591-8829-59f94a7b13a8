'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { Star } from 'lucide-react';

const testimonials = [
  {
    name: '<PERSON>',
    role: '<PERSON><PERSON>riet<PERSON><PERSON> de Imobiliária',
    content: 'O JáVai transformou meu negócio. Consegui aumentar minhas vendas em 40% no primeiro mês de uso da plataforma.',
    rating: 5,
    image: '/avatars/avatar-1.png',
  },
  {
    name: '<PERSON>',
    role: 'Compradora',
    content: 'Encontrei meu apartamento dos sonhos através do JáVai. A plataforma é intuitiva e o contato com o vendedor foi super fácil.',
    rating: 5,
    image: '/avatars/avatar-2.png',
  },
  {
    name: '<PERSON>',
    role: 'Prestador de Serviços',
    content: 'Como eletricista, sempre tive dificuldade em encontrar novos clientes. Com o JáVai, minha agenda está sempre cheia!',
    rating: 4,
    image: '/avatars/avatar-3.png',
  },
  {
    name: '<PERSON>',
    role: 'Vendedora de Produtos',
    content: 'A facilidade de criar anún<PERSON> e o sistema de chat integrado fazem toda a diferença. Recomendo para todos os empreendedores.',
    rating: 5,
    image: '/avatars/avatar-4.png',
  },
];

export default function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.2 });

  return (
    <section className="py-20 bg-muted/30 dark:bg-muted/10">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
            O que nossos usuários dizem
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Histórias reais de pessoas que transformaram seus negócios e encontraram o que procuravam com o JáVai.
          </p>
        </div>

        <div
          ref={ref}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-card dark:bg-card/50 p-6 rounded-lg shadow-sm border border-border"
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-muted mr-4 overflow-hidden">
                  {testimonial.image ? (
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-primary/10 text-primary font-bold">
                      {testimonial.name.charAt(0)}
                    </div>
                  )}
                </div>
                <div>
                  <h4 className="font-semibold">{testimonial.name}</h4>
                  <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                </div>
              </div>
              <div className="flex mb-3">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < testimonial.rating
                        ? 'text-yellow-500 fill-yellow-500'
                        : 'text-muted-foreground'
                    }`}
                  />
                ))}
              </div>
              <p className="text-muted-foreground">{testimonial.content}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
