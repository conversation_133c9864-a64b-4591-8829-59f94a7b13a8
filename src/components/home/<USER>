'use client';

import { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import TypingEffect from './typing-effect';
import Logo from '../forms/logo';

export default function HeroSection() {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start start', 'end start'],
  });

  const y = useTransform(scrollYProgress, [0, 1], ['0%', '40%']);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  const clientPhrases = [
    'Precisando de mão de obra? JáVai!',
    'Procurando por uma casa? JáVai!',
    'Quer trocar de carro? JáVai!',
    'Buscando produtos novos? JáVai!',
  ];

  const businessPhrases = [
    'Quer divulgar seus serviços? JáVai!',
    'Tem imóveis para vender? JáVai!',
    'Quer anunciar seu veículo? JáVai!',
    'Quer vender seus produtos? JáVai!',
  ];

  return (
    <div ref={ref} className="relative h-[100vh] overflow-hidden">
      {/* Background with parallax effect */}
      <motion.div style={{ y, opacity }} className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-b from-primary/20 to-background/0 dark:from-primary/10 dark:to-background/0" />
        <div className="absolute inset-0 bg-[url('/hero-pattern.svg')] bg-repeat opacity-10 dark:opacity-5" />
      </motion.div>

      {/* Content */}
      <div className="container relative z-10 mx-auto flex h-full flex-col items-center justify-center px-4 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1.2 }}
          className="max-w-4xl"
        >
          <div className="mb-4 flex items-center justify-center space-x-2">
            <Logo full={false} size={24} />
          </div>
          <h1 className="mb-6 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
            Conectando <span className="text-primary">pessoas</span> e{' '}
            <span className="text-primary">oportunidades</span>
          </h1>

          <div className="mb-8 text-xl font-medium text-muted-foreground sm:text-2xl md:text-3xl">
            <TypingEffect
              phrases={[...clientPhrases, ...businessPhrases].sort(() => 0.5 - Math.random())}
            />
          </div>
          {/* <div className="mb-8 text-xl font-medium text-muted-foreground sm:text-2xl md:text-3xl">
            <TypingEffect phrases={clientPhrases} />
          </div> */}

          {/* <div className="mb-12 text-lg text-muted-foreground">
            <TypingEffect phrases={businessPhrases} className="italic" />
          </div> */}

          <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0 justify-center">
            <Button asChild size="lg" className="rounded-full px-8">
              <Link href="/public/services">Encontrar Serviços</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="rounded-full px-8">
              <Link href="/subscription">Anunciar Agora</Link>
            </Button>
          </div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 -translate-x-1/2">
        <motion.div
          animate={{ y: [0, 10, 0] }}
          transition={{ repeat: Infinity, duration: 1.5 }}
          className="flex flex-col items-center"
        >
          <span className="mb-2 text-sm text-muted-foreground">Rolar para descobrir</span>
          <div className="h-6 w-4 rounded-full border-2 border-primary">
            <motion.div
              animate={{ y: [0, 8, 0] }}
              transition={{ repeat: Infinity, duration: 1.5 }}
              className="mx-auto mt-1 h-1 w-1 rounded-full bg-primary"
            />
          </div>
        </motion.div>
      </div>
    </div>
  );
}
