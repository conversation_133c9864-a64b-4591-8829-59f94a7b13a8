// src/components/layout/dashboard-header.tsx
'use client';

import { User } from 'next-auth';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { signOut } from 'next-auth/react';
import { useState, useEffect } from 'react';
import {
  Bell,
  Menu,
  Settings,
  User as UserIcon,
  Home,
  Calendar,
  MessageSquare,
  Heart,
  CreditCard,
  BriefcaseBusiness,
  Store,
  ShieldCheck,
} from 'lucide-react';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import DashboardSidebar from './dashboard-sidebar';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import Logo from '../forms/logo';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface DashboardHeaderProps {
  user: User;
}

export default function DashboardHeader({ user }: DashboardHeaderProps) {
  const pathname = usePathname();
  const [hasSubscription, setHasSubscription] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Verificar se o usuário tem uma assinatura ativa e se é administrador
  useEffect(() => {
    const checkUserStatus = async () => {
      try {
        // Verificar assinatura
        const subscriptionResponse = await fetch('/api/subscription/check');
        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();
          setHasSubscription(subscriptionData.hasSubscription);
        }

        // Verificar se é administrador
        const adminEmails = [
          process.env.NEXT_PUBLIC_ADMIN_EMAIL,
          '<EMAIL>',
          '<EMAIL>',
        ].filter(Boolean);

        setIsAdmin(adminEmails.includes(user.email || ''));
      } catch (error) {
        console.error('Erro ao verificar status do usuário:', error);
      }
    };

    checkUserStatus();
  }, [user]);
  return (
    <header className="border-b border-slate-200 bg-white dark:bg-slate-950 dark:border-slate-800">
      <div className="flex h-16 items-center px-4 md:px-6">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="mr-4 md:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-full max-w-[300px]">
            <div className="flex h-full w-full flex-col bg-white dark:bg-slate-900">
              <div className="flex h-16 items-center border-b">
                <Logo />
              </div>
              <div className="flex-1 overflow-auto py-2">
                <nav className="grid items-start px-2 text-sm font-medium">
                  {[
                    {
                      title: 'Dashboard',
                      href: '/dashboard',
                      icon: Home,
                    },
                    {
                      title: 'Agendamentos',
                      href: '/dashboard/appointments',
                      icon: Calendar,
                    },
                    {
                      title: 'Mensagens',
                      href: '/dashboard/messages',
                      icon: MessageSquare,
                    },
                    {
                      title: 'Favoritos',
                      href: '/dashboard/favorites',
                      icon: Heart,
                    },
                    {
                      title: 'Assinatura',
                      href: '/dashboard/subscription',
                      icon: CreditCard,
                    },
                  ].map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        'flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary',
                        pathname === item.href
                          ? 'bg-slate-100 dark:bg-slate-800 text-primary'
                          : 'text-slate-500 hover:bg-slate-100 hover:bg-slate-800',
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      {item.title}
                    </Link>
                  ))}
                </nav>
              </div>
              <div className="border-t p-4 space-y-3">
                {hasSubscription ? (
                  <Link
                    href="/advertiser"
                    className="flex items-center gap-3 rounded-lg bg-green-600 dark:bg-green-700 px-4 py-2 text-sm font-medium text-white hover:bg-green-700 transition-colors"
                  >
                    <Store className="h-4 w-4" />
                    Acessar Painel de Anunciante
                  </Link>
                ) : (
                  <Link
                    href="/subscription"
                    className="flex items-center gap-3 rounded-lg bg-slate-900 dark:bg-slate-800 px-4 py-2 text-sm font-medium text-white hover:bg-slate-900 hover:bg-slate-800/90 transition-colors"
                  >
                    <BriefcaseBusiness className="h-4 w-4" />
                    Tornar-se um Anunciante
                  </Link>
                )}

                {isAdmin && (
                  <Link
                    href="/admin/dashboard"
                    className="flex items-center gap-3 rounded-lg bg-indigo-600 dark:bg-indigo-700 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 dark:hover:bg-indigo-600 transition-colors border border-indigo-500 dark:border-indigo-600 shadow-sm"
                  >
                    <ShieldCheck className="h-4 w-4" />
                    Acessar Painel Admin
                  </Link>
                )}
              </div>
            </div>
          </SheetContent>
        </Sheet>
        <div className="ml-auto flex items-center gap-4">
          <ThemeToggle />
          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
            <span className="sr-only">Notificações</span>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={user.image || ''} alt={user.name || user.email || 'Usuário'} />
                  <AvatarFallback>
                    <UserIcon className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel className="flex flex-col items-center gap-2">
                <span className="text-primary">{user.name || 'Usuário'}</span>
                <span className="text-sm text-muted-foreground">{user.email || 'Minha Conta'}</span>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <UserIcon className="mr-2 h-4 w-4" />
                <span>Perfil</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Configurações</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => signOut({ callbackUrl: '/' })}>
                Sair
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
