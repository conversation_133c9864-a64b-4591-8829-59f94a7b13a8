// src/components/layout/dashboard-sidebar.tsx
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  Calendar,
  Home,
  MessageSquare,
  Heart,
  CreditCard,
  BriefcaseBusiness,
  Store,
  ShieldCheck,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import Logo from '../forms/logo';

const sidebarItems = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: Home,
  },
  {
    title: 'Agendamentos',
    href: '/dashboard/appointments',
    icon: Calendar,
  },
  {
    title: 'Mensagens',
    href: '/dashboard/messages',
    icon: MessageSquare,
  },
  {
    title: 'Favoritos',
    href: '/dashboard/favorites',
    icon: Heart,
  },
  {
    title: 'Assinatura',
    href: '/dashboard/subscription',
    icon: CreditCard,
  },
];

export default function DashboardSidebar() {
  const pathname = usePathname();
  const { data: session } = useSession();
  const [hasSubscription, setHasSubscription] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Verificar se o usuário tem uma assinatura ativa e se é administrador
  useEffect(() => {
    const checkUserStatus = async () => {
      if (!session?.user?.id) return;

      try {
        // Verificar assinatura
        const subscriptionResponse = await fetch('/api/subscription/check');
        if (subscriptionResponse.ok) {
          const subscriptionData = await subscriptionResponse.json();
          setHasSubscription(subscriptionData.hasSubscription);
        }

        // Verificar se é administrador
        // Em produção, isso seria verificado com base em um campo no documento do usuário
        // ou em uma lista de administradores definida em variáveis de ambiente
        const adminEmails = [
          process.env.NEXT_PUBLIC_ADMIN_EMAIL,
          '<EMAIL>',
          '<EMAIL>',
        ].filter(Boolean);

        setIsAdmin(adminEmails.includes(session.user.email || ''));
      } catch (error) {
        console.error('Erro ao verificar status do usuário:', error);
      }
    };

    checkUserStatus();
  }, [session]);

  return (
    <div className="flex h-screen w-64 flex-col border-r bg-white dark:bg-slate-900 sticky top-0">
      <div className="flex h-16 items-center border-b">
        <Logo />
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid items-start px-2 text-sm font-medium">
          {sidebarItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary',
                pathname === item.href
                  ? 'bg-slate-100 dark:bg-slate-800 text-primary'
                  : 'text-slate-500 hover:bg-slate-100 hover:bg-slate-800',
              )}
            >
              <item.icon className="h-4 w-4" />
              {item.title}
            </Link>
          ))}
        </nav>
      </div>
      <div className="border-t p-4 space-y-3">
        {hasSubscription ? (
          <Link
            href="/advertiser"
            className="flex items-center gap-3 rounded-lg bg-green-600 dark:bg-green-700 px-4 py-2 text-sm font-medium text-white hover:bg-green-700 transition-colors"
          >
            <Store className="h-4 w-4" />
            Acessar Painel de Anunciante
          </Link>
        ) : (
          <Link
            href="/subscription"
            className="flex items-center gap-3 rounded-lg bg-slate-900 dark:bg-slate-800 px-4 py-2 text-sm font-medium text-white hover:bg-slate-900 hover:bg-slate-800/90 transition-colors"
          >
            <BriefcaseBusiness className="h-4 w-4" />
            Tornar-se um Anunciante
          </Link>
        )}

        {isAdmin && (
          <Link
            href="/admin/dashboard"
            className="flex items-center gap-3 rounded-lg bg-indigo-600 dark:bg-indigo-700 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 dark:hover:bg-indigo-600 transition-colors border border-indigo-500 dark:border-indigo-600 shadow-sm"
          >
            <ShieldCheck className="h-4 w-4" />
            Acessar Painel Admin
          </Link>
        )}
      </div>
    </div>
  );
}
