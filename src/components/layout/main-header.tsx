'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useSession } from 'next-auth/react';
import { Menu, X } from 'lucide-react';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import Logo from '../forms/logo';

export default function MainHeader() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Não mostrar o header em páginas de autenticação ou dashboard
  if (
    pathname.startsWith('/auth') ||
    pathname.startsWith('/dashboard') ||
    pathname.startsWith('/admin') ||
    pathname.startsWith('/advertiser')
  ) {
    return null;
  }

  return (
    <header className="bg-white dark:bg-slate-950 border-b border-slate-200 dark:border-slate-800 sticky top-0 z-40">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Logo />

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link
              href="/public/services"
              className="text-slate-600 dark:text-slate-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              Serviços
            </Link>
            <Link
              href="/public/properties"
              className="text-slate-600 dark:text-slate-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              Imóveis
            </Link>
            <Link
              href="/public/vehicles"
              className="text-slate-600 dark:text-slate-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              Veículos
            </Link>
            <Link
              href="/public/products"
              className="text-slate-600 dark:text-slate-300 hover:text-primary dark:hover:text-primary transition-colors"
            >
              Produtos
            </Link>

            {/* Destacar link de assinatura */}
            <Link
              href="/subscription"
              className="text-primary font-medium hover:text-primary/80 transition-colors"
            >
              Planos
            </Link>
          </nav>

          {/* Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            <ThemeToggle />
            {session ? (
              <>
                <Button asChild variant="outline" size="sm">
                  <Link href="/dashboard">Minha Conta</Link>
                </Button>
                <Button asChild size="sm">
                  <Link href="/subscription">Assinar</Link>
                </Button>
              </>
            ) : (
              <>
                <Button asChild variant="outline" size="sm">
                  <Link href="/auth/login">Entrar</Link>
                </Button>
                <Button asChild size="sm">
                  <Link href="/auth/register">Cadastrar</Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden" onClick={() => setMobileMenuOpen(!mobileMenuOpen)}>
            {mobileMenuOpen ? (
              <X className="h-6 w-6 text-slate-600" />
            ) : (
              <Menu className="h-6 w-6 text-slate-600" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-white dark:bg-slate-950 border-t border-slate-200 dark:border-slate-800 py-4">
          <div className="container mx-auto px-4 space-y-4">
            <Link
              href="/public/services"
              className="block text-slate-600 hover:text-primary transition-colors py-2"
              onClick={() => setMobileMenuOpen(false)}
            >
              Serviços
            </Link>
            <Link
              href="/public/properties"
              className="block text-slate-600 hover:text-primary transition-colors py-2"
              onClick={() => setMobileMenuOpen(false)}
            >
              Imóveis
            </Link>
            <Link
              href="/public/vehicles"
              className="block text-slate-600 hover:text-primary transition-colors py-2"
              onClick={() => setMobileMenuOpen(false)}
            >
              Veículos
            </Link>
            <Link
              href="/public/products"
              className="block text-slate-600 hover:text-primary transition-colors py-2"
              onClick={() => setMobileMenuOpen(false)}
            >
              Produtos
            </Link>

            {/* Destacar link de assinatura */}
            <Link
              href="/subscription"
              className="block text-primary font-medium hover:text-primary/80 transition-colors py-2"
              onClick={() => setMobileMenuOpen(false)}
            >
              Planos
            </Link>

            <div className="flex justify-center py-2">
              <ThemeToggle />
            </div>

            <div className="pt-4 border-t border-slate-200 space-y-3">
              {session ? (
                <>
                  <Button asChild variant="outline" className="w-full">
                    <Link href="/dashboard" onClick={() => setMobileMenuOpen(false)}>
                      Minha Conta
                    </Link>
                  </Button>
                  <Button asChild className="w-full">
                    <Link href="/subscription" onClick={() => setMobileMenuOpen(false)}>
                      Assinar
                    </Link>
                  </Button>
                </>
              ) : (
                <>
                  <Button asChild variant="outline" className="w-full">
                    <Link href="/auth/login" onClick={() => setMobileMenuOpen(false)}>
                      Entrar
                    </Link>
                  </Button>
                  <Button asChild className="w-full">
                    <Link href="/auth/register" onClick={() => setMobileMenuOpen(false)}>
                      Cadastrar
                    </Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
