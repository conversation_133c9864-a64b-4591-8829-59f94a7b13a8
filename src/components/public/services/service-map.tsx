'use client';

import { useState, useEffect, useRef } from 'react';
import { Service } from '@/types/models/service';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import dynamic from 'next/dynamic';

// Importar Leaflet dinamicamente para evitar problemas de SSR
const MapContainer = dynamic(() => import('react-leaflet').then((mod) => mod.MapContainer), {
  ssr: false,
});
const TileLayer = dynamic(() => import('react-leaflet').then((mod) => mod.TileLayer), {
  ssr: false,
});
const Marker = dynamic(() => import('react-leaflet').then((mod) => mod.Marker), { ssr: false });
const Popup = dynamic(() => import('react-leaflet').then((mod) => mod.Popup), { ssr: false });
const ZoomControl = dynamic(() => import('react-leaflet').then((mod) => mod.ZoomControl), {
  ssr: false,
});
const useMap = dynamic(() => import('react-leaflet').then((mod) => mod.useMap), { ssr: false });

interface ServiceMapProps {
  services: Service[];
}

// Componente para ajustar o mapa para mostrar todos os marcadores
function FitBounds({ services }: { services: Service[] }) {
  const map = useMap();

  useEffect(() => {
    if (services.length === 0) return;

    // Extrair coordenadas válidas dos serviços
    const validCoords = services
      .filter((service) => {
        return service.coordinates?.latitude && service.coordinates?.longitude;
      })
      .map((service) => [service.coordinates!.latitude, service.coordinates!.longitude]);

    // Se não houver coordenadas válidas, usar uma localização padrão (Brasil)
    if (validCoords.length === 0) {
      map.setView([-15.77972, -47.92972], 4); // Brasil
      return;
    }

    // Se houver apenas um marcador, centralizar nele
    if (validCoords.length === 1) {
      map.setView(validCoords[0], 13);
      return;
    }

    // Criar bounds para ajustar o mapa para mostrar todos os marcadores
    try {
      // @ts-ignore - Leaflet types issue
      const bounds = L.latLngBounds(validCoords);
      map.fitBounds(bounds, { padding: [50, 50] });
    } catch (error) {
      console.error('Erro ao ajustar o mapa:', error);
      map.setView([-15.77972, -47.92972], 4); // Brasil como fallback
    }
  }, [map, services]);

  return null;
}

export default function ServiceMap({ services }: ServiceMapProps) {
  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapError, setMapError] = useState<string | null>(null);
  const mapRef = useRef(null);

  // Verificar se estamos no lado do cliente
  useEffect(() => {
    setMapLoaded(true);

    // Importar o CSS do Leaflet
    import('leaflet/dist/leaflet.css');

    // Corrigir o problema dos ícones do Leaflet
    import('leaflet')
      .then((L) => {
        delete (L.Icon.Default.prototype as any)._getIconUrl;

        L.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
          iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
          shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
        });
      })
      .catch((error) => {
        console.error('Erro ao carregar Leaflet:', error);
        setMapError('Não foi possível carregar o mapa. Tente novamente mais tarde.');
      });
  }, []);

  if (mapError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Erro</AlertTitle>
        <AlertDescription>{mapError}</AlertDescription>
      </Alert>
    );
  }

  if (!mapLoaded) {
    return (
      <Card>
        <CardContent className="p-6 h-[500px] flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Carregando mapa...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Verificar se há serviços com coordenadas válidas
  const servicesWithCoords = services.filter(
    (service) => service.coordinates?.latitude && service.coordinates?.longitude,
  );

  // Se não houver serviços com coordenadas, mostrar mensagem
  if (servicesWithCoords.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 h-[500px] flex items-center justify-center">
          <div className="text-center max-w-md">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Sem localizações disponíveis</h3>
            <p className="text-muted-foreground mb-4">
              Não há serviços com localização definida para exibir no mapa.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="h-[500px] w-full">
          <MapContainer
            center={[-15.77972, -47.92972]} // Coordenadas iniciais (Brasil)
            zoom={4}
            style={{ height: '100%', width: '100%' }}
            zoomControl={false}
            ref={mapRef}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <ZoomControl position="bottomright" />
            <FitBounds services={services} />

            {services.map((service) => {
              // Verificar se o serviço tem coordenadas válidas
              if (!service.coordinates?.latitude || !service.coordinates?.longitude) {
                return null;
              }

              return (
                <Marker
                  key={service.id}
                  position={[service.coordinates.latitude, service.coordinates.longitude]}
                >
                  <Popup>
                    <div className="p-1">
                      <h3 className="font-medium text-base">{service.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {service.address?.city}, {service.address?.state}
                      </p>
                      <p className="text-sm font-medium mt-1">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: service.currency || 'BRL',
                        }).format(service.price / 100)}
                        {service.priceType === 'hourly' && ' / hora'}
                        {service.priceType === 'daily' && ' / dia'}
                        {service.priceType === 'monthly' && ' / mês'}
                      </p>
                      <Button asChild size="sm" className="w-full mt-2">
                        <Link href={`/public/services/${service.id}`}>Ver detalhes</Link>
                      </Button>
                    </div>
                  </Popup>
                </Marker>
              );
            })}
          </MapContainer>
        </div>
      </CardContent>
    </Card>
  );
}
