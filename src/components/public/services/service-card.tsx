'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Service } from '@/types/models/service';
import { formatCurrency, formatDate } from '@/lib/utils';
import { MapPin, Clock, Star, Heart, MessageSquare, Share2 } from 'lucide-react';

interface ServiceCardProps {
  service: Service;
}

export default function ServiceCard({ service }: ServiceCardProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  
  // Imagem padrão caso o serviço não tenha imagem
  const imageUrl = service.images && service.images.length > 0
    ? service.images[0]
    : '/images/service-placeholder.jpg';
  
  // Formatar preço com tipo
  const formatPriceWithType = (price: number, priceType: string) => {
    const formattedPrice = formatCurrency(price);
    
    const priceTypeMap: Record<string, string> = {
      'fixed': '',
      'hourly': '/hora',
      'daily': '/dia',
      'monthly': '/mês',
      'negotiable': ' (negociável)'
    };
    
    return `${formattedPrice}${priceTypeMap[priceType] || ''}`;
  };
  
  return (
    <Card className="overflow-hidden hover:shadow-md transition-shadow">
      <div className="relative">
        <div className="aspect-video relative overflow-hidden">
          <Image
            src={imageUrl}
            alt={service.title}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className="object-cover transition-transform hover:scale-105"
          />
        </div>
        
        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-wrap gap-1">
          {service.featured && (
            <Badge className="bg-yellow-500 hover:bg-yellow-600">Destaque</Badge>
          )}
          {service.status === 'published' && service.publishedAt && 
            new Date().getTime() - new Date(service.publishedAt).getTime() < 7 * 24 * 60 * 60 * 1000 && (
            <Badge className="bg-green-500 hover:bg-green-600">Novo</Badge>
          )}
        </div>
        
        {/* Botão de favorito */}
        <button 
          className={`absolute top-2 right-2 p-1.5 rounded-full ${
            isFavorite ? 'bg-red-500 text-white' : 'bg-white/80 text-gray-700'
          }`}
          onClick={() => setIsFavorite(!isFavorite)}
        >
          <Heart className="h-4 w-4" fill={isFavorite ? 'currentColor' : 'none'} />
        </button>
      </div>
      
      <CardContent className="p-4">
        <div className="space-y-2">
          {/* Categoria */}
          <div className="text-sm text-muted-foreground">
            {service.categoryName}
            {service.subcategoryName && ` › ${service.subcategoryName}`}
          </div>
          
          {/* Título */}
          <Link href={`/public/services/${service.id}`}>
            <h3 className="font-semibold text-lg line-clamp-2 hover:text-primary transition-colors">
              {service.title}
            </h3>
          </Link>
          
          {/* Preço */}
          <div className="font-bold text-lg text-primary">
            {formatPriceWithType(service.price, service.priceType)}
          </div>
          
          {/* Localização */}
          {service.address && (
            <div className="flex items-center text-sm text-muted-foreground">
              <MapPin className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
              <span className="truncate">
                {service.address.city}, {service.address.state}
              </span>
            </div>
          )}
          
          {/* Avaliação */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-400 mr-1" fill="currentColor" />
              <span className="font-medium">{service.rating?.toFixed(1) || '0.0'}</span>
              <span className="text-muted-foreground text-sm ml-1">
                ({service.ratingCount || 0} avaliações)
              </span>
            </div>
            
            {/* Data de publicação */}
            <div className="flex items-center text-xs text-muted-foreground">
              <Clock className="h-3 w-3 mr-1" />
              {service.publishedAt ? formatDate(new Date(service.publishedAt)) : 'Não publicado'}
            </div>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="p-4 pt-0 flex justify-between">
        <Button variant="outline" size="sm" asChild>
          <Link href={`/public/services/${service.id}`}>
            Ver detalhes
          </Link>
        </Button>
        
        <div className="flex gap-1">
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <MessageSquare className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <Share2 className="h-4 w-4" />
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
