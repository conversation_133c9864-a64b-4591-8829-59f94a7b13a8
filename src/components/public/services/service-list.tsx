'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { LocationService } from '@/services/location-service';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { Service } from '@/types/models/service';
import { ServiceService } from '@/lib/services/service-service';
import ServiceCard from './service-card';
import ServiceMap from './service-map';
import { Grid, List, MapPin, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface ServiceListProps {
  initialCategory?: string;
  initialLocation?: string;
  initialCoords?: { lat: number; lng: number } | null;
}

export default function ServiceList({
  initialCategory,
  initialLocation,
  initialCoords,
}: ServiceListProps) {
  const searchParams = useSearchParams();

  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [lastDoc, setLastDoc] = useState<any>(null);

  // Filtros
  const category = searchParams.get('category') || initialCategory || '';
  const location = searchParams.get('location') || initialLocation || '';
  const q = searchParams.get('q') || '';
  const minPrice = parseInt(searchParams.get('minPrice') || '0');
  const maxPrice = parseInt(searchParams.get('maxPrice') || '10000');
  const radius = parseInt(searchParams.get('radius') || '50');
  const lat = parseFloat(searchParams.get('lat') || initialCoords?.lat.toString() || '0');
  const lng = parseFloat(searchParams.get('lng') || initialCoords?.lng.toString() || '0');

  // Ordenação
  const [sortBy, setSortBy] = useState('recent');

  // Visualização
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');

  // Carregar serviços
  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        setError(null);

        const serviceService = new ServiceService();

        let result;

        // Buscar serviços com base nos filtros
        if (category) {
          result = await serviceService.listByCategory(category, {
            limit: 12,
            orderByField: getSortField(),
            orderByDirection: getSortDirection(),
          });
        } else if (q) {
          result = await serviceService.search(q, {
            limit: 12,
            orderByField: getSortField(),
            orderByDirection: getSortDirection(),
          });
        } else {
          result = await serviceService.listPublished({
            limit: 12,
            orderByField: getSortField(),
            orderByDirection: getSortDirection(),
          });
        }

        // Filtrar por preço
        let filteredServices = result.items.filter(
          (service) => service.price >= minPrice && service.price <= maxPrice,
        );

        // Filtrar por localização
        if (location) {
          // Normalizar o termo de busca
          const locationService = LocationService.getInstance();
          const normalizedLocation = locationService.normalizeText(location);

          filteredServices = filteredServices.filter((service) => {
            // Verificar se o serviço usa a localização do anunciante
            if (service.useAdvertiserLocation && service.advertiser) {
              // Verificar se o anunciante tem cityNormalized
              if (service.advertiser.address?.cityNormalized) {
                return service.advertiser.address.cityNormalized.includes(normalizedLocation);
              }

              // Fallback para o campo city normalizado manualmente
              if (service.advertiser.address?.city) {
                const advertiserCity = locationService.normalizeText(
                  service.advertiser.address.city,
                );
                return advertiserCity.includes(normalizedLocation);
              }
            }

            return false;
          });
        }

        // Filtrar por proximidade (simulado - em uma implementação real, isso seria feito no backend)
        if (lat && lng) {
          // Aqui seria implementado um cálculo de distância real
          // Por enquanto, apenas simulamos
          filteredServices = filteredServices.filter(() => true);
        }

        setServices(filteredServices);
        setLastDoc(result.lastDoc);
        setHasMore(result.hasMore);
      } catch (err) {
        console.error('Error fetching services:', err);
        setError('Falha ao carregar serviços. Tente novamente mais tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, [category, location, q, minPrice, maxPrice, radius, lat, lng, sortBy]);

  // Carregar mais serviços
  const loadMore = async () => {
    if (!lastDoc || !hasMore) return;

    try {
      setLoading(true);

      const serviceService = new ServiceService();

      let result;

      // Buscar mais serviços com base nos filtros
      if (category) {
        result = await serviceService.listByCategory(category, {
          limit: 12,
          orderByField: getSortField(),
          orderByDirection: getSortDirection(),
          startAfter: lastDoc,
        });
      } else if (q) {
        result = await serviceService.search(q, {
          limit: 12,
          orderByField: getSortField(),
          orderByDirection: getSortDirection(),
          startAfter: lastDoc,
        });
      } else {
        result = await serviceService.listPublished({
          limit: 12,
          orderByField: getSortField(),
          orderByDirection: getSortDirection(),
          startAfter: lastDoc,
        });
      }

      // Filtrar por preço
      let filteredServices = result.items.filter(
        (service) => service.price >= minPrice && service.price <= maxPrice,
      );

      // Filtrar por localização se especificada
      if (location) {
        // Normalizar o termo de busca
        const locationService = LocationService.getInstance();
        const normalizedLocation = locationService.normalizeText(location);

        filteredServices = filteredServices.filter((service) => {
          // Verificar se o serviço usa a localização do anunciante
          if (service.useAdvertiserLocation && service.advertiser) {
            // Verificar se o anunciante tem cityNormalized
            if (service.advertiser.address?.cityNormalized) {
              return service.advertiser.address.cityNormalized.includes(normalizedLocation);
            }

            // Fallback para o campo city normalizado manualmente
            if (service.advertiser.address?.city) {
              const advertiserCity = locationService.normalizeText(service.advertiser.address.city);
              return advertiserCity.includes(normalizedLocation);
            }
          }

          return false;
        });
      }

      // A filtragem por localização já foi feita acima

      setServices((prev) => [...prev, ...filteredServices]);
      setLastDoc(result.lastDoc);
      setHasMore(result.hasMore);
    } catch (err) {
      console.error('Error loading more services:', err);
      setError('Falha ao carregar mais serviços. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  // Obter campo de ordenação
  const getSortField = () => {
    switch (sortBy) {
      case 'recent':
        return 'publishedAt';
      case 'price_asc':
      case 'price_desc':
        return 'price';
      case 'rating':
        return 'rating';
      default:
        return 'publishedAt';
    }
  };

  // Obter direção de ordenação
  const getSortDirection = () => {
    switch (sortBy) {
      case 'price_asc':
        return 'asc';
      case 'price_desc':
      case 'recent':
      case 'rating':
        return 'desc';
      default:
        return 'desc';
    }
  };

  // Renderizar esqueletos de carregamento
  const renderSkeletons = () => {
    return Array.from({ length: 6 }).map((_, index) => (
      <div key={index} className="space-y-3">
        <Skeleton className="h-[200px] w-full rounded-lg" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-6 w-1/2" />
        <Skeleton className="h-4 w-full" />
        <div className="flex justify-between">
          <Skeleton className="h-8 w-24" />
          <Skeleton className="h-8 w-16" />
        </div>
      </div>
    ));
  };

  return (
    <div className="space-y-4">
      {/* Barra de controles */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">
            {category ? 'Serviços em ' + category : 'Todos os Serviços'}
            {location && ` em ${location}`}
          </h2>
          <p className="text-muted-foreground">{services.length} serviços encontrados</p>
        </div>

        <div className="flex gap-2 w-full sm:w-auto">
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Ordenar por" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Mais recentes</SelectItem>
              <SelectItem value="price_asc">Menor preço</SelectItem>
              <SelectItem value="price_desc">Maior preço</SelectItem>
              <SelectItem value="rating">Melhor avaliação</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="icon"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="icon"
              onClick={() => setViewMode('list')}
              className="rounded-none border-l border-r"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'map' ? 'default' : 'ghost'}
              size="icon"
              onClick={() => setViewMode('map')}
              className="rounded-l-none"
            >
              <MapPin className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Mensagem de erro */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Conteúdo */}
      <div>
        {viewMode === 'map' ? (
          <ServiceMap services={services} />
        ) : (
          <div
            className={`grid gap-6 ${
              viewMode === 'grid'
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                : 'grid-cols-1'
            }`}
          >
            {loading && services.length === 0 ? (
              renderSkeletons()
            ) : services.length === 0 ? (
              <div className="col-span-full py-12 text-center">
                <div className="mx-auto w-24 h-24 rounded-full bg-muted flex items-center justify-center mb-4">
                  <AlertCircle className="h-12 w-12 text-muted-foreground" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Nenhum serviço encontrado</h3>
                <p className="text-muted-foreground max-w-md mx-auto">
                  Não encontramos serviços com os filtros selecionados. Tente ajustar seus critérios
                  de busca.
                </p>
              </div>
            ) : (
              services.map((service) => <ServiceCard key={service.id} service={service} />)
            )}
          </div>
        )}

        {/* Botão de carregar mais */}
        {hasMore && services.length > 0 && (
          <div className="mt-8 text-center">
            <Button onClick={loadMore} disabled={loading} variant="outline" size="lg">
              {loading ? 'Carregando...' : 'Carregar mais'}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
