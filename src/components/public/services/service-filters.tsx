'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Search, MapPin, Filter, X } from 'lucide-react';
import { ServiceCategoryService } from '@/lib/services/service-category-service';
import { ServiceCategory } from '@/types/models/service';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetFooter,
  SheetClose,
} from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';

interface ServiceFiltersProps {
  onLocationDetected?: (city: string, coords: { lat: number; lng: number }) => void;
}

export default function ServiceFilters({ onLocationDetected }: ServiceFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);

  // Filtros
  const [searchTerm, setSearchTerm] = useState(searchParams.get('q') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [location, setLocation] = useState(searchParams.get('location') || '');
  const [priceRange, setPriceRange] = useState<[number, number]>([
    parseInt(searchParams.get('minPrice') || '0'),
    parseInt(searchParams.get('maxPrice') || '10000'),
  ]);
  const [radius, setRadius] = useState(parseInt(searchParams.get('radius') || '50'));
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [userCity, setUserCity] = useState<string>('');

  // Carregar categorias
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const categoryService = new ServiceCategoryService();
        const result = await categoryService.listMainCategories();
        setCategories(result);
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Detectar localização do usuário - executado apenas uma vez na montagem do componente
  useEffect(() => {
    // Verificar se já temos a localização do usuário para evitar múltiplas chamadas
    if (userCity || !navigator.geolocation) return;

    // Flag para controlar se o componente ainda está montado
    let isMounted = true;

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        // Verificar se o componente ainda está montado
        if (!isMounted) return;

        const coords = {
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        };
        setUserLocation(coords);

        // Obter nome da cidade a partir das coordenadas usando nosso backend
        try {
          const response = await fetch(`/api/location?lat=${coords.lat}&lng=${coords.lng}`, {
            cache: 'no-store', // Evitar cache no cliente
          });
          const data = await response.json();

          // Verificar se o componente ainda está montado
          if (!isMounted) return;

          // Extrair cidade dos dados retornados
          const city =
            data.address?.city ||
            data.address?.town ||
            data.address?.village ||
            data.address?.municipality ||
            '';

          if (city) {
            setUserCity(city);

            // Notificar o componente pai sobre a localização detectada
            if (onLocationDetected) {
              onLocationDetected(city, coords);
            }
          }
        } catch (error) {
          console.error('Erro ao obter nome da cidade:', error);
        }
      },
      (error) => {
        console.error('Erro ao obter localização:', error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      },
    );

    // Função de limpeza para evitar memory leaks
    return () => {
      isMounted = false;
    };
  }, []); // Dependência vazia para executar apenas uma vez

  // Contar filtros ativos
  useEffect(() => {
    let count = 0;
    if (selectedCategory) count++;
    if (location) count++;
    if (priceRange[0] > 0 || priceRange[1] < 10000) count++;
    if (radius !== 50) count++;

    setActiveFiltersCount(count);
  }, [selectedCategory, location, priceRange, radius]);

  // Aplicar filtros
  const applyFilters = () => {
    const params = new URLSearchParams();

    if (searchTerm) params.set('q', searchTerm);
    if (selectedCategory) params.set('category', selectedCategory);
    if (location) params.set('location', location);
    if (priceRange[0] > 0) params.set('minPrice', priceRange[0].toString());
    if (priceRange[1] < 10000) params.set('maxPrice', priceRange[1].toString());
    if (radius !== 50) params.set('radius', radius.toString());
    if (userLocation) {
      params.set('lat', userLocation.lat.toString());
      params.set('lng', userLocation.lng.toString());
    }

    router.push(`/public/services?${params.toString()}`);
  };

  // Limpar filtros
  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setLocation('');
    setPriceRange([0, 10000]);
    setRadius(50);
    router.push('/public/services');
  };

  // Aplicar localização detectada
  const applyDetectedLocation = () => {
    if (userCity) {
      setLocation(userCity);
    }
  };

  // Formatar preço para exibição
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(price);
  };

  return (
    <div className="space-y-4">
      {/* Barra de pesquisa principal */}
      <Card className="shadow-md">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row md:flex-row lg:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="O que você está procurando?"
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="relative flex-grow">
              <MapPin className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Localização (cidade, bairro)"
                className="pl-8"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={applyFilters} className="flex-grow md:flex-grow-0">
                <Search className="mr-2 h-4 w-4" />
                Buscar
              </Button>

              {/* Botão de filtros */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" className="relative">
                    <Filter className="h-4 w-4 mr-2" />
                    Filtros
                    {activeFiltersCount > 0 && (
                      <Badge
                        variant="secondary"
                        className="ml-2 h-5 w-5 p-0 flex items-center justify-center"
                      >
                        {activeFiltersCount}
                      </Badge>
                    )}
                  </Button>
                </SheetTrigger>
                <SheetContent className="p-4 overflow-y-auto" side="right">
                  <SheetHeader>
                    <SheetTitle>Filtros</SheetTitle>
                    <SheetDescription>Refine sua busca com os filtros abaixo</SheetDescription>
                  </SheetHeader>

                  <div className="py-6 space-y-6">
                    {/* Categorias */}
                    <div className="space-y-3">
                      <h3 className="font-medium">Categorias</h3>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger>
                          <SelectValue placeholder="Todas as categorias" />
                        </SelectTrigger>
                        <SelectContent>
                          {/* <SelectItem value="">Todas as categorias</SelectItem> */}
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Preço */}
                    <div className="space-y-3">
                      <h3 className="font-medium">Preço</h3>
                      <div className="space-y-4">
                        <Slider
                          value={priceRange}
                          min={0}
                          max={10000}
                          step={100}
                          onValueChange={(value) => setPriceRange(value as [number, number])}
                        />
                        <div className="flex justify-between text-sm">
                          <span>{formatPrice(priceRange[0])}</span>
                          <span>{formatPrice(priceRange[1])}</span>
                        </div>
                      </div>
                    </div>

                    {/* Distância */}
                    <div className="space-y-3">
                      <h3 className="font-medium">Distância</h3>
                      <div className="space-y-4">
                        <Slider
                          value={[radius]}
                          min={5}
                          max={100}
                          step={5}
                          onValueChange={(value) => setRadius(value[0])}
                        />
                        <div className="flex justify-between text-sm">
                          <span>5 km</span>
                          <span>{radius} km</span>
                          <span>100 km</span>
                        </div>
                      </div>
                    </div>

                    {/* Avaliação */}
                    <div className="space-y-3">
                      <h3 className="font-medium">Avaliação</h3>
                      <div className="space-y-2">
                        {[5, 4, 3, 2, 1].map((rating) => (
                          <div key={rating} className="flex items-center">
                            <input type="checkbox" id={`rating-${rating}`} className="mr-2" />
                            <Label htmlFor={`rating-${rating}`} className="flex items-center">
                              {Array.from({ length: rating }).map((_, i) => (
                                <svg
                                  key={i}
                                  className="w-4 h-4 text-yellow-400"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                              ))}
                              {Array.from({ length: 5 - rating }).map((_, i) => (
                                <svg
                                  key={i}
                                  className="w-4 h-4 text-gray-300"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                              ))}
                              <span className="ml-1">e acima</span>
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  <SheetFooter className="flex-row gap-3 sm:justify-between">
                    <Button variant="outline" onClick={clearFilters} className="flex-1">
                      <X className="h-4 w-4 mr-2" />
                      Limpar
                    </Button>
                    <SheetClose asChild>
                      <Button onClick={applyFilters} className="flex-1">
                        <Search className="h-4 w-4 mr-2" />
                        Aplicar
                      </Button>
                    </SheetClose>
                  </SheetFooter>
                </SheetContent>
              </Sheet>

              <Button variant="outline" onClick={clearFilters} size="icon">
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Localização detectada */}
          {userCity && !location && (
            <div className="flex items-center gap-2 mt-3 p-2 bg-primary/5 rounded border border-primary/10">
              <MapPin className="h-4 w-4 text-primary" />
              <span className="text-sm">
                Localização detectada: <strong>{userCity}</strong>
              </span>
              <Button
                variant="outline"
                size="sm"
                className="ml-auto text-xs h-7 px-2"
                onClick={(e) => {
                  e.stopPropagation();
                  applyDetectedLocation();
                  applyFilters();
                }}
              >
                Usar esta localização
              </Button>
            </div>
          )}

          {/* Filtros ativos */}
          {activeFiltersCount > 0 && (
            <div className="flex flex-wrap gap-2 mt-3">
              {selectedCategory && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Categoria:{' '}
                  {categories.find((c) => c.id === selectedCategory)?.name || selectedCategory}
                  <button
                    type="button"
                    className="ml-1 inline-flex items-center justify-center rounded-full hover:bg-muted cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedCategory('');
                    }}
                    aria-label="Remover filtro de categoria"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {location && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Local: {location}
                  <button
                    type="button"
                    className="ml-1 inline-flex items-center justify-center rounded-full hover:bg-muted cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      setLocation('');
                    }}
                    aria-label="Remover filtro de localização"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {(priceRange[0] > 0 || priceRange[1] < 10000) && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Preço: {formatPrice(priceRange[0])} - {formatPrice(priceRange[1])}
                  <button
                    type="button"
                    className="ml-1 inline-flex items-center justify-center rounded-full hover:bg-muted cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      setPriceRange([0, 10000]);
                    }}
                    aria-label="Remover filtro de preço"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
              {radius !== 50 && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Distância: {radius}km
                  <button
                    type="button"
                    className="ml-1 inline-flex items-center justify-center rounded-full hover:bg-muted cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      setRadius(50);
                    }}
                    aria-label="Remover filtro de distância"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
