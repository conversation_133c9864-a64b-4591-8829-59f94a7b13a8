import Link from 'next/link';

export default function Logo({ full = true, direction = 'row', size = 10 }) {
  if (!full) {
    return <img src="/logo.png" alt="JáVai" className={`w-${size} h-${size}`} />;
  }

  return (
    <Link href="/" className="no-underline text-primary hover:text-primary/90">
      <div className={`flex flex-${direction} items-center justify-center p-2 cursor-pointer`}>
        <img src="/logo.png" alt="JáVai" className={`w-${size} h-${size}`} />
        {full && <span className="font-bold text-xl text-primary dark:text-white">JáVai</span>}
      </div>
    </Link>
  );
}
