// src/components/subscription/ad-limit-warning.tsx
"use client";

import { useEffect, useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { useSubscription } from '@/hooks/use-subscription';

interface AdLimitWarningProps {
  module: keyof AdLimits;
  currentCount: number;
}

export default function AdLimitWarning({
  module,
  currentCount,
}: AdLimitWarningProps) {
  const { plan, loading } = useSubscription({ module, showToast: false });
  const [remainingAds, setRemainingAds] = useState<number | null>(null);
  const [showWarning, setShowWarning] = useState(false);
  
  useEffect(() => {
    if (plan && !loading) {
      const limit = plan.adLimits[module];
      const remaining = limit - currentCount;
      setRemainingAds(remaining);
      
      // Show warning if less than 20% of limit remaining or less than 3 ads
      setShowWarning(remaining <= Math.max(Math.floor(limit * 0.2), 3));
    }
  }, [plan, loading, module, currentCount]);
  
  if (loading || remainingAds === null || !showWarning) {
    return null;
  }
  
  return (
    <Alert variant="warning" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Limite de anúncios</AlertTitle>
      <AlertDescription className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <span>
          {remainingAds > 0
            ? `Você tem apenas ${remainingAds} anúncio${remainingAds !== 1 ? 's' : ''} restante${remainingAds !== 1 ? 's' : ''} para ${getModuleName(module)}.`
            : `Você atingiu o limite de anúncios para ${getModuleName(module)}.`}
        </span>
        <Button asChild size="sm" variant="outline">
          <Link href="/dashboard/subscription">
            Fazer Upgrade
          </Link>
        </Button>
      </AlertDescription>
    </Alert>
  );
}

// Helper function to get module name in Portuguese
function getModuleName(module: string): string {
  switch (module) {
    case 'services':
      return 'Serviços';
    case 'properties':
      return 'Imóveis';
    case 'vehicles':
      return 'Veículos';
    case 'products':
      return 'Produtos';
    default:
      return module;
  }
}
