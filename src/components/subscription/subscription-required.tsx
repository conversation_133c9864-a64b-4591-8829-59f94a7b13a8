// src/components/subscription/subscription-required.tsx
"use client";

import { ReactNode, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Subscription, Plan } from '@/types/subscription';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Loader2 } from 'lucide-react';

interface SubscriptionRequiredProps {
  children: ReactNode;
  module?: keyof ModuleAccess;
  fallback?: ReactNode;
}

export default function SubscriptionRequired({
  children,
  module,
  fallback,
}: SubscriptionRequiredProps) {
  const router = useRouter();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [plan, setPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  
  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setLoading(true);
        
        // Fetch user's active subscription
        const response = await fetch('/api/subscription');
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch subscription');
        }
        
        if (!data.subscription) {
          setHasAccess(false);
          return;
        }
        
        setSubscription(data.subscription);
        setPlan(data.plan);
        
        // Check module access if specified
        if (module && !data.plan.moduleAccess[module]) {
          setHasAccess(false);
          return;
        }
        
        setHasAccess(true);
      } catch (error) {
        console.error('Error fetching subscription:', error);
        toast.error('Ocorreu um erro ao verificar sua assinatura. Tente novamente.');
        setHasAccess(false);
      } finally {
        setLoading(false);
      }
    };
    
    fetchSubscription();
  }, [module]);
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Assinatura Necessária</CardTitle>
          <CardDescription>
            {!subscription
              ? 'Você precisa ter uma assinatura ativa para acessar este recurso.'
              : `Seu plano atual não inclui acesso ao módulo de ${getModuleName(module)}.`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            {!subscription
              ? 'Escolha um plano que atenda às suas necessidades e comece a aproveitar todos os recursos da plataforma.'
              : 'Faça upgrade do seu plano para acessar mais recursos e módulos.'}
          </p>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full">
            <Link href={!subscription ? '/dashboard/subscription/plans' : '/dashboard/subscription'}>
              {!subscription ? 'Ver Planos' : 'Gerenciar Assinatura'}
            </Link>
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  return <>{children}</>;
}

// Helper function to get module name in Portuguese
function getModuleName(module?: string): string {
  if (!module) return '';
  
  switch (module) {
    case 'services':
      return 'Serviços';
    case 'properties':
      return 'Imóveis';
    case 'vehicles':
      return 'Veículos';
    case 'products':
      return 'Produtos';
    default:
      return module;
  }
}
