// src/components/subscription/plan-card.tsx
"use client";

import { useState } from 'react';
import { Plan } from '@/types/subscription';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface PlanCardProps {
  plan: Plan;
}

export default function PlanCard({ plan }: PlanCardProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  
  const handleSubscribe = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/stripe/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId: plan.id }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }
      
      // Redirect to Stripe Checkout
      window.location.href = data.url;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error('Ocorreu um erro ao processar sua solicitação. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Card className={`flex flex-col ${plan.isPopular ? 'border-primary shadow-lg' : ''}`}>
      {plan.isPopular && (
        <div className="bg-primary text-primary-foreground text-center py-1 text-sm font-medium">
          Mais Popular
        </div>
      )}
      
      <CardHeader>
        <CardTitle className="text-xl">{plan.name}</CardTitle>
        <CardDescription>{plan.description}</CardDescription>
      </CardHeader>
      
      <CardContent className="flex-1">
        <div className="mb-4">
          <span className="text-3xl font-bold">
            {new Intl.NumberFormat('pt-BR', {
              style: 'currency',
              currency: 'BRL',
            }).format(plan.price / 100)}
          </span>
          <span className="text-muted-foreground">
            /{plan.interval === 'month' ? 'mês' : 'ano'}
          </span>
        </div>
        
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Módulos incluídos:</h4>
          <ul className="space-y-1">
            {plan.moduleAccess.services && (
              <li className="flex items-center text-sm">
                <Check className="h-4 w-4 mr-2 text-green-500" />
                Serviços
              </li>
            )}
            {plan.moduleAccess.properties && (
              <li className="flex items-center text-sm">
                <Check className="h-4 w-4 mr-2 text-green-500" />
                Imóveis
              </li>
            )}
            {plan.moduleAccess.vehicles && (
              <li className="flex items-center text-sm">
                <Check className="h-4 w-4 mr-2 text-green-500" />
                Veículos
              </li>
            )}
            {plan.moduleAccess.products && (
              <li className="flex items-center text-sm">
                <Check className="h-4 w-4 mr-2 text-green-500" />
                Produtos
              </li>
            )}
          </ul>
        </div>
        
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium">Limites de anúncios:</h4>
          <ul className="space-y-1">
            {plan.moduleAccess.services && (
              <li className="flex items-center text-sm">
                <Check className="h-4 w-4 mr-2 text-green-500" />
                {plan.adLimits.services} anúncios de serviços
              </li>
            )}
            {plan.moduleAccess.properties && (
              <li className="flex items-center text-sm">
                <Check className="h-4 w-4 mr-2 text-green-500" />
                {plan.adLimits.properties} anúncios de imóveis
              </li>
            )}
            {plan.moduleAccess.vehicles && (
              <li className="flex items-center text-sm">
                <Check className="h-4 w-4 mr-2 text-green-500" />
                {plan.adLimits.vehicles} anúncios de veículos
              </li>
            )}
            {plan.moduleAccess.products && (
              <li className="flex items-center text-sm">
                <Check className="h-4 w-4 mr-2 text-green-500" />
                {plan.adLimits.products} anúncios de produtos
              </li>
            )}
          </ul>
        </div>
        
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium">Recursos incluídos:</h4>
          <ul className="space-y-1">
            {plan.features.map((feature) => (
              <li key={feature.id} className="flex items-center text-sm">
                {feature.included ? (
                  <Check className="h-4 w-4 mr-2 text-green-500" />
                ) : (
                  <div className="h-4 w-4 mr-2" />
                )}
                {feature.name}
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
      
      <CardFooter>
        <Button 
          onClick={handleSubscribe} 
          disabled={isLoading} 
          className="w-full"
          variant={plan.isPopular ? "default" : "outline"}
        >
          {isLoading ? 'Carregando...' : 'Assinar Plano'}
        </Button>
      </CardFooter>
    </Card>
  );
}
