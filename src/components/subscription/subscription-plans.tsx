'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Check, X, AlertCircle, Loader2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';
import { Plan } from '@/types/models/plan';

interface SubscriptionPlansProps {
  userId: string;
  activeSubscription: any | null;
  plans: Plan[];
}

export default function SubscriptionPlans({
  userId,
  activeSubscription,
  plans: initialPlans,
}: SubscriptionPlansProps) {
  const router = useRouter();
  const [interval, setInterval] = useState<'month' | 'year'>('year');
  const [loading, setLoading] = useState<string | null>(null);
  const [plans, setPlans] = useState<Plan[]>(
    initialPlans.filter((plan) => plan.interval === interval),
  );

  const handleIntervalChange = (value: string) => {
    setInterval(value as 'month' | 'year');
    setPlans(initialPlans.filter((plan) => plan.interval === value));
  };

  const handleSubscribe = async (planId: string) => {
    try {
      setLoading(planId);
      console.log('Iniciando assinatura para o plano:', planId);

      // Chamar a API para criar uma checkout session
      const response = await fetch('/api/subscription/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId }),
      });

      console.log('Resposta da API:', response.status, response.statusText);
      const data = await response.json();
      console.log('Dados da resposta:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao processar assinatura');
      }

      // Redirecionar para a URL de checkout do Stripe
      if (data.url) {
        console.log('Redirecionando para:', data.url);
        window.location.href = data.url;
      } else {
        throw new Error('URL de checkout não encontrada');
      }
    } catch (error) {
      console.error('Erro ao processar assinatura:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro ao processar assinatura';
      toast.error(`Erro: ${errorMessage}. Tente novamente.`);
      setLoading(null);
    }
  };

  const handleManageSubscription = async () => {
    try {
      setLoading('manage');
      console.log('Iniciando gerenciamento de assinatura');

      // Chamar a API para criar uma sessão do portal de clientes do Stripe
      const response = await fetch('/api/subscription/create-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      console.log('Resposta da API:', response.status, response.statusText);
      const data = await response.json();
      console.log('Dados da resposta:', data);

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao acessar portal de gerenciamento');
      }

      // Redirecionar para a URL do portal do Stripe
      if (data.url) {
        console.log('Redirecionando para:', data.url);
        window.location.href = data.url;
      } else {
        throw new Error('URL do portal não encontrada');
      }
    } catch (error) {
      console.error('Erro ao gerenciar assinatura:', error);
      const errorMessage = error instanceof Error ? error.message : 'Erro ao gerenciar assinatura';
      toast.error(`Erro: ${errorMessage}. Tente novamente.`);
      setLoading(null);
    }
  };

  // Se o usuário já tem uma assinatura ativa, mostrar informações da assinatura atual
  if (activeSubscription) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Sua Assinatura Atual</CardTitle>
          <CardDescription>
            Você já possui uma assinatura ativa. Confira os detalhes abaixo.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-semibold text-lg">
                  {activeSubscription.planName || 'Plano Profissional'}
                </h3>
                <p className="text-muted-foreground">
                  {activeSubscription.interval === 'year' ? 'Anual' : 'Mensal'}
                </p>
              </div>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Ativo
              </Badge>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="text-muted-foreground">Próxima cobrança</p>
                <p className="font-medium">
                  {activeSubscription.currentPeriodEnd
                    ? new Date(activeSubscription.currentPeriodEnd).toLocaleDateString('pt-BR')
                    : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR')}
                </p>
              </div>
              <div className="text-right">
                <p className="text-muted-foreground">Valor</p>
                <p className="font-medium">{formatCurrency(activeSubscription.amount || 9990)}</p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/advertiser/dashboard')}>
            Ir para o Dashboard
          </Button>
          <Button onClick={handleManageSubscription} disabled={loading === 'manage'}>
            {loading === 'manage' ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Carregando...
              </>
            ) : (
              'Gerenciar Assinatura'
            )}
          </Button>
        </CardFooter>
      </Card>
    );
  }

  // Se não houver planos disponíveis
  if (!plans || plans.length === 0) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Planos Indisponíveis</CardTitle>
          <CardDescription>No momento, não há planos de assinatura disponíveis.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              Por favor, tente novamente mais tarde ou entre em contato com o suporte.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-5xl mx-auto space-y-8">
      <Tabs defaultValue="year" className="w-full" onValueChange={handleIntervalChange}>
        <div className="flex justify-center mb-8">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="month">Mensal</TabsTrigger>
            <TabsTrigger value="year" className="flex items-center gap-2 justify-center">
              Anual
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Economize 20%
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="month" className="space-y-8">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mx-auto">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={`flex flex-col ${plan.popular ? 'border-primary shadow-md' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 z-10 mt-2 mr-2">
                    <Badge className="bg-primary">Popular</Badge>
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="mt-4">
                    <span className="text-3xl font-bold">{formatCurrency(plan.price)}</span>
                    <span className="text-muted-foreground">/mês</span>
                  </div>
                </CardHeader>
                <CardContent className="flex-1">
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        {feature.included ? (
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                        ) : (
                          <X className="h-4 w-4 text-red-500 mr-2" />
                        )}
                        <span className={feature.included ? '' : 'text-muted-foreground'}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={loading === plan.id}
                    variant={plan.popular ? 'default' : 'outline'}
                  >
                    {loading === plan.id ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processando...
                      </>
                    ) : (
                      'Assinar Agora'
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="year" className="space-y-8">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mx-auto">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={`flex flex-col ${plan.popular ? 'border-primary shadow-md' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 z-10 mt-2 mr-2">
                    <Badge className="bg-primary">Popular</Badge>
                  </div>
                )}
                <CardHeader>
                  <CardTitle>{plan.name}</CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="mt-4">
                    <span className="text-3xl font-bold">{formatCurrency(plan.price)}</span>
                    <span className="text-muted-foreground">/ano</span>
                    <div className="text-sm text-muted-foreground mt-1">
                      Equivalente a {formatCurrency(Math.round(plan.price / 12))}/mês
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="flex-1">
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        {feature.included ? (
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                        ) : (
                          <X className="h-4 w-4 text-red-500 mr-2" />
                        )}
                        <span className={feature.included ? '' : 'text-muted-foreground'}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={loading === plan.id}
                    variant={plan.popular ? 'default' : 'outline'}
                  >
                    {loading === plan.id ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processando...
                      </>
                    ) : (
                      'Assinar Agora'
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* <div className="bg-slate-50 dark:bg-slate-800 border rounded-lg p-6 max-w-3xl mx-auto">
        <div className="flex items-start gap-4">
          <AlertCircle className="h-6 w-6 text-primary mt-1" />
          <div>
            <h3 className="font-semibold text-lg mb-2">Precisa de um plano personalizado?</h3>
            <p className="text-muted-foreground mb-4">
              Se você precisa de um plano com mais recursos ou limites maiores, entre em contato
              conosco para discutir um plano personalizado para o seu negócio.
            </p>
            <Button variant="outline">Fale Conosco</Button>
          </div>
        </div>
      </div> */}
    </div>
  );
}
