'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Check, X, AlertCircle, Loader2 } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';
import { toast } from 'sonner';
import { Plan } from '@/types/models/plan';

// Importamos o tipo Plan diretamente do modelo

interface SubscriptionPlansProps {
  userId: string;
  activeSubscription: any | null;
  plans: Plan[];
}
    name: 'Básico',
    description: 'Ideal para quem está começando',
    price: 4990, // R$ 49,90
    interval: 'month',
    popular: false,
    modules: {
      services: true,
      properties: false,
      vehicles: false,
      products: false,
    },
    limits: {
      services: 5,
      properties: 0,
      vehicles: 0,
      products: 0,
      featured: 1,
    },
    features: [
      { name: 'Até 5 serviços ativos', included: true },
      { name: '1 anúncio em destaque', included: true },
      { name: 'Estatísticas básicas', included: true },
      { name: 'Suporte por email', included: true },
      { name: 'Imóveis', included: false },
      { name: 'Veículos', included: false },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
  },
  {
    id: 'pro-monthly',
    name: 'Profissional',
    description: 'Para profissionais e pequenas empresas',
    price: 9990, // R$ 99,90
    interval: 'month',
    popular: true,
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: false,
    },
    limits: {
      services: 20,
      properties: 10,
      vehicles: 10,
      products: 0,
      featured: 5,
    },
    features: [
      { name: 'Até 20 serviços ativos', included: true },
      { name: 'Até 10 imóveis ativos', included: true },
      { name: 'Até 10 veículos ativos', included: true },
      { name: '5 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte por email e chat', included: true },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
  },
  {
    id: 'business-monthly',
    name: 'Empresarial',
    description: 'Para empresas e grandes anunciantes',
    price: 19990, // R$ 199,90
    interval: 'month',
    popular: false,
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: true,
    },
    limits: {
      services: 50,
      properties: 30,
      vehicles: 30,
      products: 100,
      featured: 15,
    },
    features: [
      { name: 'Até 50 serviços ativos', included: true },
      { name: 'Até 30 imóveis ativos', included: true },
      { name: 'Até 30 veículos ativos', included: true },
      { name: 'Até 100 produtos ativos', included: true },
      { name: '15 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte prioritário', included: true },
      { name: 'API de integração', included: true },
    ],
  },
  {
    id: 'basic-yearly',
    name: 'Básico',
    description: 'Ideal para quem está começando',
    price: 47900, // R$ 479,00 (equivalente a R$ 39,92/mês)
    interval: 'year',
    popular: false,
    modules: {
      services: true,
      properties: false,
      vehicles: false,
      products: false,
    },
    limits: {
      services: 5,
      properties: 0,
      vehicles: 0,
      products: 0,
      featured: 1,
    },
    features: [
      { name: 'Até 5 serviços ativos', included: true },
      { name: '1 anúncio em destaque', included: true },
      { name: 'Estatísticas básicas', included: true },
      { name: 'Suporte por email', included: true },
      { name: 'Imóveis', included: false },
      { name: 'Veículos', included: false },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
  },
  {
    id: 'pro-yearly',
    name: 'Profissional',
    description: 'Para profissionais e pequenas empresas',
    price: 95900, // R$ 959,00 (equivalente a R$ 79,92/mês)
    interval: 'year',
    popular: true,
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: false,
    },
    limits: {
      services: 20,
      properties: 10,
      vehicles: 10,
      products: 0,
      featured: 5,
    },
    features: [
      { name: 'Até 20 serviços ativos', included: true },
      { name: 'Até 10 imóveis ativos', included: true },
      { name: 'Até 10 veículos ativos', included: true },
      { name: '5 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte por email e chat', included: true },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
  },
  {
    id: 'business-yearly',
    name: 'Empresarial',
    description: 'Para empresas e grandes anunciantes',
    price: 191900, // R$ 1.919,00 (equivalente a R$ 159,92/mês)
    interval: 'year',
    popular: false,
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: true,
    },
    limits: {
      services: 50,
      properties: 30,
      vehicles: 30,
      products: 100,
      featured: 15,
    },
    features: [
      { name: 'Até 50 serviços ativos', included: true },
      { name: 'Até 30 imóveis ativos', included: true },
      { name: 'Até 30 veículos ativos', included: true },
      { name: 'Até 100 produtos ativos', included: true },
      { name: '15 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte prioritário', included: true },
      { name: 'API de integração', included: true },
    ],
  },
];

interface SubscriptionPlansProps {
  userId: string;
  activeSubscription: any | null;
}

export default function SubscriptionPlans({ userId, activeSubscription }: SubscriptionPlansProps) {
  const router = useRouter();
  const [interval, setInterval] = useState<'month' | 'year'>('month');
  const [loading, setLoading] = useState<string | null>(null);
  const [plans, setPlans] = useState<Plan[]>([]);

  useEffect(() => {
    // Em uma implementação real, buscaríamos os planos do banco de dados
    // Por enquanto, usamos os planos mockados
    const filteredPlans = mockPlans.filter((plan) => plan.interval === interval);
    setPlans(filteredPlans);
  }, [interval]);

  const handleSubscribe = async (planId: string) => {
    try {
      setLoading(planId);

      // Chamar a API para criar uma checkout session
      const response = await fetch('/api/subscription/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ planId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao processar assinatura');
      }

      // Redirecionar para a URL de checkout do Stripe
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('URL de checkout não encontrada');
      }
    } catch (error) {
      console.error('Erro ao processar assinatura:', error);
      toast.error('Erro ao processar assinatura. Tente novamente.');
      setLoading(null);
    }
  };

  const handleManageSubscription = async () => {
    try {
      setLoading('manage');

      // Chamar a API para criar uma sessão do portal de clientes do Stripe
      const response = await fetch('/api/subscription/create-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Erro ao acessar portal de gerenciamento');
      }

      // Redirecionar para a URL do portal do Stripe
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('URL do portal não encontrada');
      }
    } catch (error) {
      console.error('Erro ao gerenciar assinatura:', error);
      toast.error('Erro ao gerenciar assinatura. Tente novamente.');
      setLoading(null);
    }
  };

  // Se o usuário já tem uma assinatura ativa, mostrar informações da assinatura atual
  if (activeSubscription) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Sua Assinatura Atual</CardTitle>
          <CardDescription>
            Você já possui uma assinatura ativa. Confira os detalhes abaixo.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-semibold text-lg">
                  Plano {activeSubscription.planName || 'Profissional'}
                </h3>
                <p className="text-muted-foreground">
                  {activeSubscription.interval === 'year' ? 'Anual' : 'Mensal'}
                </p>
              </div>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                Ativo
              </Badge>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="text-muted-foreground">Próxima cobrança</p>
                <p className="font-medium">
                  {activeSubscription.currentPeriodEnd
                    ? new Date(activeSubscription.currentPeriodEnd).toLocaleDateString('pt-BR')
                    : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR')}
                </p>
              </div>
              <div className="text-right">
                <p className="text-muted-foreground">Valor</p>
                <p className="font-medium">{formatCurrency(activeSubscription.amount || 9990)}</p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={() => router.push('/advertiser/dashboard')}>
            Ir para o Dashboard
          </Button>
          <Button onClick={handleManageSubscription} disabled={loading === 'manage'}>
            {loading === 'manage' ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Carregando...
              </>
            ) : (
              'Gerenciar Assinatura'
            )}
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-5xl mx-auto space-y-8">
      <Tabs
        defaultValue="month"
        className="w-full"
        onValueChange={(value) => setInterval(value as 'month' | 'year')}
      >
        <div className="flex justify-center mb-8">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="month">Mensal</TabsTrigger>
            <TabsTrigger value="year">
              Anual
              <Badge variant="outline" className="ml-2 bg-green-50 text-green-700 border-green-200">
                Economize 20%
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="month" className="space-y-8">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mx-auto px-4">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={`flex flex-col ${plan.popular ? 'border-primary shadow-md' : ''} m`}
              >
                <CardHeader>
                  <CardTitle>
                    <div className="flex items-center justify-between">
                      {plan.name}
                      {plan.popular && (
                        <Badge className="bg-primary text-primary-foreground">Popular</Badge>
                      )}
                    </div>
                  </CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="mt-4">
                    <span className="text-3xl font-bold">{formatCurrency(plan.price)}</span>
                    <span className="text-muted-foreground">/mês</span>
                  </div>
                </CardHeader>
                <CardContent className="flex-1">
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        {feature.included ? (
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                        ) : (
                          <X className="h-4 w-4 text-red-500 mr-2" />
                        )}
                        <span className={feature.included ? '' : 'text-muted-foreground'}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={loading === plan.id}
                    variant={plan.popular ? 'default' : 'outline'}
                  >
                    {loading === plan.id ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processando...
                      </>
                    ) : (
                      'Assinar Agora'
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="year" className="space-y-8">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mx-auto p">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={`flex flex-col ${plan.popular ? 'border-primary shadow-md' : ''}`}
              >
                <CardHeader>
                  <CardTitle>
                    <div className="flex items-center justify-between">
                      {plan.name}
                      {plan.popular && (
                        <Badge className="bg-primary text-primary-foreground">Popular</Badge>
                      )}
                    </div>
                  </CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="mt-4">
                    <span className="text-3xl font-bold">{formatCurrency(plan.price)}</span>
                    <span className="text-muted-foreground">/ano</span>
                    <div className="text-sm text-muted-foreground mt-1">
                      Equivalente a {formatCurrency(Math.round(plan.price / 12))}/mês
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="flex-1">
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        {feature.included ? (
                          <Check className="h-4 w-4 text-green-500 mr-2" />
                        ) : (
                          <X className="h-4 w-4 text-red-500 mr-2" />
                        )}
                        <span className={feature.included ? '' : 'text-muted-foreground'}>
                          {feature.name}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    onClick={() => handleSubscribe(plan.id)}
                    disabled={loading === plan.id}
                    variant={plan.popular ? 'default' : 'outline'}
                  >
                    {loading === plan.id ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processando...
                      </>
                    ) : (
                      'Assinar Agora'
                    )}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      <div className="bg-slate-50 border rounded-lg p-6 max-w-3xl mx-auto">
        <div className="flex items-start gap-4">
          <AlertCircle className="h-6 w-6 text-primary mt-1" />
          <div>
            <h3 className="font-semibold text-lg mb-2">Precisa de um plano personalizado?</h3>
            <p className="text-muted-foreground mb-4">
              Se você precisa de um plano com mais recursos ou limites maiores, entre em contato
              conosco para discutir um plano personalizado para o seu negócio.
            </p>
            <Button variant="outline">Fale Conosco</Button>
          </div>
        </div>
      </div>
    </div>
  );
}
