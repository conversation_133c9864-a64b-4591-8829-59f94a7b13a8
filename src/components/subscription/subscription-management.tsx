// src/components/subscription/subscription-management.tsx
"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Subscription } from '@/types/subscription';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface SubscriptionManagementProps {
  subscription: Subscription;
  cancelAtPeriodEnd: boolean;
}

export default function SubscriptionManagement({ 
  subscription, 
  cancelAtPeriodEnd 
}: SubscriptionManagementProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  
  const handleManageSubscription = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/stripe/portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to create portal session');
      }
      
      // Redirect to Stripe Portal
      window.location.href = data.url;
    } catch (error) {
      console.error('Error creating portal session:', error);
      toast.error('Ocorreu um erro ao acessar o portal de gerenciamento. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="w-full">
      {cancelAtPeriodEnd ? (
        <div className="text-center mb-4">
          <p className="text-yellow-600 font-medium">
            Sua assinatura será cancelada ao final do período atual.
          </p>
        </div>
      ) : null}
      
      <Button 
        onClick={handleManageSubscription} 
        disabled={isLoading} 
        className="w-full"
      >
        {isLoading ? 'Carregando...' : 'Gerenciar Assinatura'}
      </Button>
      
      <p className="text-xs text-center text-muted-foreground mt-2">
        Você será redirecionado para o portal de gerenciamento do Stripe.
      </p>
    </div>
  );
}
