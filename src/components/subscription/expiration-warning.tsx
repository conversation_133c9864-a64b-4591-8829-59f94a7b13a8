// src/components/subscription/expiration-warning.tsx
"use client";

import { useEffect, useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { useSubscription } from '@/hooks/use-subscription';
import { differenceInDays } from 'date-fns';

export default function ExpirationWarning() {
  const { subscription, loading } = useSubscription({ showToast: false });
  const [daysRemaining, setDaysRemaining] = useState<number | null>(null);
  const [showWarning, setShowWarning] = useState(false);
  
  useEffect(() => {
    if (subscription && !loading) {
      // Calculate days remaining until subscription ends
      const days = differenceInDays(
        subscription.currentPeriodEnd,
        new Date()
      );
      
      setDaysRemaining(days);
      
      // Show warning if subscription is ending in 7 days or less
      // or if subscription is set to cancel at period end
      setShowWarning(days <= 7 || subscription.cancelAtPeriodEnd);
    }
  }, [subscription, loading]);
  
  if (loading || daysRemaining === null || !showWarning) {
    return null;
  }
  
  return (
    <Alert variant="warning" className="mb-4">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>
        {subscription?.cancelAtPeriodEnd
          ? 'Assinatura será cancelada'
          : 'Assinatura expirando em breve'}
      </AlertTitle>
      <AlertDescription className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <span>
          {subscription?.cancelAtPeriodEnd
            ? `Sua assinatura será cancelada em ${daysRemaining} dia${daysRemaining !== 1 ? 's' : ''}.`
            : `Sua assinatura expira em ${daysRemaining} dia${daysRemaining !== 1 ? 's' : ''}.`}
        </span>
        <Button asChild size="sm" variant="outline">
          <Link href="/dashboard/subscription">
            {subscription?.cancelAtPeriodEnd ? 'Reativar Assinatura' : 'Renovar Agora'}
          </Link>
        </Button>
      </AlertDescription>
    </Alert>
  );
}
