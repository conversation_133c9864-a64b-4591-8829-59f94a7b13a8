// src/components/admin/plan-table.tsx
"use client";

import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { 
  MoreHorizontal, 
  Eye,
  Edit,
  Trash,
  Check,
  X
} from 'lucide-react';
import { Plan } from '@/types/subscription';

// Dados de exemplo para demonstração
const mockPlans: Plan[] = [
  {
    id: '1',
    name: 'Plano Básico',
    description: 'Ideal para iniciantes',
    price: 9900, // R$ 99,00
    interval: 'month',
    moduleAccess: {
      services: true,
      properties: false,
      vehicles: false,
      products: false,
    },
    adLimits: {
      services: 5,
      properties: 0,
      vehicles: 0,
      products: 0,
    },
    features: [
      { id: '1', name: '<PERSON><PERSON><PERSON>s básicos', description: 'Anúncios com fotos e descrição', included: true },
      { id: '2', name: 'Destaque na busca', description: 'Apareça no topo dos resultados', included: false },
      { id: '3', name: 'Estatísticas avançadas', description: 'Métricas detalhadas de desempenho', included: false },
    ],
    stripePriceId: 'price_123',
    isActive: true,
    createdAt: new Date('2023-01-10'),
    updatedAt: new Date('2023-01-10'),
  },
  {
    id: '2',
    name: 'Plano Profissional',
    description: 'Para profissionais e pequenas empresas',
    price: 19900, // R$ 199,00
    interval: 'month',
    moduleAccess: {
      services: true,
      properties: true,
      vehicles: true,
      products: false,
    },
    adLimits: {
      services: 20,
      properties: 10,
      vehicles: 10,
      products: 0,
    },
    features: [
      { id: '1', name: 'Anúncios básicos', description: 'Anúncios com fotos e descrição', included: true },
      { id: '2', name: 'Destaque na busca', description: 'Apareça no topo dos resultados', included: true },
      { id: '3', name: 'Estatísticas avançadas', description: 'Métricas detalhadas de desempenho', included: false },
    ],
    stripePriceId: 'price_456',
    isPopular: true,
    isActive: true,
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-02-20'),
  },
  {
    id: '3',
    name: 'Plano Premium',
    description: 'Solução completa para empresas',
    price: 29900, // R$ 299,00
    interval: 'month',
    moduleAccess: {
      services: true,
      properties: true,
      vehicles: true,
      products: true,
    },
    adLimits: {
      services: 50,
      properties: 30,
      vehicles: 30,
      products: 50,
    },
    features: [
      { id: '1', name: 'Anúncios básicos', description: 'Anúncios com fotos e descrição', included: true },
      { id: '2', name: 'Destaque na busca', description: 'Apareça no topo dos resultados', included: true },
      { id: '3', name: 'Estatísticas avançadas', description: 'Métricas detalhadas de desempenho', included: true },
    ],
    stripePriceId: 'price_789',
    isActive: true,
    createdAt: new Date('2023-01-20'),
    updatedAt: new Date('2023-03-15'),
  },
  {
    id: '4',
    name: 'Plano Anual',
    description: 'Economize com pagamento anual',
    price: 199900, // R$ 1.999,00
    interval: 'year',
    moduleAccess: {
      services: true,
      properties: true,
      vehicles: true,
      products: true,
    },
    adLimits: {
      services: 50,
      properties: 30,
      vehicles: 30,
      products: 50,
    },
    features: [
      { id: '1', name: 'Anúncios básicos', description: 'Anúncios com fotos e descrição', included: true },
      { id: '2', name: 'Destaque na busca', description: 'Apareça no topo dos resultados', included: true },
      { id: '3', name: 'Estatísticas avançadas', description: 'Métricas detalhadas de desempenho', included: true },
    ],
    stripePriceId: 'price_101112',
    isActive: false,
    createdAt: new Date('2023-02-01'),
    updatedAt: new Date('2023-04-10'),
  },
];

export default function PlanTable() {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(amount / 100);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR');
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Nome</TableHead>
            <TableHead>Preço</TableHead>
            <TableHead>Periodicidade</TableHead>
            <TableHead>Módulos</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Última Atualização</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {mockPlans.map((plan) => (
            <TableRow key={plan.id}>
              <TableCell>
                <div>
                  <div className="font-medium">{plan.name}</div>
                  <div className="text-sm text-muted-foreground">{plan.description}</div>
                </div>
              </TableCell>
              <TableCell>{formatCurrency(plan.price)}</TableCell>
              <TableCell>
                {plan.interval === 'month' ? 'Mensal' : 'Anual'}
              </TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {plan.moduleAccess.services && (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      Serviços
                    </Badge>
                  )}
                  {plan.moduleAccess.properties && (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      Imóveis
                    </Badge>
                  )}
                  {plan.moduleAccess.vehicles && (
                    <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                      Veículos
                    </Badge>
                  )}
                  {plan.moduleAccess.products && (
                    <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                      Produtos
                    </Badge>
                  )}
                </div>
              </TableCell>
              <TableCell>
                {plan.isActive ? (
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    Ativo
                  </Badge>
                ) : (
                  <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                    Inativo
                  </Badge>
                )}
              </TableCell>
              <TableCell>{formatDate(plan.updatedAt)}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                      <span className="sr-only">Abrir menu</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Ações</DropdownMenuLabel>
                    <DropdownMenuItem>
                      <Eye className="mr-2 h-4 w-4" />
                      <span>Visualizar</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Edit className="mr-2 h-4 w-4" />
                      <span>Editar</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    {plan.isActive ? (
                      <DropdownMenuItem>
                        <X className="mr-2 h-4 w-4" />
                        <span>Desativar</span>
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem>
                        <Check className="mr-2 h-4 w-4" />
                        <span>Ativar</span>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-red-600">
                      <Trash className="mr-2 h-4 w-4" />
                      <span>Excluir</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
