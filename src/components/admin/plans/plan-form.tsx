'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Check, Plus, Trash2, X } from 'lucide-react';
import { Plan, PlanFormValues } from '@/types/models/plan';
import { PlanService } from '@/lib/services/plan-service';
import { toast } from 'sonner';

// Schema de validação do formulário
const planFormSchema = z.object({
  name: z.string().min(3, 'O nome deve ter pelo menos 3 caracteres'),
  description: z.string().min(10, 'A descrição deve ter pelo menos 10 caracteres'),
  price: z.coerce.number().min(1, 'O preço deve ser maior que zero'),
  interval: z.enum(['month', 'year']),
  popular: z.boolean().default(false),
  active: z.boolean().default(true),
  order: z.coerce.number().int().min(0),
  modules: z.object({
    services: z.boolean().default(true),
    properties: z.boolean().default(false),
    vehicles: z.boolean().default(false),
    products: z.boolean().default(false),
  }),
  limits: z.object({
    services: z.coerce.number().int().min(0),
    properties: z.coerce.number().int().min(0),
    vehicles: z.coerce.number().int().min(0),
    products: z.coerce.number().int().min(0),
    featured: z.coerce.number().int().min(0),
  }),
  features: z
    .array(
      z.object({
        name: z.string().min(1, 'O nome da característica é obrigatório'),
        included: z.boolean().default(true),
      }),
    )
    .min(1, 'Adicione pelo menos uma característica'),
});

interface PlanFormProps {
  plan?: Plan;
}

export function PlanForm({ plan }: PlanFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Inicializar o formulário com os valores do plano ou valores padrão
  const form = useForm<PlanFormValues>({
    resolver: zodResolver(planFormSchema),
    defaultValues: plan
      ? {
          ...plan,
          price: plan.price / 100, // Converter de centavos para reais para o formulário
        }
      : {
          name: '',
          description: '',
          price: 49.9, // Valor padrão em reais
          interval: 'month' as const,
          popular: false,
          active: true,
          order: 0,
          modules: {
            services: true,
            properties: false,
            vehicles: false,
            products: false,
          },
          limits: {
            services: 5,
            properties: 0,
            vehicles: 0,
            products: 0,
            featured: 1,
          },
          features: [
            { name: 'Até 5 serviços ativos', included: true },
            { name: '1 anúncio em destaque', included: true },
            { name: 'Estatísticas básicas', included: true },
            { name: 'Suporte por email', included: true },
          ],
        },
  });

  // Configurar o field array para as características
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'features',
  });

  // Função para adicionar uma nova característica
  const addFeature = () => {
    append({ name: '', included: true });
  };

  // Função para enviar o formulário
  const onSubmit = async (data: PlanFormValues) => {
    setIsSubmitting(true);

    try {
      const planService = new PlanService();

      if (plan) {
        // Atualizar plano existente
        await planService.update(plan.id, data);
        toast.success('Plano atualizado com sucesso!');
      } else {
        // Criar novo plano
        await planService.create(data);
        toast.success('Plano criado com sucesso!');
      }

      // Redirecionar para a lista de planos
      router.push('/admin/plans');
      router.refresh();
    } catch (error) {
      console.error('Erro ao salvar plano:', error);
      toast.error('Erro ao salvar plano. Tente novamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">Informações Básicas</TabsTrigger>
            <TabsTrigger value="modules">Módulos e Limites</TabsTrigger>
            <TabsTrigger value="features">Características</TabsTrigger>
          </TabsList>

          {/* Aba de Informações Básicas */}
          <TabsContent value="basic" className="space-y-6 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nome do Plano</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Plano Básico" {...field} />
                    </FormControl>
                    <FormDescription>Nome que será exibido para os usuários.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preço (R$)</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" placeholder="Ex: 49.90" {...field} />
                    </FormControl>
                    <FormDescription>
                      Valor em reais (será convertido para centavos).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Descrição</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Ex: Ideal para quem está começando" {...field} />
                  </FormControl>
                  <FormDescription>Breve descrição do plano.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                control={form.control}
                name="interval"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Intervalo</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o intervalo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="month">Mensal</SelectItem>
                        <SelectItem value="year">Anual</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>Periodicidade da cobrança.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="order"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Ordem</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="Ex: 1" {...field} />
                    </FormControl>
                    <FormDescription>Ordem de exibição do plano.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex flex-col space-y-6">
                <FormField
                  control={form.control}
                  name="popular"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Popular</FormLabel>
                        <FormDescription>Destacar como plano popular.</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Ativo</FormLabel>
                        <FormDescription>Plano disponível para assinatura.</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </TabsContent>

          {/* Aba de Módulos e Limites */}
          <TabsContent value="modules" className="space-y-6 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Módulos Incluídos</h3>
              <p className="text-sm text-muted-foreground">
                Selecione quais módulos estarão disponíveis neste plano.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="modules.services"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Serviços</FormLabel>
                        <FormDescription>Permitir cadastro de serviços.</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="modules.properties"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Imóveis</FormLabel>
                        <FormDescription>Permitir cadastro de imóveis.</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="modules.vehicles"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Veículos</FormLabel>
                        <FormDescription>Permitir cadastro de veículos.</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="modules.products"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Produtos</FormLabel>
                        <FormDescription>Permitir cadastro de produtos.</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Limites</h3>
              <p className="text-sm text-muted-foreground">
                Defina os limites para cada tipo de item neste plano.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="limits.services"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Limite de Serviços</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Ex: 5" {...field} />
                      </FormControl>
                      <FormDescription>Quantidade máxima de serviços.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="limits.properties"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Limite de Imóveis</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Ex: 0" {...field} />
                      </FormControl>
                      <FormDescription>Quantidade máxima de imóveis.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="limits.vehicles"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Limite de Veículos</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Ex: 0" {...field} />
                      </FormControl>
                      <FormDescription>Quantidade máxima de veículos.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="limits.products"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Limite de Produtos</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Ex: 0" {...field} />
                      </FormControl>
                      <FormDescription>Quantidade máxima de produtos.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="limits.featured"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Anúncios em Destaque</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="Ex: 1" {...field} />
                      </FormControl>
                      <FormDescription>Quantidade de anúncios em destaque.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </TabsContent>

          {/* Aba de Características */}
          <TabsContent value="features" className="space-y-6 pt-4">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-medium">Características do Plano</h3>
                <p className="text-sm text-muted-foreground">
                  Adicione as características que serão exibidas na página de assinatura.
                </p>
              </div>
              <Button type="button" onClick={addFeature} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Adicionar
              </Button>
            </div>

            <div className="space-y-4">
              {fields.map((field, index) => (
                <div key={field.id} className="flex items-center gap-4">
                  <div className="flex-1 flex items-center gap-4">
                    <FormField
                      control={form.control}
                      name={`features.${index}.name`}
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormControl>
                            <Input placeholder="Ex: Até 5 serviços ativos" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`features.${index}.included`}
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <div className="flex items-center space-x-2">
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                id={`feature-included-${index}`}
                              />
                              <label
                                htmlFor={`feature-included-${index}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                              >
                                {field.value ? (
                                  <span className="text-green-600 flex items-center">
                                    <Check className="h-4 w-4 mr-1" />
                                    Incluído
                                  </span>
                                ) : (
                                  <span className="text-red-600 flex items-center">
                                    <X className="h-4 w-4 mr-1" />
                                    Não incluído
                                  </span>
                                )}
                              </label>
                            </div>
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>

                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => remove(index)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}

              {fields.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  Nenhuma característica adicionada. Clique em &quot;Adicionar&quot; para começar.
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push('/admin/plans')}
            disabled={isSubmitting}
          >
            Cancelar
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 'Salvando...' : plan ? 'Atualizar Plano' : 'Criar Plano'}
          </Button>
        </div>
      </form>
    </Form>
  );
}
