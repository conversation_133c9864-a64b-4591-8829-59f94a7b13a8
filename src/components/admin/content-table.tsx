// src/components/admin/content-table.tsx
"use client";

import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { 
  MoreHorizontal, 
  Eye,
  Edit,
  Trash,
  Check,
  X,
  AlertTriangle,
  ThumbsUp
} from 'lucide-react';

// Tipos de conteúdo para demonstração
type ContentType = 'services' | 'properties' | 'vehicles' | 'products';
type ContentStatus = 'active' | 'pending' | 'rejected' | 'reported';

interface Content {
  id: string;
  title: string;
  type: ContentType;
  status: ContentStatus;
  owner: string;
  ownerEmail: string;
  createdAt: Date;
  reports?: number;
}

// Dados de exemplo para demonstração
const mockContent: Content[] = [
  {
    id: '1',
    title: 'Serviço de Encanamento Residencial',
    type: 'services',
    status: 'active',
    owner: '<PERSON>',
    ownerEmail: '<EMAIL>',
    createdAt: new Date('2023-05-15'),
  },
  {
    id: '2',
    title: 'Apartamento 3 quartos - Centro',
    type: 'properties',
    status: 'pending',
    owner: 'Maria Oliveira',
    ownerEmail: '<EMAIL>',
    createdAt: new Date('2023-05-20'),
  },
  {
    id: '3',
    title: 'Honda Civic 2020 - Completo',
    type: 'vehicles',
    status: 'reported',
    owner: 'Pedro Santos',
    ownerEmail: '<EMAIL>',
    createdAt: new Date('2023-05-18'),
    reports: 3,
  },
  {
    id: '4',
    title: 'iPhone 13 Pro - Novo',
    type: 'products',
    status: 'active',
    owner: 'Ana Costa',
    ownerEmail: '<EMAIL>',
    createdAt: new Date('2023-05-22'),
  },
  {
    id: '5',
    title: 'Serviço de Limpeza Comercial',
    type: 'services',
    status: 'rejected',
    owner: 'Carlos Ferreira',
    ownerEmail: '<EMAIL>',
    createdAt: new Date('2023-05-10'),
  },
  {
    id: '6',
    title: 'Casa com piscina - Condomínio',
    type: 'properties',
    status: 'reported',
    owner: 'Fernanda Lima',
    ownerEmail: '<EMAIL>',
    createdAt: new Date('2023-05-12'),
    reports: 2,
  },
];

interface ContentTableProps {
  type?: ContentType;
}

export default function ContentTable({ type }: ContentTableProps) {
  // Filtrar conteúdo com base no tipo fornecido
  const filteredContent = type 
    ? mockContent.filter(content => content.type === type)
    : mockContent;

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR');
  };

  const getContentTypeLabel = (type: ContentType) => {
    switch (type) {
      case 'services':
        return 'Serviço';
      case 'properties':
        return 'Imóvel';
      case 'vehicles':
        return 'Veículo';
      case 'products':
        return 'Produto';
    }
  };

  const getContentTypeColor = (type: ContentType) => {
    switch (type) {
      case 'services':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'properties':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'vehicles':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200';
      case 'products':
        return 'bg-purple-50 text-purple-700 border-purple-200';
    }
  };

  const getStatusBadge = (status: ContentStatus) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Ativo
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Pendente
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Rejeitado
          </Badge>
        );
      case 'reported':
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
            Denunciado
          </Badge>
        );
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Título</TableHead>
            {!type && <TableHead>Tipo</TableHead>}
            <TableHead>Status</TableHead>
            <TableHead>Proprietário</TableHead>
            <TableHead>Data de Criação</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredContent.length === 0 ? (
            <TableRow>
              <TableCell colSpan={type ? 5 : 6} className="h-24 text-center">
                Nenhum conteúdo encontrado.
              </TableCell>
            </TableRow>
          ) : (
            filteredContent.map((content) => (
              <TableRow key={content.id}>
                <TableCell>
                  <div className="font-medium">
                    {content.title}
                    {content.reports && (
                      <Badge variant="outline" className="ml-2 bg-red-50 text-red-700 border-red-200">
                        <AlertTriangle className="h-3 w-3 mr-1" />
                        {content.reports} {content.reports === 1 ? 'denúncia' : 'denúncias'}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                {!type && (
                  <TableCell>
                    <Badge variant="outline" className={getContentTypeColor(content.type)}>
                      {getContentTypeLabel(content.type)}
                    </Badge>
                  </TableCell>
                )}
                <TableCell>
                  {getStatusBadge(content.status)}
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{content.owner}</div>
                    <div className="text-sm text-muted-foreground">{content.ownerEmail}</div>
                  </div>
                </TableCell>
                <TableCell>{formatDate(content.createdAt)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Abrir menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Ações</DropdownMenuLabel>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>Visualizar</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Editar</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {content.status === 'pending' && (
                        <>
                          <DropdownMenuItem>
                            <Check className="mr-2 h-4 w-4 text-green-600" />
                            <span className="text-green-600">Aprovar</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <X className="mr-2 h-4 w-4 text-red-600" />
                            <span className="text-red-600">Rejeitar</span>
                          </DropdownMenuItem>
                        </>
                      )}
                      {content.status === 'reported' && (
                        <>
                          <DropdownMenuItem>
                            <ThumbsUp className="mr-2 h-4 w-4 text-green-600" />
                            <span className="text-green-600">Ignorar Denúncias</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <X className="mr-2 h-4 w-4 text-red-600" />
                            <span className="text-red-600">Remover Conteúdo</span>
                          </DropdownMenuItem>
                        </>
                      )}
                      {content.status === 'active' && (
                        <DropdownMenuItem>
                          <X className="mr-2 h-4 w-4" />
                          <span>Desativar</span>
                        </DropdownMenuItem>
                      )}
                      {content.status === 'rejected' && (
                        <DropdownMenuItem>
                          <Check className="mr-2 h-4 w-4" />
                          <span>Ativar</span>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash className="mr-2 h-4 w-4" />
                        <span>Excluir</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
