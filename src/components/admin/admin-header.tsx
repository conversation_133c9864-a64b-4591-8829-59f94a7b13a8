// src/components/admin/admin-header.tsx
'use client';

import { Session } from 'next-auth';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { signOut } from 'next-auth/react';
import {
  Bell,
  Menu,
  Settings,
  User as UserIcon,
  LogOut,
  LayoutDashboard,
  Users,
  CreditCard,
  FileText,
  BarChart3,
} from 'lucide-react';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { AdminSidebar } from './admin-sidebar';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import Logo from '../forms/logo';

interface AdminHeaderProps {
  user: Session['user'];
}

export function AdminHeader({ user }: AdminHeaderProps) {
  const pathname = usePathname();
  const getInitials = (name: string | null | undefined) => {
    if (!name) return 'A';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <header className="border-b border-slate-200 bg-white dark:bg-slate-950 dark:border-slate-800">
      <div className="flex h-16 items-center px-4 md:px-6">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="mr-4 md:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-full max-w-[300px]">
            <div className="flex h-full w-full flex-col bg-white dark:bg-slate-900">
              <div className="flex h-16 items-center border-b">
                <Logo />
              </div>
              <div className="flex-1 overflow-auto py-2">
                <nav className="grid items-start px-2 text-sm font-medium">
                  {[
                    {
                      title: 'Dashboard',
                      href: '/admin/dashboard',
                      icon: LayoutDashboard,
                    },
                    {
                      title: 'Usuários',
                      href: '/admin/users',
                      icon: Users,
                    },
                    {
                      title: 'Planos',
                      href: '/admin/plans',
                      icon: CreditCard,
                    },
                    {
                      title: 'Conteúdo',
                      href: '/admin/content',
                      icon: FileText,
                    },
                    {
                      title: 'Analytics',
                      href: '/admin/analytics',
                      icon: BarChart3,
                    },
                    {
                      title: 'Configurações',
                      href: '/admin/settings',
                      icon: Settings,
                    },
                  ].map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        'flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary',
                        pathname === item.href || pathname.startsWith(`${item.href}/`)
                          ? 'bg-slate-100 dark:bg-slate-800 text-primary'
                          : 'text-slate-500 hover:bg-slate-100 hover:bg-slate-800',
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      {item.title}
                    </Link>
                  ))}
                </nav>
              </div>
            </div>
          </SheetContent>
        </Sheet>

        <div className="ml-auto flex items-center gap-4">
          <Button variant="ghost" size="icon">
            <Bell className="h-5 w-5" />
            <span className="sr-only">Notificações</span>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={user.image || ''} alt={user.name || 'Admin'} />
                  <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Administrador</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <UserIcon className="mr-2 h-4 w-4" />
                <span>Perfil</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Configurações</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => signOut({ callbackUrl: '/' })}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sair</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
