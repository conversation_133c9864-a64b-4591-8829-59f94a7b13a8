// src/components/admin/admin-initializer.tsx
'use client';

import { useEffect, useState } from 'react';

export default function AdminInitializer() {
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    const initAdmin = async () => {
      try {
        // Chamar a API para inicializar o administrador
        const response = await fetch('/api/admin/init');
        const data = await response.json();
        
        if (data.success) {
          console.log('Admin initialization successful');
        } else {
          console.error('Admin initialization failed:', data.error);
        }
      } catch (error) {
        console.error('Error initializing admin:', error);
      } finally {
        setInitialized(true);
      }
    };

    if (!initialized) {
      initAdmin();
    }
  }, [initialized]);

  // Este componente não renderiza nada visualmente
  return null;
}
