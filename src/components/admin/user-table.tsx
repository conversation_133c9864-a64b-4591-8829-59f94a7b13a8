// src/components/admin/user-table.tsx
"use client";

import { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  MoreHorizontal, 
  UserCheck, 
  UserX, 
  Shield, 
  ShieldOff,
  Eye,
  Edit,
  Trash
} from 'lucide-react';

// Tipos de usuário para demonstração
interface User {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'blocked';
  role: 'user' | 'admin';
  createdAt: Date;
  subscription?: string;
}

// Dados de exemplo para demonstração
const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'active',
    role: 'user',
    createdAt: new Date('2023-01-15'),
    subscription: 'Plano Básico',
  },
  {
    id: '2',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    status: 'active',
    role: 'admin',
    createdAt: new Date('2023-02-20'),
  },
  {
    id: '3',
    name: 'Pedro Santos',
    email: '<EMAIL>',
    status: 'blocked',
    role: 'user',
    createdAt: new Date('2023-03-10'),
    subscription: 'Plano Premium',
  },
  {
    id: '4',
    name: 'Ana Costa',
    email: '<EMAIL>',
    status: 'active',
    role: 'user',
    createdAt: new Date('2023-04-05'),
    subscription: 'Plano Profissional',
  },
  {
    id: '5',
    name: 'Carlos Ferreira',
    email: '<EMAIL>',
    status: 'active',
    role: 'user',
    createdAt: new Date('2023-05-12'),
  },
];

interface UserTableProps {
  filter?: 'active' | 'blocked' | 'admin';
}

export default function UserTable({ filter }: UserTableProps) {
  // Filtrar usuários com base no filtro fornecido
  const filteredUsers = mockUsers.filter(user => {
    if (filter === 'active') return user.status === 'active';
    if (filter === 'blocked') return user.status === 'blocked';
    if (filter === 'admin') return user.role === 'admin';
    return true;
  });

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR');
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Usuário</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Função</TableHead>
            <TableHead>Data de Cadastro</TableHead>
            <TableHead>Assinatura</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredUsers.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                Nenhum usuário encontrado.
              </TableCell>
            </TableRow>
          ) : (
            filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-9 w-9">
                      <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.name}`} alt={user.name} />
                      <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{user.name}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {user.status === 'active' ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      Ativo
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      Bloqueado
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  {user.role === 'admin' ? (
                    <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                      Administrador
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      Usuário
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{formatDate(user.createdAt)}</TableCell>
                <TableCell>
                  {user.subscription ? (
                    <span>{user.subscription}</span>
                  ) : (
                    <span className="text-muted-foreground">Sem assinatura</span>
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Abrir menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Ações</DropdownMenuLabel>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>Visualizar</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Editar</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {user.status === 'active' ? (
                        <DropdownMenuItem>
                          <UserX className="mr-2 h-4 w-4" />
                          <span>Bloquear</span>
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem>
                          <UserCheck className="mr-2 h-4 w-4" />
                          <span>Ativar</span>
                        </DropdownMenuItem>
                      )}
                      {user.role === 'admin' ? (
                        <DropdownMenuItem>
                          <ShieldOff className="mr-2 h-4 w-4" />
                          <span>Remover Admin</span>
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem>
                          <Shield className="mr-2 h-4 w-4" />
                          <span>Tornar Admin</span>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash className="mr-2 h-4 w-4" />
                        <span>Excluir</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
