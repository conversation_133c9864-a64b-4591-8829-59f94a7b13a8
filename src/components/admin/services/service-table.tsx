'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { MoreHorizontal, Eye, Edit, Trash, Check, X, AlertTriangle, Star } from 'lucide-react';
import { Service } from '@/types/models/service';
import { ServiceService } from '@/lib/services/service-service';
import { formatCurrency, formatDate } from '@/lib/utils';

export default function ServiceTable() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        setLoading(true);
        const serviceService = new ServiceService();
        const result = await serviceService.list({
          orderByField: 'createdAt',
          orderByDirection: 'desc',
          limit: 10,
        });
        setServices(result.items);
      } catch (err) {
        console.error('Error fetching services:', err);
        setError('Falha ao carregar serviços. Tente novamente mais tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Publicado
          </Badge>
        );
      case 'draft':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Rascunho
          </Badge>
        );
      case 'pending_review':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Em Revisão
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Rejeitado
          </Badge>
        );
      case 'archived':
        return (
          <Badge variant="outline" className="bg-slate-50 text-slate-700 border-slate-200">
            Arquivado
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-red-500 flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5" />
          {error}
        </div>
      </div>
    );
  }

  // Dados de exemplo para demonstração
  const mockServices: Service[] = [
    {
      id: '1',
      title: 'Serviço de Encanamento Residencial',
      description: 'Conserto de vazamentos, instalação de torneiras, chuveiros e mais.',
      status: 'published',
      createdAt: new Date('2023-05-15'),
      updatedAt: new Date('2023-05-15'),
      publishedAt: new Date('2023-05-16'),
      createdBy: 'user1',
      price: 15000, // R$ 150,00
      currency: 'BRL',
      priceType: 'hourly',
      categoryId: 'cat1',
      categoryName: 'Encanamento',
      advertiserId: 'adv1',
      advertiserName: 'João Silva',
      views: 120,
      rating: 4.5,
      ratingCount: 12,
      featured: true,
      address: {
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01234-567',
        country: 'Brasil',
        neighborhood: 'Centro',
        street: 'Rua da Praça',
        number: '123',
      },
    },
    {
      id: '2',
      title: 'Serviço de Limpeza Residencial',
      description:
        'Limpeza completa de residências, incluindo salas, quartos, cozinha e banheiros.',
      status: 'draft',
      createdAt: new Date('2023-05-18'),
      updatedAt: new Date('2023-05-18'),
      createdBy: 'user2',
      price: 20000, // R$ 200,00
      currency: 'BRL',
      priceType: 'fixed',
      categoryId: 'cat2',
      categoryName: 'Limpeza',
      advertiserId: 'adv2',
      advertiserName: 'Maria Oliveira',
      address: {
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01234-567',
        country: 'Brasil',
        neighborhood: 'Centro',
        street: 'Rua da Praça',
        number: '123',
      },
    },
    {
      id: '3',
      title: 'Aulas de Inglês Particulares',
      description: 'Aulas de inglês para todos os níveis, com foco em conversação.',
      status: 'pending_review',
      createdAt: new Date('2023-05-20'),
      updatedAt: new Date('2023-05-20'),
      createdBy: 'user3',
      price: 8000, // R$ 80,00
      currency: 'BRL',
      priceType: 'hourly',
      categoryId: 'cat3',
      categoryName: 'Aulas',
      subcategoryId: 'subcat1',
      subcategoryName: 'Idiomas',
      advertiserId: 'adv3',
      advertiserName: 'Pedro Santos',
      address: {
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01234-567',
        country: 'Brasil',
        neighborhood: 'Centro',
        street: 'Rua da Praça',
        number: '123',
      },
    },
    {
      id: '4',
      title: 'Serviço de Jardinagem',
      description: 'Manutenção de jardins, poda de árvores, plantio de flores e gramados.',
      status: 'rejected',
      createdAt: new Date('2023-05-10'),
      updatedAt: new Date('2023-05-12'),
      createdBy: 'user4',
      price: 12000, // R$ 120,00
      currency: 'BRL',
      priceType: 'hourly',
      categoryId: 'cat4',
      categoryName: 'Jardinagem',
      advertiserId: 'adv4',
      advertiserName: 'Ana Costa',
      reports: 2,
      reportReasons: ['Informações falsas', 'Preço abusivo'],
      address: {
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01234-567',
        country: 'Brasil',
        neighborhood: 'Centro',
        street: 'Rua da Praça',
        number: '123',
      },
    },
    {
      id: '5',
      title: 'Consultoria Financeira',
      description: 'Consultoria para planejamento financeiro pessoal e familiar.',
      status: 'archived',
      createdAt: new Date('2023-04-25'),
      updatedAt: new Date('2023-05-05'),
      publishedAt: new Date('2023-04-26'),
      createdBy: 'user5',
      price: 30000, // R$ 300,00
      currency: 'BRL',
      priceType: 'hourly',
      categoryId: 'cat5',
      categoryName: 'Consultoria',
      subcategoryId: 'subcat2',
      subcategoryName: 'Finanças',
      advertiserId: 'adv5',
      advertiserName: 'Carlos Ferreira',
      views: 85,
      rating: 5,
      ratingCount: 7,
      address: {
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01234-567',
        country: 'Brasil',
        neighborhood: 'Centro',
        street: 'Rua da Praça',
        number: '123',
      },
    },
  ];

  // Usar dados mockados para demonstração
  const displayServices = services.length > 0 ? services : mockServices;

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Serviço</TableHead>
            <TableHead>Categoria</TableHead>
            <TableHead>Preço</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Anunciante</TableHead>
            <TableHead>Visualizações</TableHead>
            <TableHead>Avaliação</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {displayServices.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="h-24 text-center">
                Nenhum serviço encontrado.
              </TableCell>
            </TableRow>
          ) : (
            displayServices.map((service) => (
              <TableRow key={service.id}>
                <TableCell>
                  <div>
                    <div className="font-medium flex items-center">
                      {service.title}
                      {service.featured && (
                        <Badge
                          variant="outline"
                          className="ml-2 bg-purple-50 text-purple-700 border-purple-200"
                        >
                          Destaque
                        </Badge>
                      )}
                      {service.reports && service.reports > 0 && (
                        <Badge
                          variant="outline"
                          className="ml-2 bg-red-50 text-red-700 border-red-200"
                        >
                          <AlertTriangle className="h-3 w-3 mr-1" />
                          {service.reports} {service.reports === 1 ? 'denúncia' : 'denúncias'}
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground text-ellipsis overflow-hidden w-[300px] line-clamp-1">
                      {service.description}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div>{service.categoryName}</div>
                    {service.subcategoryName && (
                      <div className="text-sm text-muted-foreground">{service.subcategoryName}</div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div>{formatCurrency(service.price, service.currency)}</div>
                    <div className="text-sm text-muted-foreground">
                      {service.priceType === 'fixed' && 'Fixo'}
                      {service.priceType === 'hourly' && 'Por hora'}
                      {service.priceType === 'daily' && 'Por dia'}
                      {service.priceType === 'monthly' && 'Por mês'}
                      {service.priceType === 'negotiable' && 'Negociável'}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{getStatusBadge(service.status)}</TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{service.advertiserName}</div>
                    <div className="text-sm text-muted-foreground">ID: {service.advertiserId}</div>
                  </div>
                </TableCell>
                <TableCell>{service.views || 0}</TableCell>
                <TableCell>
                  {service.rating ? (
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-500 mr-1 fill-yellow-500" />
                      <span>{service.rating.toFixed(1)}</span>
                      <span className="text-sm text-muted-foreground ml-1">
                        ({service.ratingCount})
                      </span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Sem avaliações</span>
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Abrir menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Ações</DropdownMenuLabel>
                      <DropdownMenuItem>
                        <Eye className="mr-2 h-4 w-4" />
                        <span>Visualizar</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 h-4 w-4" />
                        <span>Editar</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {service.status === 'pending_review' && (
                        <>
                          <DropdownMenuItem>
                            <Check className="mr-2 h-4 w-4 text-green-600" />
                            <span className="text-green-600">Aprovar</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <X className="mr-2 h-4 w-4 text-red-600" />
                            <span className="text-red-600">Rejeitar</span>
                          </DropdownMenuItem>
                        </>
                      )}
                      {service.status === 'draft' && (
                        <DropdownMenuItem>
                          <Check className="mr-2 h-4 w-4" />
                          <span>Publicar</span>
                        </DropdownMenuItem>
                      )}
                      {service.status === 'published' && (
                        <DropdownMenuItem>
                          <X className="mr-2 h-4 w-4" />
                          <span>Arquivar</span>
                        </DropdownMenuItem>
                      )}
                      {service.status === 'archived' && (
                        <DropdownMenuItem>
                          <Check className="mr-2 h-4 w-4" />
                          <span>Restaurar</span>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash className="mr-2 h-4 w-4" />
                        <span>Excluir</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
