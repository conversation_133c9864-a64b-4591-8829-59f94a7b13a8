'use client';

import { useState, useEffect } from 'react';
import { LocationService, State, City } from '@/services/location-service';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { FormControl, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface LocationSelectorProps {
  selectedState: string;
  selectedCity: string;
  onStateChange: (stateId: string, stateName: string, stateUF: string) => void;
  onCityChange: (cityId: string, cityName: string, normalizedName: string) => void;
  label?: {
    state?: string;
    city?: string;
  };
  disabled?: boolean;
}

export function LocationSelector({
  selectedState,
  selectedCity,
  onStateChange,
  onCityChange,
  label = { state: 'Estado', city: 'Cidade' },
  disabled = false,
}: LocationSelectorProps) {
  const [states, setStates] = useState<State[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const locationService = LocationService.getInstance();

  // Carregar estados
  useEffect(() => {
    const fetchStates = async () => {
      try {
        setLoading(true);
        const statesData = await locationService.getStates();
        setStates(statesData);
        setError(null);
      } catch (err) {
        console.error('Erro ao carregar estados:', err);
        setError('Não foi possível carregar a lista de estados.');
      } finally {
        setLoading(false);
      }
    };

    fetchStates();
  }, []);

  // Carregar cidades quando o estado mudar
  useEffect(() => {
    const fetchCities = async () => {
      if (!selectedState) {
        setCities([]);
        return;
      }

      try {
        setLoading(true);
        const citiesData = await locationService.getCitiesByState(selectedState);
        setCities(citiesData);
        setError(null);
      } catch (err) {
        console.error('Erro ao carregar cidades:', err);
        setError('Não foi possível carregar a lista de cidades.');
      } finally {
        setLoading(false);
      }
    };

    fetchCities();
  }, [selectedState]);

  // Manipuladores de eventos
  const handleStateChange = (stateId: string) => {
    const state = states.find(s => s.id === stateId);
    if (state) {
      onStateChange(state.id, state.nome, state.sigla);
      onCityChange('', '', ''); // Limpar cidade quando o estado mudar
    }
  };

  const handleCityChange = (cityId: string) => {
    const city = cities.find(c => c.id === cityId);
    if (city) {
      onCityChange(city.id, city.nome, city.nomeNormalizado);
    }
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <FormItem>
        <FormLabel>{label.state}</FormLabel>
        <FormControl>
          {loading && !states.length ? (
            <Skeleton className="h-10 w-full" />
          ) : (
            <Select
              value={selectedState}
              onValueChange={handleStateChange}
              disabled={disabled || loading}
            >
              <SelectTrigger>
                <SelectValue placeholder={`Selecione o ${label.state?.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {states.map((state) => (
                  <SelectItem key={state.id} value={state.id}>
                    {state.nome} ({state.sigla})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </FormControl>
        <FormMessage />
      </FormItem>

      <FormItem>
        <FormLabel>{label.city}</FormLabel>
        <FormControl>
          {loading && selectedState ? (
            <Skeleton className="h-10 w-full" />
          ) : (
            <Select
              value={selectedCity}
              onValueChange={handleCityChange}
              disabled={disabled || loading || !selectedState}
            >
              <SelectTrigger>
                <SelectValue placeholder={`Selecione a ${label.city?.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {cities.map((city) => (
                  <SelectItem key={city.id} value={city.id}>
                    {city.nome}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </FormControl>
        <FormMessage />
      </FormItem>
    </div>
  );
}
