'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Trash, Upload, Loader2, Plus } from 'lucide-react';
import Image from 'next/image';
import { toast } from 'sonner';

interface ImageUploadProps {
  value: string[];
  onChange: (value: string[]) => void;
  maxImages?: number;
  className?: string;
}

export function ImageUpload({
  value = [],
  onChange,
  maxImages = 10,
  className,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUpload = async (file: File) => {
    // Verificar tipo de arquivo
    if (!file.type.startsWith('image/')) {
      toast.error('Tipo de arquivo inválido', {
        description: 'Por favor, selecione apenas imagens (JPG, PNG, etc.)',
      });
      return;
    }

    // Verificar tamanho do arquivo (5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('Arquivo muito grande', {
        description: 'O tamanho máximo permitido é 5MB',
      });
      return;
    }

    try {
      setIsUploading(true);

      // Criar FormData para enviar o arquivo
      const formData = new FormData();
      formData.append('file', file);

      // Enviar para a API
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.message || 'Erro ao fazer upload da imagem');
      }

      // Adicionar URL à lista de imagens
      onChange([...value, data.url]);
      toast.success('Upload concluído', {
        description: 'A imagem foi adicionada com sucesso',
      });
    } catch (error) {
      console.error('Erro ao fazer upload:', error);
      toast.error('Falha no upload', {
        description: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleUpload(file);
    }
    // Limpar o input para permitir selecionar o mesmo arquivo novamente
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemove = (index: number) => {
    const newImages = [...value];
    newImages.splice(index, 1);
    onChange(newImages);
    toast.success('Imagem removida');
  };

  const handleAddClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Usar imagem aleatória do Unsplash quando não houver imagem
  const handleAddPlaceholder = () => {
    const mockImageUrl = `https://source.unsplash.com/random/800x600?service&sig=${Date.now()}`;
    onChange([...value, mockImageUrl]);
    toast.success('Imagem de exemplo adicionada', {
      description: 'Uma imagem aleatória foi adicionada como exemplo',
    });
  };

  return (
    <div className={className}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Imagens existentes */}
        {value.map((image, index) => (
          <div key={index} className="relative border rounded-md overflow-hidden h-40 group">
            <Image
              src={image}
              alt={`Imagem ${index + 1}`}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button
                type="button"
                variant="destructive"
                size="sm"
                onClick={() => handleRemove(index)}
                disabled={isUploading}
              >
                <Trash className="h-4 w-4 mr-2" />
                Remover
              </Button>
            </div>
          </div>
        ))}

        {/* Botão de upload */}
        {value.length < maxImages && (
          <div
            className="border border-dashed rounded-md p-4 flex flex-col items-center justify-center h-40 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-900"
            onClick={handleAddClick}
          >
            {isUploading ? (
              <>
                <Loader2 className="h-8 w-8 text-muted-foreground mb-2 animate-spin" />
                <p className="text-sm text-muted-foreground">Enviando...</p>
              </>
            ) : (
              <>
                <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                <p className="text-sm text-muted-foreground">Clique para fazer upload</p>
                <p className="text-xs text-muted-foreground mt-1">ou arraste uma imagem</p>
                <div className="flex gap-2 mt-2">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAddPlaceholder();
                    }}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Usar exemplo
                  </Button>
                </div>
              </>
            )}
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
              disabled={isUploading}
            />
          </div>
        )}
      </div>
    </div>
  );
}
