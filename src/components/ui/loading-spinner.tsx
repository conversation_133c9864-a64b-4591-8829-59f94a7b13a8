'use client';

import { ThreeCircles } from 'react-loader-spinner';

interface LoadingSpinnerProps {
  size?: number;
  color?: string;
  fullScreen?: boolean;
  message?: string;
}

export function LoadingSpinner({
  size = 100,
  color = '#0ea5e9', // Cor primária do tema (sky-500)
  fullScreen = false,
  message = 'Carregando...',
}: LoadingSpinnerProps) {
  const containerClasses = fullScreen
    ? 'fixed inset-0 flex flex-col items-center justify-center bg-background/80 backdrop-blur-sm z-50'
    : 'flex flex-col items-center justify-center py-8';

  return (
    <div className={containerClasses}>
      <ThreeCircles
        visible={true}
        height={size}
        width={size}
        color={color}
        wrapperStyle={{}}
        wrapperClass=""
        ariaLabel="three-circles-loading"
        outerCircleColor={color}
        innerCircleColor="#38bdf8" // sky-400
        middleCircleColor="#7dd3fc" // sky-300
      />
      {message && <p className="mt-4 text-muted-foreground">{message}</p>}
    </div>
  );
}
