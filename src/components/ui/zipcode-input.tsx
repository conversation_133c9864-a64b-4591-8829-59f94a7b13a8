'use client';

import { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, Search } from 'lucide-react';
import { toast } from 'sonner';

interface ZipCodeInputProps {
  value: string;
  onChange: (value: string) => void;
  onAddressFound: (address: ZipCodeAddress) => void;
  disabled?: boolean;
  className?: string;
}

export interface ZipCodeAddress {
  zipCode: string;
  street: string;
  neighborhood: string;
  city: string;
  state: string;
  stateCode: string;
  complement?: string;
  ibgeCode?: string;
}

export function ZipCodeInput({
  value,
  onChange,
  onAddressFound,
  disabled = false,
  className = '',
}: ZipCodeInputProps) {
  const [isSearching, setIsSearching] = useState(false);
  const [formattedValue, setFormattedValue] = useState('');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastSearchedZipCodeRef = useRef<string>('');

  // Formatar o CEP ao digitar (adicionar hífen)
  useEffect(() => {
    const formatted = value
      .replace(/\D/g, '')
      .replace(/^(\d{5})(\d)/, '$1-$2')
      .substring(0, 9);

    setFormattedValue(formatted);
  }, [value]);

  // Buscar CEP automaticamente quando tiver 8 dígitos
  useEffect(() => {
    const cleanValue = value.replace(/\D/g, '');

    if (cleanValue.length === 8 && !isSearching && cleanValue !== lastSearchedZipCodeRef.current) {
      // Limpar timeout anterior se existir
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Definir um pequeno delay para evitar múltiplas requisições
      timeoutRef.current = setTimeout(() => {
        lastSearchedZipCodeRef.current = cleanValue;
        searchZipCode(cleanValue);
      }, 500);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [value, isSearching]);

  // Função para buscar CEP
  const searchZipCode = async (zipCode: string) => {
    // Evitar buscar o mesmo CEP novamente ou se já estiver buscando
    if (isSearching || disabled || zipCode === lastSearchedZipCodeRef.current) return;

    setIsSearching(true);

    try {
      const response = await fetch(`/api/zipcode?code=${zipCode}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao buscar CEP');
      }

      const data = await response.json();

      // Atualizar o último CEP pesquisado
      lastSearchedZipCodeRef.current = zipCode;

      // Notificar o componente pai sobre o endereço encontrado
      onAddressFound({
        zipCode: data.zipCode,
        street: data.street,
        neighborhood: data.neighborhood,
        city: data.city,
        state: data.state,
        stateCode: data.stateCode,
        complement: data.complement,
        ibgeCode: data.ibgeCode,
      });

      toast.success('Endereço encontrado!');
    } catch (error) {
      console.error('Erro ao buscar CEP:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao buscar CEP');
      // Limpar o último CEP pesquisado em caso de erro para permitir nova tentativa
      lastSearchedZipCodeRef.current = '';
    } finally {
      setIsSearching(false);
    }
  };

  // Função para lidar com a mudança no input
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
  };

  // Função para buscar CEP manualmente (botão de busca)
  const handleSearch = () => {
    const cleanValue = value.replace(/\D/g, '');

    if (cleanValue.length === 8) {
      // Permitir buscar novamente mesmo se for o mesmo CEP
      lastSearchedZipCodeRef.current = '';
      searchZipCode(cleanValue);
    } else {
      toast.error('CEP inválido. Digite um CEP com 8 dígitos.');
    }
  };

  return (
    <div className={`relative ${className}`}>
      <Input
        type="text"
        value={formattedValue}
        onChange={handleChange}
        placeholder="00000-000"
        disabled={disabled || isSearching}
        maxLength={9}
        className="pr-10"
      />
      <Button
        type="button"
        variant="ghost"
        size="icon"
        className="absolute right-0 top-0 h-full"
        onClick={handleSearch}
        disabled={disabled || isSearching}
      >
        {isSearching ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Search className="h-4 w-4" />
        )}
      </Button>
    </div>
  );
}
