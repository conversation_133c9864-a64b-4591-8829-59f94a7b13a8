// src/components/advertiser/advertiser-sidebar.tsx
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Briefcase,
  Calendar,
  BarChart3,
  Settings,
  MessageSquare,
  User,
  Bell,
  Heart,
  Star,
} from 'lucide-react';

const sidebarItems = [
  {
    title: 'Dashboard',
    href: '/advertiser/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Meus Serviços',
    href: '/advertiser/services',
    icon: Briefcase,
  },
  {
    title: 'Agendamentos',
    href: '/advertiser/appointments',
    icon: Calendar,
  },
  {
    title: 'Analytics',
    href: '/advertiser/analytics',
    icon: BarChart3,
  },
  {
    title: 'Mensagens',
    href: '/advertiser/messages',
    icon: MessageSquare,
    badge: 3,
  },
  {
    title: 'Avaliações',
    href: '/advertiser/reviews',
    icon: Star,
  },
  {
    title: 'Favoritos',
    href: '/advertiser/favorites',
    icon: Heart,
  },
  {
    title: 'Notificações',
    href: '/advertiser/notifications',
    icon: Bell,
    badge: 5,
  },
  {
    title: 'Perfil',
    href: '/advertiser/profile',
    icon: User,
  },
  {
    title: 'Configurações',
    href: '/advertiser/settings',
    icon: Settings,
  },
];

export default function AdvertiserSidebar() {
  const pathname = usePathname();

  return (
    <div className="hidden md:flex h-screen w-64 flex-col border-r bg-white dark:bg-slate-900 sticky top-0">
      <div className="flex h-16 items-center border-b px-6">
        <Link href="/advertiser" className="flex items-center gap-2 font-bold text-xl">
          <span className="text-primary">JáVai</span>
          <span className="text-sm text-muted-foreground">Anunciante</span>
        </Link>
      </div>
      <div className="flex-1 overflow-auto py-2">
        <nav className="grid items-start px-2 text-sm font-medium">
          {sidebarItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary',
                pathname === item.href || pathname.startsWith(`${item.href}/`)
                  ? 'bg-slate-100 dark:bg-slate-800 text-primary'
                  : 'text-slate-500 hover:bg-slate-100 hover:bg-slate-800',
              )}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
              {item.badge && (
                <span className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-primary dark:bg-slate-800 text-[10px] font-medium text-white">
                  {item.badge}
                </span>
              )}
            </Link>
          ))}
        </nav>
      </div>
      <div className="border-t p-4">
        <div className="flex items-center gap-3 rounded-lg bg-slate-100 dark:bg-slate-800 px-3 py-2">
          <div className="text-sm">
            <div className="font-medium">Plano Profissional</div>
            <div className="text-xs text-muted-foreground">Expira em 28 dias</div>
          </div>
          <Link href="/subscription" className="ml-auto text-xs text-primary hover:underline">
            Gerenciar
          </Link>
        </div>
      </div>
    </div>
  );
}
