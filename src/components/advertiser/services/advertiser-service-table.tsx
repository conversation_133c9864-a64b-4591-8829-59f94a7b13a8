'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/use-auth';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontal,
  Eye,
  Edit,
  Trash,
  Copy,
  Archive,
  ExternalLink,
  Star,
  BarChart2,
} from 'lucide-react';
import { Service } from '@/types/models/service';
import { ServiceService } from '@/lib/services/service-service';
import { formatCurrency, formatDate } from '@/lib/utils';
import Link from 'next/link';

export default function AdvertiserServiceTable() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Função para publicar um serviço
  const handlePublish = async (serviceId: string) => {
    try {
      const serviceService = new ServiceService();
      await serviceService.publish(serviceId);

      // Atualizar a lista de serviços
      setServices((prevServices) =>
        prevServices.map((service) =>
          service.id === serviceId
            ? { ...service, status: 'published' as const, publishedAt: new Date() }
            : service,
        ),
      );

      toast.success('Serviço publicado', {
        description: 'Seu serviço foi publicado com sucesso!',
      });
    } catch (error) {
      console.error('Erro ao publicar serviço:', error);
      toast.error('Erro ao publicar', {
        description: 'Ocorreu um erro ao publicar o serviço. Tente novamente.',
      });
    }
  };

  // Função para arquivar um serviço
  const handleArchive = async (serviceId: string) => {
    try {
      const serviceService = new ServiceService();
      await serviceService.archive(serviceId);

      // Atualizar a lista de serviços
      setServices((prevServices) =>
        prevServices.map((service) =>
          service.id === serviceId ? { ...service, status: 'archived' as const } : service,
        ),
      );

      toast.success('Serviço arquivado', {
        description: 'Seu serviço foi arquivado com sucesso!',
      });
    } catch (error) {
      console.error('Erro ao arquivar serviço:', error);
      toast.error('Erro ao arquivar', {
        description: 'Ocorreu um erro ao arquivar o serviço. Tente novamente.',
      });
    }
  };

  const { user, loading: authLoading, isAuthenticated } = useAuth();

  useEffect(() => {
    const fetchServices = async () => {
      // Se ainda está carregando a autenticação, aguarde
      if (authLoading) return;

      // Se não está autenticado após carregar, mostre o erro
      if (!isAuthenticated || !user?.id) {
        setError('Usuário não autenticado');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Buscar serviços do usuário
        const serviceService = new ServiceService();
        const result = await serviceService.listByAdvertiser(user.id, {
          orderByField: 'updatedAt',
          orderByDirection: 'desc',
        });

        setServices(result.items);
      } catch (err) {
        console.error('Error fetching services:', err);
        setError('Falha ao carregar serviços. Tente novamente mais tarde.');
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, [user, authLoading, isAuthenticated]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            Publicado
          </Badge>
        );
      case 'draft':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            Rascunho
          </Badge>
        );
      case 'pending_review':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Em Revisão
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Rejeitado
          </Badge>
        );
      case 'archived':
        return (
          <Badge variant="outline" className="bg-slate-50 text-slate-700 border-slate-200">
            Arquivado
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (authLoading) {
    return <LoadingSpinner message="Verificando autenticação..." />;
  }

  if (loading) {
    return <LoadingSpinner message="Carregando serviços..." />;
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center h-64 gap-4">
        <div className="text-red-500 font-medium">{error}</div>
        <Button variant="outline" onClick={() => window.location.reload()}>
          Tentar novamente
        </Button>
      </div>
    );
  }

  return (
    <div className="rounded-md border mx">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Serviço</TableHead>
            <TableHead>Categoria</TableHead>
            <TableHead>Preço</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Visualizações</TableHead>
            <TableHead>Avaliação</TableHead>
            <TableHead>Última Atualização</TableHead>
            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {services.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="h-24 text-center">
                Nenhum serviço encontrado.{' '}
                <Link href="/advertiser/services/new" className="text-primary hover:underline">
                  Criar um novo serviço
                </Link>
                .
              </TableCell>
            </TableRow>
          ) : (
            services.map((service) => (
              <TableRow key={service.id}>
                <TableCell>
                  <div>
                    <div className="font-medium flex items-center">
                      {service.title}
                      {service.featured && (
                        <Badge
                          variant="outline"
                          className="ml-2 bg-purple-50 text-purple-700 border-purple-200"
                        >
                          Destaque
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground line-clamp-1">
                      {service.description}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{service.categoryName}</TableCell>
                <TableCell>
                  <div>
                    <div>{formatCurrency(service.price, service.currency)}</div>
                    <div className="text-sm text-muted-foreground">
                      {service.priceType === 'fixed' && 'Fixo'}
                      {service.priceType === 'hourly' && 'Por hora'}
                      {service.priceType === 'daily' && 'Por dia'}
                      {service.priceType === 'monthly' && 'Por mês'}
                      {service.priceType === 'negotiable' && 'Negociável'}
                    </div>
                  </div>
                </TableCell>
                <TableCell>{getStatusBadge(service.status)}</TableCell>
                <TableCell>{service.views || 0}</TableCell>
                <TableCell>
                  {service.rating ? (
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-500 mr-1 fill-yellow-500" />
                      <span>{service.rating.toFixed(1)}</span>
                      <span className="text-sm text-muted-foreground ml-1">
                        ({service.ratingCount})
                      </span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Sem avaliações</span>
                  )}
                </TableCell>
                <TableCell>{formatDate(service.updatedAt)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Abrir menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Ações</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/advertiser/services/${service.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          <span>Detalhes</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/public/services/${service.id}`} target="_blank">
                          <ExternalLink className="mr-2 h-4 w-4" />
                          <span>Ver como cliente</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/advertiser/services/${service.id}/edit`}>
                          <Edit className="mr-2 h-4 w-4" />
                          <span>Editar</span>
                        </Link>
                      </DropdownMenuItem>
                      {service.status === 'draft' && (
                        <DropdownMenuItem onClick={() => handlePublish(service.id)}>
                          <ExternalLink className="mr-2 h-4 w-4" />
                          <span>Publicar</span>
                        </DropdownMenuItem>
                      )}
                      {service.status === 'published' && (
                        <DropdownMenuItem onClick={() => handleArchive(service.id)}>
                          <Archive className="mr-2 h-4 w-4" />
                          <span>Arquivar</span>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem asChild>
                        <Link href={`/advertiser/services/${service.id}/stats`}>
                          <BarChart2 className="mr-2 h-4 w-4" />
                          <span>Estatísticas</span>
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {service.status === 'archived' && (
                        <DropdownMenuItem onClick={() => handlePublish(service.id)}>
                          <ExternalLink className="mr-2 h-4 w-4" />
                          <span>Republicar</span>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem>
                        <Copy className="mr-2 h-4 w-4" />
                        <span>Duplicar</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash className="mr-2 h-4 w-4" />
                        <span>Excluir</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
