'use client';

import { useState } from 'react';
import { useFieldArray } from 'react-hook-form';
import { useAuth } from '@/hooks/use-auth';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { ServiceService } from '@/lib/services/service-service';
import { Service } from '@/types/models/service';
import { PublishStatus } from '@/types/models/base';
import { toast } from 'sonner';
import { ImageUpload } from '@/components/ui/image-upload';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Info,
  MapPin,
  DollarSign,
  Calendar,
  Image,
  HelpCircle,
  Plus,
  Trash,
  Upload,
} from 'lucide-react';

// Schema de validação para o formulário
const serviceFormSchema = z.object({
  title: z
    .string()
    .min(5, { message: 'O título deve ter pelo menos 5 caracteres' })
    .max(100, { message: 'O título deve ter no máximo 100 caracteres' }),
  description: z
    .string()
    .min(20, { message: 'A descrição deve ter pelo menos 20 caracteres' })
    .max(2000, { message: 'A descrição deve ter no máximo 2000 caracteres' }),
  categoryId: z.string({ required_error: 'Selecione uma categoria' }),
  subcategoryId: z.string().optional(),
  price: z.coerce.number().min(1, { message: 'O preço deve ser maior que zero' }),
  priceType: z.enum(['fixed', 'hourly', 'daily', 'monthly', 'negotiable'], {
    required_error: 'Selecione um tipo de preço',
  }),
  currency: z.string().default('BRL'),
  useAdvertiserLocation: z.boolean().default(true),
  serviceLocationType: z.enum(['at_provider', 'at_client', 'both']).default('at_provider'),
  availability: z
    .array(
      z.object({
        days: z.array(z.enum(['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'])),
        start: z.string(),
        end: z.string(),
      }),
    )
    .default([]),
  duration: z.coerce.number().optional(),
  tags: z.array(z.string()).optional(),
  featured: z.boolean().default(false),
  status: z.enum(['draft', 'published', 'pending_review', 'archived', 'rejected']).default('draft'),
  images: z.array(z.string()).default([]),
  faqs: z
    .array(
      z.object({
        question: z.string(),
        answer: z.string(),
      }),
    )
    .default([]),
});

type ServiceFormValues = z.infer<typeof serviceFormSchema>;

// Categorias mockadas para demonstração
const mockCategories = [
  { id: 'cat1', name: 'Encanamento' },
  { id: 'cat2', name: 'Ar Condicionado' },
  { id: 'cat3', name: 'Elétrica' },
  { id: 'cat4', name: 'Pintura' },
  { id: 'cat5', name: 'Limpeza' },
  { id: 'cat6', name: 'Jardinagem' },
  { id: 'cat7', name: 'Marcenaria' },
  { id: 'cat8', name: 'Informática' },
];

// Subcategorias mockadas para demonstração
const mockSubcategories = {
  cat1: [
    { id: 'subcat1', name: 'Conserto de Vazamentos' },
    { id: 'subcat2', name: 'Instalação de Torneiras' },
    { id: 'subcat3', name: 'Desentupimento' },
  ],
  cat2: [
    { id: 'subcat4', name: 'Instalação' },
    { id: 'subcat5', name: 'Manutenção' },
    { id: 'subcat6', name: 'Limpeza' },
  ],
  cat3: [
    { id: 'subcat7', name: 'Instalação Elétrica' },
    { id: 'subcat8', name: 'Manutenção Elétrica' },
    { id: 'subcat9', name: 'Iluminação' },
  ],
};

export default function ServiceForm({ service }: { service?: Service }) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(
    service?.categoryId || null,
  );

  // Inicializar o formulário com valores padrão ou do serviço existente
  const form = useForm<ServiceFormValues>({
    resolver: zodResolver(serviceFormSchema),
    defaultValues: service
      ? {
          title: service.title,
          description: service.description,
          categoryId: service.categoryId,
          subcategoryId: service.subcategoryId,
          price: service.price / 100, // Converter de centavos para reais
          priceType: service.priceType,
          currency: service.currency,
          address: service.address,
          showExactLocation: service.showExactLocation,
          availability: service.availability,
          duration: service.duration,
          tags: service.tags,
          featured: service.featured,
          status: service.status as PublishStatus,
          images: service.images,
          faqs: service.faq,
        }
      : {
          title: '',
          description: '',
          categoryId: '',
          price: undefined,
          priceType: 'hourly',
          currency: 'BRL',
          showExactLocation: false,
          featured: false,
          status: 'draft',
          images: [],
          faqs: [],
        },
  });

  // Manipular a mudança de categoria
  const handleCategoryChange = (value: string) => {
    setSelectedCategory(value);
    form.setValue('subcategoryId', ''); // Limpar subcategoria ao mudar de categoria
  };

  // Enviar o formulário
  const onSubmit = async (values: ServiceFormValues) => {
    try {
      setIsSubmitting(true);

      // Converter preço de reais para centavos
      const priceInCents = Math.round(values.price * 100);

      // Obter o ID do usuário atual
      const auth = getAuth();
      const user = auth.currentUser;

      if (!user) {
        throw new Error('Usuário não autenticado');
      }

      // Obter o nome do usuário
      const userName = user.displayName || 'Usuário';

      // Obter o nome da categoria selecionada
      const selectedCategoryObj = mockCategories.find((cat) => cat.id === values.categoryId);
      const categoryName = selectedCategoryObj?.name || '';

      // Obter o nome da subcategoria selecionada, se houver
      let subcategoryName = '';
      if (values.subcategoryId && values.categoryId) {
        const subcategories =
          mockSubcategories[values.categoryId as keyof typeof mockSubcategories];
        if (subcategories) {
          const selectedSubcategory = subcategories.find(
            (subcat) => subcat.id === values.subcategoryId,
          );
          subcategoryName = selectedSubcategory?.name || '';
        }
      }

      // Criar objeto de serviço
      const serviceData = {
        ...values,
        price: priceInCents,
        advertiserId: user.uid,
        advertiserName: userName,
        categoryName,
        subcategoryName,
      };
      console.log('Service data:', serviceData);

      // Criar ou atualizar serviço
      const serviceService = new ServiceService();

      if (service) {
        // Atualizar serviço existente
        await serviceService.update(service.id, serviceData);
        toast.success('Serviço atualizado com sucesso!');
      } else {
        // Criar novo serviço
        await serviceService.create(serviceData as any);
        toast.success('Serviço criado com sucesso!');
      }

      // Redirecionar para a lista de serviços
      router.push('/advertiser/services');
      router.refresh();
    } catch (error) {
      console.error('Erro ao salvar serviço:', error);
      toast.error('Erro ao salvar serviço. Tente novamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              <span className="hidden sm:inline">Informações Básicas</span>
              <span className="sm:hidden">Básico</span>
            </TabsTrigger>
            <TabsTrigger value="location" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              <span className="hidden sm:inline">Localização</span>
              <span className="sm:hidden">Local</span>
            </TabsTrigger>
            <TabsTrigger value="pricing" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              <span className="hidden sm:inline">Preço e Pagamento</span>
              <span className="sm:hidden">Preço</span>
            </TabsTrigger>
            <TabsTrigger value="availability" className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <span className="hidden sm:inline">Disponibilidade</span>
              <span className="sm:hidden">Agenda</span>
            </TabsTrigger>
            <TabsTrigger value="media" className="flex items-center gap-2">
              <Image className="h-4 w-4" />
              <span className="hidden sm:inline">Mídia e Detalhes</span>
              <span className="sm:hidden">Mídia</span>
            </TabsTrigger>
          </TabsList>

          {/* Aba de Informações Básicas */}
          <TabsContent value="basic" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Título do Serviço</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Serviço de Encanamento Residencial" {...field} />
                    </FormControl>
                    <FormDescription>
                      Um título claro e descritivo para seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Descreva detalhadamente o serviço que você oferece..."
                        className="min-h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Descreva o que você oferece, sua experiência e diferenciais.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        field.onChange(value);
                        handleCategoryChange(value);
                      }}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockCategories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Escolha a categoria que melhor descreve seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="subcategoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subcategoria</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={!selectedCategory}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma subcategoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {selectedCategory &&
                          mockSubcategories[
                            selectedCategory as keyof typeof mockSubcategories
                          ]?.map((subcategory) => (
                            <SelectItem key={subcategory.id} value={subcategory.id}>
                              {subcategory.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Escolha uma subcategoria para classificar melhor seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ex: encanamento, residencial, vazamento (separadas por vírgula)"
                        value={field.value?.join(', ') || ''}
                        onChange={(e) => {
                          const tags = e.target.value
                            .split(',')
                            .map((tag) => tag.trim())
                            .filter(Boolean);
                          field.onChange(tags);
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Adicione palavras-chave para ajudar os clientes a encontrar seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </TabsContent>

          {/* Aba de Localização */}
          <TabsContent value="location" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="address.street"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Rua</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Av. Paulista" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: 1000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.complement"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Complemento</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Sala 123" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.neighborhood"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bairro</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Centro" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Cidade</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: São Paulo" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.state"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estado</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: SP" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.zipCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>CEP</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: 01310-100" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="showExactLocation"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 md:col-span-2">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Mostrar localização exata</FormLabel>
                      <FormDescription>
                        Se ativado, sua localização exata será mostrada no mapa.
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </TabsContent>

          {/* Aba de Preço e Pagamento */}
          <TabsContent value="pricing" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preço</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          className="pl-8"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>Informe o valor do seu serviço.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priceType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Preço</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo de preço" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="fixed">Preço Fixo</SelectItem>
                        <SelectItem value="hourly">Por Hora</SelectItem>
                        <SelectItem value="daily">Por Dia</SelectItem>
                        <SelectItem value="monthly">Por Mês</SelectItem>
                        <SelectItem value="negotiable">Negociável</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>Como você cobra pelo seu serviço.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Moeda</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a moeda" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="BRL">Real (BRL)</SelectItem>
                        <SelectItem value="USD">Dólar (USD)</SelectItem>
                        <SelectItem value="EUR">Euro (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>A moeda em que você cobra pelo serviço.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </TabsContent>

          {/* Aba de Disponibilidade */}
          <TabsContent value="availability" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Horários de Disponibilidade</CardTitle>
                <CardDescription>
                  Configure seus horários de disponibilidade no seu perfil de anunciante.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-6 text-center space-y-4">
                  <div className="mx-auto w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
                    <Calendar className="h-8 w-8 text-primary" />
                  </div>
                  <h3 className="text-lg font-medium">Disponibilidade Global</h3>
                  <p className="text-muted-foreground max-w-md mx-auto">
                    Seus horários de disponibilidade são configurados no seu perfil de anunciante e
                    se aplicam a todos os seus serviços. Isso facilita o gerenciamento da sua agenda
                    e evita conflitos de horários.
                  </p>
                  <Button variant="outline" asChild className="mt-2">
                    <Link href="/advertiser/profile/availability">
                      <Calendar className="mr-2 h-4 w-4" />
                      Configurar Disponibilidade
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Duração (em minutos)</FormLabel>
                  <FormControl>
                    <Input type="number" min="1" placeholder="Ex: 60" {...field} />
                  </FormControl>
                  <FormDescription>
                    Tempo médio de duração do serviço. Deixe em branco se for variável.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>

          {/* Aba de Mídia e Detalhes */}
          <TabsContent value="media" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Imagens</CardTitle>
                <CardDescription>
                  Adicione fotos do seu serviço ou trabalhos anteriores.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="images"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {field.value &&
                            field.value.map((image, index) => (
                              <div
                                key={index}
                                className="relative border rounded-md overflow-hidden h-40 group"
                              >
                                <img
                                  src={image}
                                  alt={`Imagem ${index + 1}`}
                                  className="w-full h-full object-cover"
                                />
                                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                  <Button
                                    type="button"
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => {
                                      const newImages = [...field.value];
                                      newImages.splice(index, 1);
                                      field.onChange(newImages);
                                    }}
                                  >
                                    <Trash className="h-4 w-4 mr-2" />
                                    Remover
                                  </Button>
                                </div>
                              </div>
                            ))}
                          <label className="border border-dashed rounded-md p-4 flex flex-col items-center justify-center h-40 cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-900">
                            <input
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={async (e) => {
                                const file = e.target.files?.[0];
                                if (!file) return;

                                // Verificar tipo de arquivo
                                if (!file.type.startsWith('image/')) {
                                  toast.error('Tipo de arquivo inválido', {
                                    description:
                                      'Por favor, selecione apenas imagens (JPG, PNG, etc.)',
                                  });
                                  return;
                                }

                                // Verificar tamanho do arquivo (5MB)
                                const maxSize = 5 * 1024 * 1024;
                                if (file.size > maxSize) {
                                  toast.error('Arquivo muito grande', {
                                    description: 'O tamanho máximo permitido é 5MB',
                                  });
                                  return;
                                }

                                try {
                                  // Criar FormData para enviar o arquivo
                                  const formData = new FormData();
                                  formData.append('file', file);

                                  // Enviar para a API
                                  const response = await fetch('/api/upload', {
                                    method: 'POST',
                                    body: formData,
                                  });

                                  const data = await response.json();

                                  if (!data.success) {
                                    throw new Error(
                                      data.message || 'Erro ao fazer upload da imagem',
                                    );
                                  }

                                  // Adicionar URL à lista de imagens
                                  field.onChange([...field.value, data.url]);
                                  toast.success('Upload concluído', {
                                    description: 'A imagem foi adicionada com sucesso',
                                  });
                                } catch (error) {
                                  console.error('Erro ao fazer upload:', error);
                                  toast.error('Falha no upload', {
                                    description:
                                      error instanceof Error ? error.message : 'Erro desconhecido',
                                  });
                                }

                                // Limpar o input para permitir selecionar o mesmo arquivo novamente
                                e.target.value = '';
                              }}
                            />
                            <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                            <p className="text-sm text-muted-foreground">
                              Clique para fazer upload
                            </p>
                            <p className="text-xs text-muted-foreground mt-1">
                              ou arraste uma imagem
                            </p>
                            <div className="flex gap-2 mt-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.preventDefault();
                                  const mockImageUrl = `https://source.unsplash.com/random/800x600?service&sig=${Date.now()}`;
                                  field.onChange([...field.value, mockImageUrl]);
                                  toast.success('Imagem de exemplo adicionada');
                                }}
                              >
                                <Plus className="h-4 w-4 mr-1" />
                                Usar exemplo
                              </Button>
                            </div>
                          </label>
                        </div>
                      </FormControl>
                      <FormDescription>
                        Adicione até 10 imagens para mostrar seu trabalho. A primeira imagem será a
                        principal.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Perguntas Frequentes</CardTitle>
                <CardDescription>
                  Adicione perguntas e respostas comuns sobre seu serviço.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="faqs"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <div className="text-sm font-medium">FAQs</div>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="h-8"
                              onClick={() => {
                                field.onChange([...field.value, { question: '', answer: '' }]);
                              }}
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Adicionar Pergunta
                            </Button>
                          </div>

                          {field.value?.length === 0 && (
                            <div className="text-center py-4 text-muted-foreground">
                              Nenhuma pergunta adicionada. Clique no botão acima para adicionar.
                            </div>
                          )}

                          {field.value?.map((faq, index) => (
                            <div key={index} className="border rounded-md p-4 space-y-4">
                              <div className="space-y-2">
                                <FormLabel>Pergunta</FormLabel>
                                <Input
                                  placeholder="Ex: Qual o prazo para conclusão do serviço?"
                                  value={faq.question}
                                  onChange={(e) => {
                                    const newFaqs = [...field.value];
                                    newFaqs[index].question = e.target.value;
                                    field.onChange(newFaqs);
                                  }}
                                />
                              </div>
                              <div className="space-y-2">
                                <FormLabel>Resposta</FormLabel>
                                <Textarea
                                  placeholder="Ex: O prazo médio é de 2 dias úteis, dependendo da complexidade do serviço."
                                  value={faq.answer}
                                  onChange={(e) => {
                                    const newFaqs = [...field.value];
                                    newFaqs[index].answer = e.target.value;
                                    field.onChange(newFaqs);
                                  }}
                                />
                              </div>
                              <div className="flex justify-end">
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 text-red-500"
                                  onClick={() => {
                                    const newFaqs = [...field.value];
                                    newFaqs.splice(index, 1);
                                    field.onChange(newFaqs);
                                  }}
                                >
                                  <Trash className="h-4 w-4 mr-2" />
                                  Remover
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <FormField
              control={form.control}
              name="featured"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Destacar Serviço</FormLabel>
                    <FormDescription>
                      Destaque seu serviço para aparecer no topo dos resultados de busca.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />
          </TabsContent>
        </Tabs>

        <Separator />

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="draft">Salvar como Rascunho</SelectItem>
                      <SelectItem value="published">Publicar Agora</SelectItem>
                      <SelectItem value="pending_review">Enviar para Revisão</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            <Button type="button" variant="outline" className="gap-1">
              <HelpCircle className="h-4 w-4" />
              <span className="hidden sm:inline">Pré-visualizar</span>
              <span className="sm:hidden">Preview</span>
            </Button>
          </div>

          <div className="flex gap-2">
            <Button type="button" variant="outline" asChild>
              <Link href="/advertiser/services">Cancelar</Link>
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Salvando...' : 'Salvar Serviço'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
