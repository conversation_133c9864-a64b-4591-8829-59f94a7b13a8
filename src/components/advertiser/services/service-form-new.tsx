'use client';

import { useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useAuth } from '@/hooks/use-auth';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { ServiceService } from '@/lib/services/service-service';
import { Service } from '@/types/models/service';
import { PublishStatus } from '@/types/models/base';
import { toast } from 'sonner';
import { ImageUpload } from '@/components/ui/image-upload';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Info,
  Plus,
  Trash,
  Loader2,
} from 'lucide-react';
import { AdvertiserService } from '@/services/advertiser-service';

// Schema para validação do formulário
const serviceFormSchema = z.object({
  title: z.string().min(5, {
    message: 'O título deve ter pelo menos 5 caracteres',
  }),
  description: z.string().min(20, {
    message: 'A descrição deve ter pelo menos 20 caracteres',
  }),
  categoryId: z.string({
    required_error: 'Selecione uma categoria',
  }),
  subcategoryId: z.string().optional(),
  price: z.coerce.number().min(1, {
    message: 'O preço deve ser maior que zero',
  }),
  priceType: z.enum(['fixed', 'hourly', 'daily', 'monthly', 'negotiable'], {
    required_error: 'Selecione um tipo de preço',
  }),
  currency: z.string().default('BRL'),
  useAdvertiserLocation: z.boolean().default(true),
  serviceLocationType: z.enum(['at_provider', 'at_client', 'both']).default('at_provider'),
  availability: z
    .array(
      z.object({
        days: z.array(z.enum(['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'])),
        start: z.string(),
        end: z.string(),
      }),
    )
    .default([]),
  duration: z.coerce.number().optional(),
  tags: z.array(z.string()).optional(),
  featured: z.boolean().default(false),
  status: z.enum(['draft', 'published', 'pending_review', 'archived', 'rejected']).default('draft'),
  images: z.array(z.string()).default([]),
  faqs: z
    .array(
      z.object({
        question: z.string(),
        answer: z.string(),
      }),
    )
    .default([]),
});

// Tipo para os valores do formulário
type ServiceFormValues = z.infer<typeof serviceFormSchema>;

// Categorias de serviço (mock)
const serviceCategories = [
  { id: 'cat1', name: 'Reformas e Reparos' },
  { id: 'cat2', name: 'Limpeza' },
  { id: 'cat3', name: 'Elétrica' },
  { id: 'cat4', name: 'Hidráulica' },
  { id: 'cat5', name: 'Jardinagem' },
  { id: 'cat6', name: 'Tecnologia' },
];

// Subcategorias por categoria (mock)
const serviceSubcategories: Record<string, { id: string; name: string }[]> = {
  cat1: [
    { id: 'subcat1', name: 'Pintura' },
    { id: 'subcat2', name: 'Alvenaria' },
    { id: 'subcat3', name: 'Marcenaria' },
  ],
  cat2: [
    { id: 'subcat4', name: 'Residencial' },
    { id: 'subcat5', name: 'Comercial' },
    { id: 'subcat6', name: 'Limpeza' },
  ],
  cat3: [
    { id: 'subcat7', name: 'Instalação Elétrica' },
    { id: 'subcat8', name: 'Manutenção Elétrica' },
    { id: 'subcat9', name: 'Iluminação' },
  ],
};

export default function ServiceForm({ service }: { service?: Service }) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>(service?.categoryId || '');
  
  // Obter o usuário autenticado
  const { user } = useAuth();

  // Inicializar o formulário com valores padrão ou do serviço existente
  const form = useForm<ServiceFormValues>({
    resolver: zodResolver(serviceFormSchema),
    defaultValues: service
      ? {
          title: service.title,
          description: service.description,
          categoryId: service.categoryId,
          subcategoryId: service.subcategoryId,
          price: service.price / 100, // Converter de centavos para reais
          priceType: service.priceType,
          currency: service.currency,
          useAdvertiserLocation: service.useAdvertiserLocation,
          serviceLocationType: service.serviceLocationType || 'at_provider',
          availability: service.availability,
          duration: service.duration,
          tags: service.tags,
          featured: service.featured,
          status: service.status,
          images: service.images || [],
          faqs: service.faq || [],
        }
      : {
          title: '',
          description: '',
          categoryId: '',
          subcategoryId: '',
          price: 0,
          priceType: 'fixed',
          currency: 'BRL',
          useAdvertiserLocation: true,
          serviceLocationType: 'at_provider',
          availability: [{ days: ['mon', 'tue', 'wed', 'thu', 'fri'], start: '09:00', end: '18:00' }],
          duration: 60,
          tags: [],
          featured: false,
          status: 'draft',
          images: [],
          faqs: [],
        },
  });

  // Configurar field arrays para FAQs
  const { fields: faqFields, append: appendFaq, remove: removeFaq } = useFieldArray({
    control: form.control,
    name: 'faqs',
  });

  // Atualizar subcategorias quando a categoria mudar
  const watchedCategory = form.watch('categoryId');
  if (watchedCategory !== selectedCategory) {
    setSelectedCategory(watchedCategory);
    form.setValue('subcategoryId', '');
  }

  // Função para lidar com o envio do formulário
  async function onSubmit(values: ServiceFormValues) {
    setIsSubmitting(true);

    try {
      // Converter o preço de reais para centavos
      const priceInCents = Math.round(values.price * 100);

      // Obter o ID do usuário atual
      if (!user?.id) {
        toast.error('Usuário não autenticado');
        setIsSubmitting(false);
        return;
      }

      // Obter o anunciante
      const advertiserService = new AdvertiserService();
      const advertiser = await advertiserService.getByUserId(user.id);

      if (!advertiser) {
        toast.error('Perfil de anunciante não encontrado');
        setIsSubmitting(false);
        return;
      }

      // Criar ou atualizar o serviço
      const serviceService = new ServiceService();
      const serviceData = {
        ...values,
        price: priceInCents,
        advertiserId: advertiser.id,
        advertiserName: advertiser.name,
        advertiserPhoto: advertiser.photo,
      };

      if (service) {
        // Atualizar serviço existente
        await serviceService.update(service.id, serviceData);
        toast.success('Serviço atualizado com sucesso!');
      } else {
        // Criar novo serviço
        await serviceService.create(serviceData);
        toast.success('Serviço criado com sucesso!');
      }

      // Redirecionar para a lista de serviços
      router.push('/advertiser/services');
    } catch (error) {
      console.error('Erro ao salvar serviço:', error);
      toast.error('Erro ao salvar serviço');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              Básico
            </TabsTrigger>
            <TabsTrigger value="location" className="flex items-center gap-2">
              Localização
            </TabsTrigger>
            <TabsTrigger value="pricing" className="flex items-center gap-2">
              Preço
            </TabsTrigger>
            <TabsTrigger value="media" className="flex items-center gap-2">
              Mídia
            </TabsTrigger>
            <TabsTrigger value="details" className="flex items-center gap-2">
              Detalhes
            </TabsTrigger>
          </TabsList>

          {/* Aba de Informações Básicas */}
          <TabsContent value="basic" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Título do Serviço</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: Serviço de Encanamento Residencial" {...field} />
                    </FormControl>
                    <FormDescription>
                      Um título claro e descritivo para seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Descrição</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Descreva seu serviço em detalhes..."
                        className="min-h-32"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Forneça uma descrição detalhada do serviço que você oferece.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Categoria</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma categoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {serviceCategories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Selecione a categoria que melhor descreve seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="subcategoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Subcategoria</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione uma subcategoria" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {selectedCategory &&
                          serviceSubcategories[selectedCategory]?.map((subcategory) => (
                            <SelectItem key={subcategory.id} value={subcategory.id}>
                              {subcategory.name}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Selecione uma subcategoria para classificar melhor seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel>Tags</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ex: encanamento, vazamento, conserto (separados por vírgula)"
                        value={field.value?.join(', ') || ''}
                        onChange={(e) => {
                          const value = e.target.value;
                          field.onChange(
                            value.split(',').map((tag) => tag.trim()).filter(Boolean)
                          );
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Adicione palavras-chave para ajudar os clientes a encontrar seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </TabsContent>

          {/* Aba de Localização */}
          <TabsContent value="location" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Localização do Serviço</CardTitle>
                <CardDescription>
                  Configure como a localização do serviço será exibida para os clientes.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="useAdvertiserLocation"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Usar minha localização</FormLabel>
                        <FormDescription>
                          Se ativado, o serviço usará a localização configurada no seu perfil de anunciante.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="serviceLocationType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tipo de Atendimento</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Selecione o tipo de atendimento" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="at_provider">No meu local (cliente vai até você)</SelectItem>
                          <SelectItem value="at_client">No local do cliente (você vai até o cliente)</SelectItem>
                          <SelectItem value="both">Ambos (flexibilidade de local)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Defina onde o serviço será realizado.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="rounded-lg border p-4 bg-muted/50">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="h-4 w-4 text-muted-foreground" />
                    <h3 className="text-sm font-medium">Configuração de Localização</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Para alterar sua localização e área de atendimento, acesse a{' '}
                    <Link href="/advertiser/profile?tab=location" className="text-primary hover:underline">
                      página de perfil
                    </Link>.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba de Preço */}
          <TabsContent value="pricing" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preço</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0.00"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Defina o preço do seu serviço em reais (R$).
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="priceType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Preço</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo de preço" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="fixed">Preço Fixo</SelectItem>
                        <SelectItem value="hourly">Por Hora</SelectItem>
                        <SelectItem value="daily">Por Dia</SelectItem>
                        <SelectItem value="monthly">Por Mês</SelectItem>
                        <SelectItem value="negotiable">Negociável</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Especifique como o preço do seu serviço é calculado.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="currency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Moeda</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione a moeda" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="BRL">Real Brasileiro (BRL)</SelectItem>
                        <SelectItem value="USD">Dólar Americano (USD)</SelectItem>
                        <SelectItem value="EUR">Euro (EUR)</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Selecione a moeda para o preço do seu serviço.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Disponibilidade</CardTitle>
                <CardDescription>
                  Configure os horários em que você está disponível para prestar este serviço.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-lg border p-4 bg-muted/50">
                  <div className="flex items-center gap-2 mb-2">
                    <Info className="h-4 w-4 text-muted-foreground" />
                    <h3 className="text-sm font-medium">Configuração de Disponibilidade</h3>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Para configurar sua disponibilidade, acesse a{' '}
                    <Link href="/advertiser/profile?tab=availability" className="text-primary hover:underline">
                      página de perfil
                    </Link>.
                  </p>
                </div>
              </CardContent>
            </Card>

            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Duração (minutos)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="60"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormDescription>
                    Defina a duração média do serviço em minutos.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </TabsContent>

          {/* Aba de Mídia */}
          <TabsContent value="media" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Imagens</CardTitle>
                <CardDescription>
                  Adicione imagens para mostrar seu serviço aos clientes.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="images"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <ImageUpload
                          value={field.value}
                          onChange={field.onChange}
                          maxFiles={5}
                        />
                      </FormControl>
                      <FormDescription>
                        Adicione até 5 imagens para ilustrar seu serviço.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Aba de Detalhes */}
          <TabsContent value="details" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Perguntas Frequentes</CardTitle>
                <CardDescription>
                  Adicione perguntas e respostas comuns sobre seu serviço.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="faqs"
                  render={({ field }) => (
                    <FormItem>
                      <div className="space-y-4">
                        {faqFields.map((faqField, index) => (
                          <div key={faqField.id} className="space-y-2 border rounded-md p-4">
                            <div className="flex justify-between items-center">
                              <h4 className="font-medium">Pergunta {index + 1}</h4>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeFaq(index)}
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                Remover
                              </Button>
                            </div>
                            <FormField
                              control={form.control}
                              name={`faqs.${index}.question`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Pergunta</FormLabel>
                                  <FormControl>
                                    <Input placeholder="Ex: Qual o prazo de entrega?" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name={`faqs.${index}.answer`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Resposta</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      placeholder="Ex: O prazo de entrega varia de acordo com a complexidade do serviço..."
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => appendFaq({ question: '', answer: '' })}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Adicionar Pergunta
                        </Button>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <FormField
              control={form.control}
              name="featured"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Destacar Serviço</FormLabel>
                    <FormDescription>
                      Serviços destacados aparecem no topo dos resultados de busca.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isSubmitting}
                  >
                    <FormControl>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="draft">Rascunho</SelectItem>
                      <SelectItem value="published">Publicado</SelectItem>
                      <SelectItem value="archived">Arquivado</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push('/advertiser/services')}
              disabled={isSubmitting}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {service ? 'Atualizar Serviço' : 'Criar Serviço'}
            </Button>
          </div>
        </div>
      </form>
    </Form>
  );
}
