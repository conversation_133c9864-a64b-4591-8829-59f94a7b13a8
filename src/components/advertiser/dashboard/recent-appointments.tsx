'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { formatDate } from '@/lib/utils';
import { Check, X } from 'lucide-react';

// Dados mockados para demonstração
const appointments = [
  {
    id: '1',
    clientName: '<PERSON>',
    clientImage: '',
    serviceName: 'Serviço de Encanamento',
    date: new Date(Date.now() + 86400000), // Amanhã
    time: '14:00',
    status: 'pending',
  },
  {
    id: '2',
    clientName: '<PERSON>',
    clientImage: '',
    serviceName: 'Instalação de Ar Condicionado',
    date: new Date(Date.now() + 172800000), // Depois de amanhã
    time: '10:30',
    status: 'pending',
  },
  {
    id: '3',
    clientName: 'Pedro Santos',
    clientImage: '',
    serviceName: 'Serviço de Encanamento',
    date: new Date(Date.now() + 259200000), // Em 3 dias
    time: '16:00',
    status: 'pending',
  },
];

export default function RecentAppointments() {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="space-y-4">
      {appointments.length === 0 ? (
        <div className="text-center py-4 text-muted-foreground">Nenhum agendamento pendente.</div>
      ) : (
        appointments.map((appointment) => (
          <div
            key={appointment.id}
            className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
          >
            <div className="flex items-center gap-3">
              <Avatar className="h-9 w-9">
                <AvatarImage src={appointment.clientImage} alt={appointment.clientName} />
                <AvatarFallback>{getInitials(appointment.clientName)}</AvatarFallback>
              </Avatar>
              <div>
                <div className="font-medium">{appointment.clientName}</div>
                <div className="text-sm text-muted-foreground">{appointment.serviceName}</div>
                <div className="text-xs text-muted-foreground">
                  {formatDate(appointment.date)} às {appointment.time}
                </div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="icon" className="h-8 w-8">
                <X className="h-4 w-4 text-red-500" />
                <span className="sr-only">Recusar</span>
              </Button>
              <Button variant="outline" size="icon" className="h-8 w-8">
                <Check className="h-4 w-4 text-green-500" />
                <span className="sr-only">Aceitar</span>
              </Button>
            </div>
          </div>
        ))
      )}
    </div>
  );
}
