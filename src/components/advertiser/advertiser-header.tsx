// src/components/advertiser/advertiser-header.tsx
'use client';

import { User } from 'next-auth';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { signOut } from 'next-auth/react';
import {
  Bell,
  Menu,
  Settings,
  User as UserIcon,
  LogOut,
  Plus,
  MessageSquare,
  BarChart3,
  Calendar,
  Briefcase,
  LayoutDashboard,
  Heart,
  Star,
} from 'lucide-react';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import AdvertiserSidebar from './advertiser-sidebar';
import Link from 'next/link';
import { ThemeToggle } from '@/components/theme/theme-toggle';
import { cn } from '@/lib/utils';

interface AdvertiserHeaderProps {
  user: User;
}

export default function AdvertiserHeader({ user }: AdvertiserHeaderProps) {
  const pathname = usePathname();
  const getInitials = (name: string | null | undefined) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <header className="sticky top-0 z-30 border-b border-slate-200 bg-white dark:bg-slate-950 dark:border-slate-800">
      <div className="flex h-16 items-center px-4 md:px-6">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="mr-4 md:hidden">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-full max-w-[300px]">
            <div className="flex h-full w-full flex-col bg-white dark:bg-slate-900">
              <div className="flex h-16 items-center border-b px-6">
                <Link href="/advertiser" className="flex items-center gap-2 font-bold text-xl">
                  <span className="text-primary">JáVai</span>
                  <span className="text-sm text-muted-foreground">Anunciante</span>
                </Link>
              </div>
              <div className="flex-1 overflow-auto py-2">
                <nav className="grid items-start px-2 text-sm font-medium">
                  {[
                    {
                      title: 'Dashboard',
                      href: '/advertiser/dashboard',
                      icon: LayoutDashboard,
                    },
                    {
                      title: 'Meus Serviços',
                      href: '/advertiser/services',
                      icon: Briefcase,
                    },
                    {
                      title: 'Agendamentos',
                      href: '/advertiser/appointments',
                      icon: Calendar,
                    },
                    {
                      title: 'Analytics',
                      href: '/advertiser/analytics',
                      icon: BarChart3,
                    },
                    {
                      title: 'Mensagens',
                      href: '/advertiser/messages',
                      icon: MessageSquare,
                      badge: 3,
                    },
                    {
                      title: 'Avaliações',
                      href: '/advertiser/reviews',
                      icon: Star,
                    },
                    {
                      title: 'Favoritos',
                      href: '/advertiser/favorites',
                      icon: Heart,
                    },
                    {
                      title: 'Notificações',
                      href: '/advertiser/notifications',
                      icon: Bell,
                      badge: 5,
                    },
                    {
                      title: 'Perfil',
                      href: '/advertiser/profile',
                      icon: UserIcon,
                    },
                    {
                      title: 'Configurações',
                      href: '/advertiser/settings',
                      icon: Settings,
                    },
                  ].map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        'flex items-center gap-3 rounded-lg px-3 py-2 transition-all hover:text-primary',
                        pathname === item.href || pathname.startsWith(`${item.href}/`)
                          ? 'bg-slate-100 dark:bg-slate-800 text-primary'
                          : 'text-slate-500 hover:bg-slate-100 dark:hover:bg-slate-800',
                      )}
                    >
                      <item.icon className="h-4 w-4" />
                      <span>{item.title}</span>
                      {item.badge && (
                        <span className="ml-auto flex h-5 w-5 items-center justify-center rounded-full bg-primary dark:bg-slate-800 text-[10px] font-medium text-white">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  ))}
                </nav>
              </div>
              <div className="border-t p-4">
                <div className="flex items-center gap-3 rounded-lg bg-slate-100 dark:bg-slate-800 px-3 py-2">
                  <div className="text-sm">
                    <div className="font-medium">Plano Profissional</div>
                    <div className="text-xs text-muted-foreground">Expira em 28 dias</div>
                  </div>
                  <Link
                    href="/subscription"
                    className="ml-auto text-xs text-primary hover:underline"
                  >
                    Gerenciar
                  </Link>
                </div>
              </div>
            </div>
          </SheetContent>
        </Sheet>

        <div className="ml-auto flex items-center gap-2">
          <ThemeToggle />

          <Button variant="outline" size="sm" asChild>
            <Link href="/advertiser/services/new">
              <Plus className="h-4 w-4 mr-2" />
              Novo Serviço
            </Link>
          </Button>

          <Button variant="ghost" size="icon" className="relative">
            <MessageSquare className="h-5 w-5" />
            <span className="absolute top-1 right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary dark:bg-slate-800 text-[10px] font-medium text-white">
              3
            </span>
            <span className="sr-only">Mensagens</span>
          </Button>

          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            <span className="absolute top-1 right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary dark:bg-slate-800 text-[10px] font-medium text-white">
              5
            </span>
            <span className="sr-only">Notificações</span>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={user.image || ''} alt={user.name || 'Usuário'} />
                  <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel className="flex flex-col items-center gap-2">
                <span className="text-primary">{user.name || 'Usuário'}</span>
                <span className="text-sm text-muted-foreground">{user.email || 'Minha Conta'}</span>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/advertiser/profile">
                  <UserIcon className="mr-2 h-4 w-4" />
                  <span>Perfil</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/advertiser/settings">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Configurações</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => signOut({ callbackUrl: '/' })}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sair</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
