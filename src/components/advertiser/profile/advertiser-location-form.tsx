'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { ZipCodeInput, ZipCodeAddress } from '@/components/ui/zipcode-input';
import { Advertiser } from '@/types/models/advertiser';
import { AdvertiserService } from '@/services/advertiser-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LocationSelector } from '@/components/ui/location-selector';
import { toast } from 'sonner';
import { Loader2, MapPin } from 'lucide-react';
import { Slider } from '@/components/ui/slider';

const locationFormSchema = z.object({
  address: z.object({
    street: z.string().min(1, { message: 'A rua é obrigatória' }),
    number: z.string().optional(),
    complement: z.string().optional(),
    neighborhood: z.string().min(1, { message: 'O bairro é obrigatório' }),
    city: z.string().min(1, { message: 'A cidade é obrigatória' }),
    cityId: z.string().optional(),
    cityNormalized: z.string().optional(),
    state: z.string().min(1, { message: 'O estado é obrigatório' }),
    stateId: z.string().optional(),
    stateUF: z.string().optional(),
    zipCode: z.string().min(1, { message: 'O CEP é obrigatório' }),
    country: z.string().default('Brasil'),
  }),
  coordinates: z
    .object({
      latitude: z.number().optional(),
      longitude: z.number().optional(),
    })
    .optional(),
  showExactLocation: z.boolean().default(false),
  serviceLocationType: z.enum(['fixed', 'mobile', 'both']),
  serviceRadius: z.number().min(1).max(100).optional(),
});

type LocationFormValues = z.infer<typeof locationFormSchema>;

interface AdvertiserLocationFormProps {
  advertiser: Advertiser | null;
  setAdvertiser: (advertiser: Advertiser) => void;
}

export function AdvertiserLocationForm({ advertiser, setAdvertiser }: AdvertiserLocationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);

  const form = useForm<LocationFormValues>({
    resolver: zodResolver(locationFormSchema),
    defaultValues: {
      address: {
        street: advertiser?.address?.street || '',
        number: advertiser?.address?.number || '',
        complement: advertiser?.address?.complement || '',
        neighborhood: advertiser?.address?.neighborhood || '',
        city: advertiser?.address?.city || '',
        state: advertiser?.address?.state || '',
        zipCode: advertiser?.address?.zipCode || '',
        country: advertiser?.address?.country || 'Brasil',
      },
      coordinates: advertiser?.coordinates || undefined,
      showExactLocation: advertiser?.showExactLocation || false,
      serviceLocationType: advertiser?.serviceLocationType || 'fixed',
      serviceRadius: advertiser?.serviceRadius || 10,
    },
  });

  const serviceLocationType = form.watch('serviceLocationType');

  async function onSubmit(data: LocationFormValues) {
    if (!advertiser) return;

    setIsSubmitting(true);

    try {
      const advertiserService = new AdvertiserService();
      const updatedAdvertiser = await advertiserService.update(advertiser.id, data);

      setAdvertiser(updatedAdvertiser);
      toast.success('Localização atualizada com sucesso');
    } catch (error) {
      console.error('Error updating location:', error);
      toast.error('Erro ao atualizar localização');
    } finally {
      setIsSubmitting(false);
    }
  }

  const detectLocation = () => {
    setIsDetectingLocation(true);

    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const coords = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          };

          try {
            // Obter endereço a partir das coordenadas usando a API de Geocoding
            const response = await fetch(
              `https://nominatim.openstreetmap.org/reverse?format=json&lat=${coords.latitude}&lon=${coords.longitude}&zoom=18&addressdetails=1`,
              { headers: { 'Accept-Language': 'pt-BR' } },
            );
            const data = await response.json();

            // Preencher o formulário com os dados obtidos
            form.setValue('coordinates', coords);

            if (data.address) {
              form.setValue('address.street', data.address.road || '');
              form.setValue(
                'address.neighborhood',
                data.address.suburb || data.address.neighbourhood || '',
              );
              form.setValue(
                'address.city',
                data.address.city || data.address.town || data.address.village || '',
              );
              form.setValue('address.state', data.address.state || '');
              form.setValue('address.zipCode', data.address.postcode || '');
              form.setValue('address.country', data.address.country || 'Brasil');
            }

            toast.success('Localização detectada com sucesso');
          } catch (error) {
            console.error('Erro ao obter endereço:', error);
            toast.error('Erro ao obter endereço a partir das coordenadas');
          } finally {
            setIsDetectingLocation(false);
          }
        },
        (error) => {
          console.error('Erro ao obter localização:', error);
          toast.error('Erro ao detectar localização');
          setIsDetectingLocation(false);
        },
      );
    } else {
      toast.error('Geolocalização não suportada pelo seu navegador');
      setIsDetectingLocation(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Localização</CardTitle>
        <CardDescription>Configure sua localização e preferências de atendimento.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="flex justify-end">
              <Button
                type="button"
                variant="outline"
                onClick={detectLocation}
                disabled={isDetectingLocation}
              >
                {isDetectingLocation ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <MapPin className="mr-2 h-4 w-4" />
                )}
                Detectar Localização
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="address.street"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rua</FormLabel>
                    <FormControl>
                      <Input placeholder="Nome da rua" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Número</FormLabel>
                    <FormControl>
                      <Input placeholder="Número" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.complement"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Complemento</FormLabel>
                    <FormControl>
                      <Input placeholder="Apto, sala, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.neighborhood"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bairro</FormLabel>
                    <FormControl>
                      <Input placeholder="Bairro" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.stateId"
                render={({ field }) => (
                  <FormItem className="md:col-span-2">
                    <LocationSelector
                      selectedState={field.value || ''}
                      selectedCity={form.watch('address.cityId') || ''}
                      onStateChange={(stateId, stateName, stateUF) => {
                        field.onChange(stateId);
                        form.setValue('address.state', stateName);
                        form.setValue('address.stateUF', stateUF);
                      }}
                      onCityChange={(cityId, cityName, normalizedName) => {
                        form.setValue('address.cityId', cityId);
                        form.setValue('address.city', cityName);
                        form.setValue('address.cityNormalized', normalizedName);
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.zipCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>CEP</FormLabel>
                    <FormControl>
                      <ZipCodeInput
                        value={field.value}
                        onChange={field.onChange}
                        onAddressFound={(address) => {
                          // Preencher os campos de endereço automaticamente
                          form.setValue('address.street', address.street);
                          form.setValue('address.neighborhood', address.neighborhood);
                          form.setValue('address.city', address.city);
                          form.setValue('address.state', address.state);

                          // Buscar o estado e a cidade no seletor de localização
                          // Isso será implementado em uma etapa futura se necessário
                        }}
                      />
                    </FormControl>
                    <FormDescription>
                      Digite o CEP para preencher o endereço automaticamente
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address.country"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>País</FormLabel>
                    <FormControl>
                      <Input placeholder="País" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="showExactLocation"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Mostrar localização exata</FormLabel>
                    <FormDescription>
                      Se ativado, sua localização exata será mostrada no mapa. Caso contrário,
                      apenas a região aproximada será exibida.
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="serviceLocationType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tipo de Atendimento</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o tipo de atendimento" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="fixed">Fixo (Cliente vai até você)</SelectItem>
                      <SelectItem value="mobile">Móvel (Você vai até o cliente)</SelectItem>
                      <SelectItem value="both">Ambos (Fixo e Móvel)</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>Defina como você prefere atender seus clientes.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {(serviceLocationType === 'mobile' || serviceLocationType === 'both') && (
              <FormField
                control={form.control}
                name="serviceRadius"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Raio de Atendimento: {field.value} km</FormLabel>
                    <FormControl>
                      <Slider
                        min={1}
                        max={100}
                        step={1}
                        defaultValue={[field.value || 10]}
                        onValueChange={(value) => field.onChange(value[0])}
                      />
                    </FormControl>
                    <FormDescription>
                      Defina a distância máxima que você está disposto a percorrer para atender seus
                      clientes.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Salvar Alterações
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
