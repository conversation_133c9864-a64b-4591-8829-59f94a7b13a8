'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import * as z from 'zod';
import { Advertiser } from '@/types/models/advertiser';
import { AdvertiserService } from '@/services/advertiser-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2, Plus, Trash } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const availabilityFormSchema = z.object({
  availability: z
    .array(
      z.object({
        days: z.array(z.enum(['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'])),
        start: z.string(),
        end: z.string(),
      }),
    )
    .min(1, { message: 'Adicione pelo menos um período de disponibilidade' }),
});

type AvailabilityFormValues = z.infer<typeof availabilityFormSchema>;

interface AdvertiserAvailabilityFormProps {
  advertiser: Advertiser | null;
  setAdvertiser: (advertiser: Advertiser) => void;
}

const dayOptions = [
  { id: 'mon', label: 'Segunda-feira' },
  { id: 'tue', label: 'Terça-feira' },
  { id: 'wed', label: 'Quarta-feira' },
  { id: 'thu', label: 'Quinta-feira' },
  { id: 'fri', label: 'Sexta-feira' },
  { id: 'sat', label: 'Sábado' },
  { id: 'sun', label: 'Domingo' },
];

const timeOptions = Array.from({ length: 24 * 4 }, (_, i) => {
  const hour = Math.floor(i / 4);
  const minute = (i % 4) * 15;
  return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
});

export function AdvertiserAvailabilityForm({
  advertiser,
  setAdvertiser,
}: AdvertiserAvailabilityFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<AvailabilityFormValues>({
    resolver: zodResolver(availabilityFormSchema),
    defaultValues: {
      availability:
        advertiser?.availability && advertiser.availability.length > 0
          ? advertiser.availability
          : [{ days: ['mon', 'tue', 'wed', 'thu', 'fri'], start: '09:00', end: '18:00' }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'availability',
  });

  async function onSubmit(data: AvailabilityFormValues) {
    if (!advertiser) return;

    setIsSubmitting(true);

    try {
      const advertiserService = new AdvertiserService();
      const updatedAdvertiser = await advertiserService.update(advertiser.id, data);

      setAdvertiser(updatedAdvertiser);
      toast.success('Disponibilidade atualizada com sucesso');
    } catch (error) {
      console.error('Error updating availability:', error);
      toast.error('Erro ao atualizar disponibilidade');
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Disponibilidade</CardTitle>
        <CardDescription>
          Configure seus horários de disponibilidade para atendimento.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="space-y-4">
              {fields.map((field: any, index: number) => (
                <div key={field.id} className="border rounded-md p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Período {index + 1}</h3>
                    {fields.length > 1 && (
                      <Button type="button" variant="ghost" size="sm" onClick={() => remove(index)}>
                        <Trash className="h-4 w-4 mr-2" />
                        Remover
                      </Button>
                    )}
                  </div>

                  <FormField
                    control={form.control}
                    name={`availability.${index}.days`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Dias da Semana</FormLabel>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          {dayOptions.map((day) => (
                            <FormItem
                              key={day.id}
                              className="flex flex-row items-start space-x-3 space-y-0"
                            >
                              <FormControl>
                                <Checkbox
                                  checked={field.value?.includes(day.id as any)}
                                  onCheckedChange={(checked) => {
                                    const updatedDays = checked
                                      ? [...field.value, day.id]
                                      : field.value.filter((d) => d !== day.id);
                                    field.onChange(updatedDays);
                                  }}
                                />
                              </FormControl>
                              <FormLabel className="font-normal">{day.label}</FormLabel>
                            </FormItem>
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name={`availability.${index}.start`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Horário de Início</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o horário" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time} value={time}>
                                  {time}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name={`availability.${index}.end`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Horário de Término</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Selecione o horário" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {timeOptions.map((time) => (
                                <SelectItem key={time} value={time}>
                                  {time}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={() =>
                  append({
                    days: ['mon', 'tue', 'wed', 'thu', 'fri'],
                    start: '09:00',
                    end: '18:00',
                  })
                }
              >
                <Plus className="h-4 w-4 mr-2" />
                Adicionar Período
              </Button>
            </div>

            <FormDescription>
              Você pode adicionar múltiplos períodos de disponibilidade para diferentes dias da
              semana. Por exemplo, um período para dias úteis e outro para finais de semana.
            </FormDescription>

            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Salvar Alterações
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
