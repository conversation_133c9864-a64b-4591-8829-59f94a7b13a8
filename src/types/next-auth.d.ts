// src/types/next-auth.d.ts
import NextA<PERSON>, { DefaultSession } from "next-auth";
import { JWT } from "next-auth/jwt";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      idToken?: string;
      firebaseToken?: string;
    } & DefaultSession["user"];
  }

  interface User {
    id: string;
    idToken?: string;
    firebaseToken?: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    idToken?: string;
    firebaseToken?: string;
  }
}
