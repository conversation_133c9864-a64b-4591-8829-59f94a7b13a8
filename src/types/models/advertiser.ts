// src/types/models/advertiser.ts
import { BaseModel, LocationModel, ContactModel, SchedulableModel, RateableModel } from './base';

/**
 * Modelo para anunciantes (prestadores de serviço, vendedores, etc.)
 */
export interface Advertiser
  extends BaseModel,
    LocationModel,
    ContactModel,
    SchedulableModel,
    RateableModel {
  // Informações básicas
  userId: string;
  name: string;
  email: string;
  phone?: string;
  photo?: string;
  bio?: string;

  // Informações profissionais
  businessName?: string;
  businessType?: 'individual' | 'company';
  document?: string; // CPF ou CNPJ
  website?: string;
  socialMedia?: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    youtube?: string;
  };

  // Informações de verificação
  verified?: boolean;
  verificationDate?: Date;
  verificationDocuments?: string[]; // URLs dos documentos

  // Informações de assinatura
  subscriptionId?: string;
  subscriptionStatus?: 'active' | 'canceled' | 'past_due' | 'trialing' | 'incomplete';
  planId?: string;
  planName?: string;

  // Configurações de serviço
  serviceLocationType?: 'fixed' | 'mobile' | 'both'; // Tipo de localização de serviço
  serviceRadius?: number; // Raio de atendimento em km (para serviços móveis)

  // Estatísticas
  totalServices?: number;
  totalAppointments?: number;
  completedAppointments?: number;
  canceledAppointments?: number;

  // Configurações de notificação
  notificationPreferences?: {
    email?: boolean;
    push?: boolean;
    sms?: boolean;
  };

  // Configurações de privacidade
  privacySettings?: {
    showPhone?: boolean;
    showEmail?: boolean;
    showAddress?: boolean;
  };
}

/**
 * Valores do formulário de anunciante
 */
export interface AdvertiserFormValues {
  // Informações básicas
  name: string;
  email: string;
  phone?: string;
  photo?: string;
  bio?: string;

  // Informações profissionais
  businessName?: string;
  businessType?: 'individual' | 'company';
  document?: string;
  website?: string;
  socialMedia?: {
    instagram?: string;
    facebook?: string;
    twitter?: string;
    linkedin?: string;
    youtube?: string;
  };

  // Informações de localização
  address: {
    street: string;
    number?: string;
    complement?: string;
    neighborhood: string;
    city: string;
    cityId?: string;
    cityNormalized?: string;
    state: string;
    stateId?: string;
    stateUF?: string;
    zipCode: string;
    country: string;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  showExactLocation?: boolean;

  // Configurações de serviço
  serviceLocationType?: 'fixed' | 'mobile' | 'both';
  serviceRadius?: number;

  // Disponibilidade
  availability?: {
    days: ('sun' | 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat')[];
    start: string; // Formato HH:MM
    end: string; // Formato HH:MM
  }[];

  // Configurações de contato
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  contactWhatsapp?: string;
  preferredContactMethod?: 'phone' | 'email' | 'whatsapp';

  // Configurações de privacidade
  privacySettings?: {
    showPhone?: boolean;
    showEmail?: boolean;
    showAddress?: boolean;
  };
}
