import {
  PublishableModel,
  PriceableModel,
  ContactModel,
  SchedulableModel,
  RateableModel,
  AdvertiserOwnedModel,
  StatisticsModel,
  SubscriptionLimitedModel,
} from './base';

/**
 * Categoria de serviço
 */
export interface ServiceCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  parentId?: string; // Para subcategorias
}

/**
 * Modelo para serviços
 */
export interface Service
  extends PublishableModel,
    PriceableModel,
    ContactModel,
    SchedulableModel,
    RateableModel,
    AdvertiserOwnedModel,
    StatisticsModel,
    SubscriptionLimitedModel {
  // Campos específicos de serviços
  categoryId: string;
  categoryName: string;
  subcategoryId?: string;
  subcategoryName?: string;

  // Detalhes específicos do serviço
  experience?: number; // Anos de experiência
  qualifications?: string[]; // Qualificações, certificações
  portfolio?: {
    title: string;
    description?: string;
    imageUrl: string;
    date?: Date;
  }[];

  // Informações adicionais
  faq?: {
    question: string;
    answer: string;
  }[];

  // Opções de serviço
  options?: {
    id: string;
    name: string;
    description?: string;
    price: number;
    duration?: number; // em minutos
  }[];

  // Requisitos para o serviço
  requirements?: string[];

  // Configuração de localização
  useAdvertiserLocation: boolean; // Se true, usa a localização do anunciante
  serviceLocationType?: 'at_provider' | 'at_client' | 'both'; // Onde o serviço é realizado

  // Informações do anunciante (expandidas)
  advertiser?: {
    id: string;
    name: string;
    photo?: string;
    address?: {
      street: string;
      number?: string;
      complement?: string;
      neighborhood: string;
      city: string;
      cityId?: string;
      cityNormalized?: string;
      state: string;
      stateId?: string;
      stateUF?: string;
      zipCode: string;
      country: string;
    };
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
}
