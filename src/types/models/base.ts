/**
 * Modelo base para todos os documentos no Firestore
 */
export interface BaseModel {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // ID do usuário que criou o documento
}

/**
 * Status de publicação para modelos publicáveis
 */
export type PublishStatus = 'draft' | 'published' | 'archived' | 'pending_review' | 'rejected';

/**
 * Interface para modelos que podem ser publicados (anúncios, conteúdos, etc.)
 */
export interface PublishableModel extends BaseModel {
  status: PublishStatus;
  publishedAt?: Date;
  title: string;
  description: string;
  images?: string[]; // URLs das imagens
  featured?: boolean; // Destaque (para anúncios premium)
  views?: number; // Contador de visualizações
  likes?: number; // Contador de curtidas
  reports?: number; // Contador de denúncias
  reportReasons?: string[]; // Razões das denúncias
  tags?: string[]; // Tags para categorização e busca
}

/**
 * Interface para modelos que têm preço
 */
export interface PriceableModel {
  price: number;
  currency: string;
  priceType: 'fixed' | 'hourly' | 'daily' | 'monthly' | 'negotiable';
  discount?: number; // Desconto em porcentagem
  originalPrice?: number; // Preço original antes do desconto
}

/**
 * Interface para modelos que têm localização
 */
export interface LocationModel {
  address: {
    street: string;
    number?: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  showExactLocation?: boolean; // Se deve mostrar a localização exata ou aproximada
}

/**
 * Interface para modelos que têm contato
 */
export interface ContactModel {
  contactName?: string;
  contactPhone?: string;
  contactEmail?: string;
  contactWhatsapp?: string;
  preferredContactMethod?: 'phone' | 'email' | 'whatsapp';
  availableHours?: {
    start: string; // Formato HH:MM
    end: string; // Formato HH:MM
    days: ('mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun')[];
  }[];
}

/**
 * Interface para modelos que podem ser agendados
 */
export interface SchedulableModel {
  availability?: {
    start: string; // Formato HH:MM
    end: string; // Formato HH:MM
    days: ('mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun')[];
  }[];
  duration?: number; // Duração em minutos
  bookingLeadTime?: number; // Tempo mínimo de antecedência para agendamento (em horas)
  bookingCancellationTime?: number; // Tempo mínimo para cancelamento sem penalidade (em horas)
}

/**
 * Interface para modelos que têm avaliações
 */
export interface RateableModel {
  rating?: number; // Média das avaliações (1-5)
  ratingCount?: number; // Número de avaliações
  reviews?: {
    userId: string;
    userName: string;
    rating: number;
    comment: string;
    createdAt: Date;
    response?: {
      comment: string;
      createdAt: Date;
    };
  }[];
}

/**
 * Interface para modelos que pertencem a um usuário anunciante
 */
export interface AdvertiserOwnedModel {
  advertiserId: string;
  advertiserName: string;
  advertiserPhoto?: string;
  advertiserRating?: number;
  advertiserVerified?: boolean;
}

/**
 * Interface para modelos que têm estatísticas
 */
export interface StatisticsModel {
  impressions?: number; // Número de impressões
  clicks?: number; // Número de cliques
  inquiries?: number; // Número de consultas/contatos
  conversions?: number; // Número de conversões (agendamentos, compras, etc.)
  ctr?: number; // Click-through rate (clicks / impressions)
  conversionRate?: number; // Taxa de conversão (conversions / clicks)
}

/**
 * Interface para modelos que têm limites baseados no plano de assinatura
 */
export interface SubscriptionLimitedModel {
  planId?: string; // ID do plano de assinatura
  planName?: string; // Nome do plano de assinatura
  planFeatures?: string[]; // Recursos disponíveis no plano
  planLimits?: {
    [key: string]: number; // Limites específicos do plano (ex: maxImages: 10)
  };
}
