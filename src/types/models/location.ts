/**
 * Modelo para armazenar dados de localização obtidos por coordenadas
 */
export interface GeoLocation {
  id: string; // Formato: "${latitude}_${longitude}"
  latitude: number;
  longitude: number;
  address: {
    city?: string;
    town?: string;
    village?: string;
    municipality?: string;
    county?: string;
    state?: string;
    country?: string;
    country_code?: string;
    postcode?: string;
    road?: string;
    suburb?: string;
    neighbourhood?: string;
    [key: string]: any; // Para outros campos que possam vir da API
  };
  display_name?: string;
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date; // Data de expiração do cache (ex: 30 dias)
  source: 'nominatim' | 'google' | 'manual';
  raw?: any; // Resposta completa da API para referência
}

/**
 * Modelo para armazenar países e seus estados
 */
export interface Country {
  id: string; // Código do país (ex: "BR")
  name: string; // Nome do país
  name_en?: string; // Nome em inglês
  states: {
    code: string; // Código do estado (ex: "SP")
    name: string; // Nome do estado
  }[];
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date; // Data de expiração do cache (ex: 1 ano)
  source: 'ibge' | 'manual';
}

/**
 * Modelo para armazenar estados e suas cidades
 */
export interface State {
  id: string; // Formato: "${country_code}_${state_code}" (ex: "BR_SP")
  country: string; // Código do país
  state: string; // Código do estado
  state_full: string; // Nome completo do estado
  cities: {
    id: string; // ID da cidade na API de origem
    name: string; // Nome da cidade
    normalized_name: string; // Nome normalizado (sem acentos, minúsculas)
  }[];
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date; // Data de expiração do cache (ex: 1 ano)
  source: 'ibge' | 'manual';
}

/**
 * Modelo para armazenar dados de CEP
 */
export interface ZipCode {
  id: string; // O CEP sem hífens (ex: "01310100")
  zipCode: string; // CEP formatado (ex: "01310-100")
  street: string;
  neighborhood: string;
  city: string;
  state: string;
  stateCode: string; // UF (ex: "SP")
  complement?: string;
  ibgeCode?: string; // Código IBGE da cidade
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date; // Data de expiração do cache (ex: 1 ano)
  source: 'viacep' | 'correios' | 'manual';
  raw?: any; // Resposta completa da API para referência
}
