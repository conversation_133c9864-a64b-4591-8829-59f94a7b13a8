// src/types/models/product.ts
import {
  PublishableModel,
  PriceableModel,
  LocationModel,
  ContactModel,
  RateableModel,
  AdvertiserOwnedModel,
  StatisticsModel,
  SubscriptionLimitedModel
} from './base';

/**
 * Categoria de produto
 */
export interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  parentId?: string; // Para subcategorias
}

/**
 * Modelo para produtos
 */
export interface Product extends 
  PublishableModel,
  PriceableModel,
  LocationModel,
  ContactModel,
  RateableModel,
  AdvertiserOwnedModel,
  StatisticsModel,
  SubscriptionLimitedModel 
{
  // Campos específicos de produtos
  categoryId: string;
  categoryName: string;
  subcategoryId?: string;
  subcategoryName?: string;
  
  // Detalhes específicos do produto
  brand?: string;
  model?: string;
  condition: 'new' | 'like_new' | 'good' | 'fair' | 'poor';
  quantity: number;
  
  // Especificações técnicas
  specifications?: {
    name: string;
    value: string;
  }[];
  
  // Informações adicionais
  warranty?: {
    hasWarranty: boolean;
    period?: number; // em meses
    description?: string;
  };
  
  // Opções de entrega
  delivery?: {
    hasDelivery: boolean;
    freeDelivery: boolean;
    deliveryPrice?: number;
    deliveryArea?: string[];
  };
  
  // Variações do produto
  variations?: {
    id: string;
    name: string;
    options: {
      id: string;
      name: string;
      price?: number; // Preço adicional
      quantity: number;
    }[];
  }[];
}

/**
 * Valores do formulário de produto
 */
export interface ProductFormValues {
  title: string;
  description: string;
  categoryId: string;
  subcategoryId?: string;
  price: number; // Em reais (será convertido para centavos)
  priceType: 'fixed' | 'negotiable';
  currency: string;
  condition: 'new' | 'like_new' | 'good' | 'fair' | 'poor';
  quantity: number;
  brand?: string;
  model?: string;
  address: {
    street: string;
    number?: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  showExactLocation?: boolean;
  tags?: string[];
  featured?: boolean;
  status: 'draft' | 'published' | 'archived' | 'pending_review' | 'rejected';
  specifications?: {
    name: string;
    value: string;
  }[];
  warranty?: {
    hasWarranty: boolean;
    period?: number;
    description?: string;
  };
  delivery?: {
    hasDelivery: boolean;
    freeDelivery: boolean;
    deliveryPrice?: number;
    deliveryArea?: string[];
  };
}
