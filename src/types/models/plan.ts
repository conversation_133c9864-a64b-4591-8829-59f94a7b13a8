// src/types/models/plan.ts
import { BaseModel } from './base';

export interface PlanFeature {
  name: string;
  included: boolean;
  limit?: number;
}

export interface PlanModules {
  services: boolean;
  properties: boolean;
  vehicles: boolean;
  products: boolean;
}

export interface PlanLimits {
  services: number;
  properties: number;
  vehicles: number;
  products: number;
  featured: number;
}

export interface Plan extends BaseModel {
  name: string;
  description: string;
  price: number; // em centavos
  interval: 'month' | 'year';
  features: PlanFeature[];
  popular?: boolean;
  modules: PlanModules;
  limits: PlanLimits;
  stripeProductId?: string;
  stripePriceId?: string;
  active?: boolean;
  order?: number;
}

export interface PlanFormValues {
  name: string;
  description: string;
  price: number; // em reais (será convertido para centavos)
  interval: 'month' | 'year';
  features: PlanFeature[];
  popular: boolean;
  modules: PlanModules;
  limits: PlanLimits;
  active: boolean;
  order: number;
}
