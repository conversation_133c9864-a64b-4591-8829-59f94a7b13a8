// src/types/subscription.ts

export type ModuleAccess = {
  services: boolean;
  properties: boolean;
  vehicles: boolean;
  products: boolean;
};

export type AdLimits = {
  services: number;
  properties: number;
  vehicles: number;
  products: number;
};

export type PlanFeature = {
  id: string;
  name: string;
  description: string;
  included: boolean;
};

export type Plan = {
  id: string;
  name: string;
  description: string;
  price: number; // in cents
  interval: 'month' | 'year';
  moduleAccess: ModuleAccess;
  adLimits: AdLimits;
  features: PlanFeature[];
  stripePriceId: string;
  isPopular?: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type SubscriptionStatus =
  | 'active'
  | 'canceled'
  | 'incomplete'
  | 'incomplete_expired'
  | 'past_due'
  | 'paused'
  | 'trialing'
  | 'unpaid';

export type Subscription = {
  id: string;
  userId: string;
  planId: string;
  status: SubscriptionStatus;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  stripeSubscriptionId: string;
  stripeCustomerId: string;
  createdAt: Date;
  updatedAt: Date;
};
