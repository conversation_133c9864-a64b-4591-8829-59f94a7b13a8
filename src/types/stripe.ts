// src/types/stripe.ts
import { Stripe } from 'stripe';
import { SubscriptionStatus } from './subscription';

// Extend Stripe types to include properties that might be missing in the type definitions
export interface StripeSubscription extends Stripe.Subscription {
  current_period_start: number;
  current_period_end: number;
  cancel_at_period_end: boolean;
  status: SubscriptionStatus;
  customer: string;
}

export interface StripeInvoice extends Stripe.Invoice {
  subscription?: string;
  period_start: number;
  period_end: number;
}

export interface StripeCheckoutSession extends Omit<Stripe.Checkout.Session, 'metadata'> {
  metadata?: {
    userId?: string;
    planId?: string;
    [key: string]: string | undefined;
  };
}
