// src/middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Este middleware será executado em todas as requisições
export async function middleware(request: NextRequest) {
  // Apenas continua o processamento normal
  return NextResponse.next();
}

// Configurar o middleware para ser executado em todas as rotas
export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
