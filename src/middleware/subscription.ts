// src/middleware/subscription.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getUserActiveSubscription } from '@/services/subscriptions';
import { getPlanById } from '@/services/plans';

// Middleware to check if user has access to a specific module
export async function checkModuleAccess(
  req: NextRequest,
  module: keyof ModuleAccess
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return {
        hasAccess: false,
        redirect: '/auth/login',
      };
    }
    
    // Get user's active subscription
    const subscription = await getUserActiveSubscription(session.user.id);
    
    // If no subscription, redirect to plans page
    if (!subscription) {
      return {
        hasAccess: false,
        redirect: '/dashboard/subscription/plans',
        message: 'Você precisa ter uma assinatura ativa para acessar este recurso.',
      };
    }
    
    // Get subscription plan
    const plan = await getPlanById(subscription.planId);
    
    if (!plan) {
      return {
        hasAccess: false,
        redirect: '/dashboard/subscription/plans',
        message: 'Plano não encontrado. Entre em contato com o suporte.',
      };
    }
    
    // Check if plan has access to the requested module
    if (!plan.moduleAccess[module]) {
      return {
        hasAccess: false,
        redirect: '/dashboard/subscription',
        message: `Seu plano atual não inclui acesso ao módulo de ${getModuleName(module)}.`,
      };
    }
    
    return {
      hasAccess: true,
      subscription,
      plan,
    };
  } catch (error) {
    console.error('Error checking module access:', error);
    return {
      hasAccess: false,
      redirect: '/dashboard',
      message: 'Ocorreu um erro ao verificar seu acesso. Tente novamente.',
    };
  }
}

// Middleware to check if user has reached the ad limit for a specific module
export async function checkAdLimit(
  req: NextRequest,
  module: keyof AdLimits,
  currentCount: number
) {
  try {
    const moduleAccess = await checkModuleAccess(req, module as keyof ModuleAccess);
    
    if (!moduleAccess.hasAccess) {
      return moduleAccess;
    }
    
    const { plan } = moduleAccess;
    
    // Check if user has reached the ad limit
    if (currentCount >= plan.adLimits[module]) {
      return {
        hasAccess: false,
        redirect: '/dashboard/subscription',
        message: `Você atingiu o limite de anúncios para ${getModuleName(module)}. Faça upgrade do seu plano para adicionar mais.`,
      };
    }
    
    return {
      hasAccess: true,
      subscription: moduleAccess.subscription,
      plan,
      remainingAds: plan.adLimits[module] - currentCount,
    };
  } catch (error) {
    console.error('Error checking ad limit:', error);
    return {
      hasAccess: false,
      redirect: '/dashboard',
      message: 'Ocorreu um erro ao verificar seu limite de anúncios. Tente novamente.',
    };
  }
}

// Helper function to get module name in Portuguese
function getModuleName(module: string): string {
  switch (module) {
    case 'services':
      return 'Serviços';
    case 'properties':
      return 'Imóveis';
    case 'vehicles':
      return 'Veículos';
    case 'products':
      return 'Produtos';
    default:
      return module;
  }
}
