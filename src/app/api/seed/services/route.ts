import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase/client';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { Service } from '@/types/models/service';

// Função para gerar serviços de teste
export async function GET() {
  try {
    const servicesCollection = collection(db, 'services');

    // Lista de serviços de teste
    const testServices = [
      {
        title: 'Serviço de Encanamento',
        description:
          'Conserto de vazamentos, instalação de torneiras, chuveiros e outros serviços de encanamento.',
        categoryId: 'servicos-domesticos',
        categoryName: 'Serviços Domésticos',
        subcategoryId: 'encanamento',
        subcategoryName: 'Encanamento',
        price: 150,
        priceType: 'hourly',
        currency: 'BRL',
        address: {
          street: 'Rua Exemplo',
          number: '123',
          neighborhood: 'Centro',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01001-000',
          country: 'Brasil',
        },
        coordinates: {
          latitude: -23.55052,
          longitude: -46.633308,
        },
        showExactLocation: false,
        duration: 60,
        tags: ['encanamento', 'vazamento', 'conserto', 'instalação'],
        featured: false,
        status: 'published',
        advertiserId: 'user123',
        advertiserName: 'João Silva',
        views: 45,
        likes: 12,
        rating: 4.8,
        ratingCount: 15,
        images: ['https://source.unsplash.com/random/800x600?plumbing'],
        faqs: [
          {
            question: 'Qual o prazo para atendimento?',
            answer: 'Atendemos em até 24 horas após o contato.',
          },
          {
            question: 'Trabalha aos finais de semana?',
            answer: 'Sim, trabalhamos aos sábados e domingos com taxa adicional.',
          },
        ],
        publishedAt: new Date(),
      },
      {
        title: 'Eletricista Residencial',
        description:
          'Instalação e manutenção de sistemas elétricos residenciais. Atendimento rápido e eficiente.',
        categoryId: 'servicos-domesticos',
        categoryName: 'Serviços Domésticos',
        subcategoryId: 'eletricista',
        subcategoryName: 'Eletricista',
        price: 180,
        priceType: 'hourly',
        currency: 'BRL',
        address: {
          street: 'Avenida Paulista',
          number: '1000',
          neighborhood: 'Bela Vista',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01310-100',
          country: 'Brasil',
        },
        coordinates: {
          latitude: -23.56137,
          longitude: -46.655281,
        },
        showExactLocation: false,
        duration: 120,
        tags: ['eletricista', 'instalação', 'manutenção', 'residencial'],
        featured: true,
        status: 'published',
        advertiserId: 'user456',
        advertiserName: 'Carlos Oliveira',
        views: 78,
        likes: 23,
        rating: 4.9,
        ratingCount: 27,
        images: ['https://source.unsplash.com/random/800x600?electrician'],
        faqs: [
          {
            question: 'Atende emergências?',
            answer: 'Sim, atendemos emergências 24 horas por dia.',
          },
        ],
        publishedAt: new Date(),
      },
      {
        title: 'Diarista Profissional',
        description:
          'Limpeza completa de residências e escritórios. Serviço de qualidade e confiança.',
        categoryId: 'servicos-domesticos',
        categoryName: 'Serviços Domésticos',
        subcategoryId: 'limpeza',
        subcategoryName: 'Limpeza',
        price: 200,
        priceType: 'daily',
        currency: 'BRL',
        address: {
          street: 'Rua Augusta',
          number: '500',
          neighborhood: 'Consolação',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01304-000',
          country: 'Brasil',
        },
        coordinates: {
          latitude: -23.55314,
          longitude: -46.646018,
        },
        showExactLocation: false,
        duration: 480,
        tags: ['diarista', 'limpeza', 'residencial', 'escritório'],
        featured: false,
        status: 'published',
        advertiserId: 'user789',
        advertiserName: 'Maria Santos',
        views: 120,
        likes: 45,
        rating: 4.7,
        ratingCount: 32,
        images: ['https://source.unsplash.com/random/800x600?cleaning'],
        faqs: [
          {
            question: 'Quais produtos de limpeza são utilizados?',
            answer: 'Utilizamos produtos de alta qualidade e biodegradáveis.',
          },
          {
            question: 'Preciso fornecer os materiais de limpeza?',
            answer: 'Não é necessário, trazemos todos os materiais e equipamentos.',
          },
        ],
        publishedAt: new Date(),
      },
      {
        title: 'Pintor Residencial',
        description:
          'Pintura interna e externa de residências e comércios. Acabamento perfeito e preço justo.',
        categoryId: 'servicos-domesticos',
        categoryName: 'Serviços Domésticos',
        subcategoryId: 'pintura',
        subcategoryName: 'Pintura',
        price: 50,
        priceType: 'hourly',
        currency: 'BRL',
        address: {
          street: 'Rua Oscar Freire',
          number: '300',
          neighborhood: 'Jardins',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01426-000',
          country: 'Brasil',
        },
        coordinates: {
          latitude: -23.562369,
          longitude: -46.668091,
        },
        showExactLocation: false,
        duration: 480,
        tags: ['pintor', 'pintura', 'residencial', 'comercial'],
        featured: false,
        status: 'published',
        advertiserId: 'user101',
        advertiserName: 'Pedro Almeida',
        views: 65,
        likes: 18,
        rating: 4.6,
        ratingCount: 22,
        images: ['https://source.unsplash.com/random/800x600?painting'],
        faqs: [
          {
            question: 'Qual o prazo para conclusão do serviço?',
            answer:
              'O prazo varia conforme o tamanho da área a ser pintada, mas geralmente entre 3 a 7 dias.',
          },
        ],
        publishedAt: new Date(),
      },
      {
        title: 'Jardineiro Profissional',
        description:
          'Manutenção de jardins, poda de árvores, plantio e paisagismo. Serviço completo para sua área verde.',
        categoryId: 'servicos-domesticos',
        categoryName: 'Serviços Domésticos',
        subcategoryId: 'jardinagem',
        subcategoryName: 'Jardinagem',
        price: 120,
        priceType: 'hourly',
        currency: 'BRL',
        address: {
          street: 'Alameda Santos',
          number: '800',
          neighborhood: 'Jardim Paulista',
          city: 'São Paulo',
          state: 'SP',
          zipCode: '01418-100',
          country: 'Brasil',
        },
        coordinates: {
          latitude: -23.568321,
          longitude: -46.649075,
        },
        showExactLocation: false,
        duration: 240,
        tags: ['jardineiro', 'jardinagem', 'paisagismo', 'poda'],
        featured: true,
        status: 'published',
        advertiserId: 'user202',
        advertiserName: 'Roberto Costa',
        views: 89,
        likes: 34,
        rating: 4.9,
        ratingCount: 28,
        images: ['https://source.unsplash.com/random/800x600?gardening'],
        faqs: [
          {
            question: 'Fornece as plantas e insumos?',
            answer:
              'Sim, podemos fornecer todas as plantas e insumos necessários com custo adicional.',
          },
        ],
        publishedAt: new Date(),
      },
    ];

    // Adicionar serviços ao Firestore
    const results = [];
    for (const service of testServices) {
      const docRef = await addDoc(servicesCollection, {
        ...service,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      results.push({
        id: docRef.id,
        title: service.title,
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Serviços de teste criados com sucesso',
      services: results,
    });
  } catch (error) {
    console.error('Erro ao criar serviços de teste:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Erro ao criar serviços de teste',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
