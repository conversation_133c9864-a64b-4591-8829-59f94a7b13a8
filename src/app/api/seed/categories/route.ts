import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase/client';
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { ServiceCategory } from '@/types/models/service';

// Função para gerar categorias de teste
export async function GET() {
  try {
    const categoriesCollection = collection(db, 'serviceCategories');
    
    // Lista de categorias de teste
    const testCategories = [
      {
        name: 'Serviços Domésticos',
        description: 'Serviços para manutenção e cuidados com a casa',
        icon: 'home',
        parentId: null,
        slug: 'servicos-domesticos',
        active: true
      },
      {
        name: 'Assistência Técnica',
        description: 'Serviços de reparo e manutenção de equipamentos',
        icon: 'tool',
        parentId: null,
        slug: 'assistencia-tecnica',
        active: true
      },
      {
        name: 'Aulas e Cursos',
        description: 'Serviços educacionais e de aprendizado',
        icon: 'book',
        parentId: null,
        slug: 'aulas-cursos',
        active: true
      },
      {
        name: 'Saúde e Bem-estar',
        description: 'Serviços relacionados à saúde e qualidade de vida',
        icon: 'heart',
        parentId: null,
        slug: 'saude-bem-estar',
        active: true
      },
      {
        name: 'Eventos',
        description: 'Serviços para organização e realização de eventos',
        icon: 'calendar',
        parentId: null,
        slug: 'eventos',
        active: true
      }
    ];
    
    // Subcategorias
    const testSubcategories = [
      // Serviços Domésticos
      {
        name: 'Limpeza',
        description: 'Serviços de limpeza residencial e comercial',
        icon: 'trash',
        parentId: 'servicos-domesticos',
        slug: 'limpeza',
        active: true
      },
      {
        name: 'Encanamento',
        description: 'Serviços de encanamento e hidráulica',
        icon: 'droplet',
        parentId: 'servicos-domesticos',
        slug: 'encanamento',
        active: true
      },
      {
        name: 'Eletricista',
        description: 'Serviços de instalação e manutenção elétrica',
        icon: 'zap',
        parentId: 'servicos-domesticos',
        slug: 'eletricista',
        active: true
      },
      {
        name: 'Pintura',
        description: 'Serviços de pintura residencial e comercial',
        icon: 'paint-bucket',
        parentId: 'servicos-domesticos',
        slug: 'pintura',
        active: true
      },
      {
        name: 'Jardinagem',
        description: 'Serviços de jardinagem e paisagismo',
        icon: 'flower',
        parentId: 'servicos-domesticos',
        slug: 'jardinagem',
        active: true
      },
      
      // Assistência Técnica
      {
        name: 'Informática',
        description: 'Serviços de reparo e manutenção de computadores',
        icon: 'cpu',
        parentId: 'assistencia-tecnica',
        slug: 'informatica',
        active: true
      },
      {
        name: 'Eletrodomésticos',
        description: 'Serviços de reparo e manutenção de eletrodomésticos',
        icon: 'tv',
        parentId: 'assistencia-tecnica',
        slug: 'eletrodomesticos',
        active: true
      },
      {
        name: 'Celulares',
        description: 'Serviços de reparo e manutenção de celulares',
        icon: 'smartphone',
        parentId: 'assistencia-tecnica',
        slug: 'celulares',
        active: true
      },
      
      // Aulas e Cursos
      {
        name: 'Idiomas',
        description: 'Aulas de idiomas diversos',
        icon: 'globe',
        parentId: 'aulas-cursos',
        slug: 'idiomas',
        active: true
      },
      {
        name: 'Música',
        description: 'Aulas de instrumentos musicais e canto',
        icon: 'music',
        parentId: 'aulas-cursos',
        slug: 'musica',
        active: true
      },
      {
        name: 'Reforço Escolar',
        description: 'Aulas de reforço para estudantes',
        icon: 'book-open',
        parentId: 'aulas-cursos',
        slug: 'reforco-escolar',
        active: true
      },
      
      // Saúde e Bem-estar
      {
        name: 'Fisioterapia',
        description: 'Serviços de fisioterapia e reabilitação',
        icon: 'activity',
        parentId: 'saude-bem-estar',
        slug: 'fisioterapia',
        active: true
      },
      {
        name: 'Nutrição',
        description: 'Consultas e acompanhamento nutricional',
        icon: 'apple',
        parentId: 'saude-bem-estar',
        slug: 'nutricao',
        active: true
      },
      {
        name: 'Personal Trainer',
        description: 'Treinamento físico personalizado',
        icon: 'award',
        parentId: 'saude-bem-estar',
        slug: 'personal-trainer',
        active: true
      },
      
      // Eventos
      {
        name: 'Fotografia',
        description: 'Serviços de fotografia para eventos',
        icon: 'camera',
        parentId: 'eventos',
        slug: 'fotografia',
        active: true
      },
      {
        name: 'Buffet',
        description: 'Serviços de alimentação para eventos',
        icon: 'coffee',
        parentId: 'eventos',
        slug: 'buffet',
        active: true
      },
      {
        name: 'Decoração',
        description: 'Serviços de decoração para eventos',
        icon: 'gift',
        parentId: 'eventos',
        slug: 'decoracao',
        active: true
      }
    ];
    
    // Adicionar categorias principais ao Firestore
    const categoryResults = [];
    for (const category of testCategories) {
      const docRef = await addDoc(categoriesCollection, {
        ...category,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      categoryResults.push({
        id: docRef.id,
        name: category.name
      });
    }
    
    // Adicionar subcategorias ao Firestore
    const subcategoryResults = [];
    for (const subcategory of testSubcategories) {
      const docRef = await addDoc(categoriesCollection, {
        ...subcategory,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      
      subcategoryResults.push({
        id: docRef.id,
        name: subcategory.name,
        parentId: subcategory.parentId
      });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Categorias de teste criadas com sucesso',
      categories: categoryResults,
      subcategories: subcategoryResults
    });
  } catch (error) {
    console.error('Erro ao criar categorias de teste:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Erro ao criar categorias de teste',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
