import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getAdminStorage } from '@/lib/firebase/admin';
// Removido import do Firebase Storage client
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ success: false, message: 'Não autorizado' }, { status: 401 });
    }

    // Obter o arquivo do formulário
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, message: 'Nenhum arquivo enviado' },
        { status: 400 },
      );
    }

    // Verificar tipo de arquivo (apenas imagens)
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { success: false, message: 'Apenas imagens são permitidas' },
        { status: 400 },
      );
    }

    // Limitar tamanho do arquivo (5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, message: 'O arquivo deve ter no máximo 5MB' },
        { status: 400 },
      );
    }

    // Gerar nome único para o arquivo
    const fileExtension = file.name.split('.').pop();
    const fileName = `${uuidv4()}.${fileExtension}`;

    // Caminho no Firebase Storage
    const userId = session.user.id;
    const path = `services/${userId}/${fileName}`;

    // Converter o arquivo para um buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Obter a instância do Firebase Storage
    const storage = getAdminStorage();

    // Fazer upload para o Firebase Storage
    const storageRef = storage.bucket().file(path);

    // Upload do buffer para o Storage
    await storageRef.save(buffer, {
      metadata: {
        contentType: file.type,
      },
    });

    // Obter a URL do arquivo
    const downloadURL = await storageRef
      .getSignedUrl({
        action: 'read',
        expires: '01-01-2100', // URL de longa duração
      })
      .then((urls) => urls[0]);

    return NextResponse.json({
      success: true,
      url: downloadURL,
      path: path,
      fileName: fileName,
    });
  } catch (error) {
    console.error('Erro ao fazer upload de arquivo:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Erro ao fazer upload de arquivo',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    );
  }
}
