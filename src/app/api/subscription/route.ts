// src/app/api/subscription/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getUserActiveSubscription } from '@/services/subscriptions';
import { getPlanById } from '@/services/plans';

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get user's active subscription
    const subscription = await getUserActiveSubscription(session.user.id);
    
    if (!subscription) {
      return NextResponse.json({ subscription: null, plan: null });
    }
    
    // Get subscription plan
    const plan = await getPlanById(subscription.planId);
    
    if (!plan) {
      return NextResponse.json(
        { error: 'Plan not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ subscription, plan });
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription' },
      { status: 500 }
    );
  }
}
