// src/app/api/subscription/check/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { LogService } from '@/lib/services/log-service';

export async function GET(req: NextRequest) {
  try {
    // Verificar se o usuário está autenticado
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const userId = session.user.id;
    const db = getAdminFirestore();

    // Buscar o documento do usuário
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
    }

    const userData = userDoc.data();
    await LogService.logToFile('check_subscription_user', userData, 'debug');

    // Verificar se o usuário tem uma assinatura
    const subscription = userData?.subscription;
    const hasSubscription = !!subscription && ['active', 'trialing'].includes(subscription.status);

    return NextResponse.json({
      hasSubscription,
      subscription: hasSubscription ? subscription : null,
    });
  } catch (error) {
    console.error('Erro ao verificar assinatura:', error);
    await LogService.logError('check_subscription_error', error);
    return NextResponse.json({ error: 'Erro ao verificar assinatura' }, { status: 500 });
  }
}
