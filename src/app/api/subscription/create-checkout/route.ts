// src/app/api/subscription/create-checkout/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { getStripe } from '@/lib/stripe';
import { absoluteUrl } from '@/lib/utils';
import { LogService } from '@/lib/services/log-service';

import { getPlanById } from '@/lib/subscription';

export async function POST(req: NextRequest) {
  try {
    // Obter o corpo da requisição
    const body = await req.json();
    await LogService.logToFile('checkout_request', body, 'debug');

    const session = await getServerSession(authOptions);
    await LogService.logToFile('checkout_session', session, 'debug');

    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const { planId } = body;

    if (!planId) {
      return NextResponse.json({ error: 'ID do plano é obrigatório' }, { status: 400 });
    }

    // Buscar o plano no Firestore
    const plan = await getPlanById(planId);
    await LogService.logToFile('checkout_plan', plan, 'debug');

    if (!plan) {
      await LogService.logError('checkout_error', new Error('Plano não encontrado'), { planId });
      return NextResponse.json({ error: 'Plano não encontrado' }, { status: 404 });
    }

    if (!plan.stripePriceId) {
      await LogService.logError('checkout_error', new Error('Plano sem ID de preço do Stripe'), {
        planId,
        plan,
      });
      return NextResponse.json({ error: 'Plano sem ID de preço do Stripe' }, { status: 400 });
    }

    if (!plan.active) {
      return NextResponse.json({ error: 'Plano inativo' }, { status: 400 });
    }

    // Obter o ID do preço do Stripe
    const stripePriceId = plan.stripePriceId;

    // Verificar se o usuário já tem uma assinatura ativa
    const db = getAdminFirestore();
    const userRef = db.collection('users').doc(session.user.id);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      await LogService.logError('checkout_error', new Error('Usuário não encontrado'), {
        userId: session.user.id,
      });
      return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
    }

    const userData = userDoc.data();
    await LogService.logToFile('checkout_user_data', userData, 'debug');

    // Criar uma instância do Stripe
    const stripe = getStripe();

    // Criar ou recuperar o cliente no Stripe
    let stripeCustomerId = userData?.stripeCustomerId;

    if (!stripeCustomerId) {
      // Criar um novo cliente no Stripe
      const customer = await stripe.customers.create({
        email: session.user.email || undefined,
        name: session.user.name || undefined,
        metadata: {
          userId: session.user.id,
        },
      });

      stripeCustomerId = customer.id;

      // Atualizar o documento do usuário com o ID do cliente no Stripe
      await userRef.update({
        stripeCustomerId,
      });
    }

    // Configurar os parâmetros da sessão de checkout
    const checkoutParams = {
      customer: stripeCustomerId,
      line_items: [
        {
          // Usar o preço diretamente em vez do ID do preço do Stripe
          // price: stripePriceId,
          price_data: {
            currency: 'brl',
            product_data: {
              name: plan.name,
              description: plan.description,
            },
            unit_amount: plan.price,
            recurring: {
              interval: plan.interval,
            },
          },
          quantity: 1,
        },
      ],
      mode: 'subscription',
      allow_promotion_codes: true,
      subscription_data: {
        metadata: {
          userId: session.user.id,
          planId,
          planName: plan.name,
          planInterval: plan.interval,
        },
      },
      success_url: absoluteUrl('/subscription?success=Assinatura+realizada+com+sucesso'),
      cancel_url: absoluteUrl('/subscription?error=Assinatura+cancelada'),
    };

    await LogService.logToFile('checkout_params', checkoutParams, 'debug');

    // Criar a sessão de checkout
    const checkoutSession = await stripe.checkout.sessions.create(checkoutParams);

    await LogService.logToFile('checkout_session_response', checkoutSession, 'debug');

    return NextResponse.json({ url: checkoutSession.url });
  } catch (error) {
    console.error('Erro ao criar sessão de checkout:', error);
    await LogService.logError('checkout_error', error, {
      req: { method: req.method, url: req.url },
    });

    // Retornar mensagem de erro mais específica quando possível
    const errorMessage = error instanceof Error ? error.message : 'Erro ao processar assinatura';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
