// src/app/api/subscription/webhook/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { Stripe } from 'stripe';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { getStripe } from '@/lib/stripe';

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = headers().get('Stripe-Signature') as string;

  let event: Stripe.Event;
  const stripe = getStripe();

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET || ''
    );
  } catch (error: any) {
    console.error(`Webhook Error: ${error.message}`);
    return NextResponse.json(
      { error: `Webhook Error: ${error.message}` },
      { status: 400 }
    );
  }

  // Manipular eventos do Stripe
  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        
        if (session.mode === 'subscription') {
          await handleSubscriptionCreated(session);
        }
        
        break;
      }
      
      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionUpdated(subscription);
        break;
      }
      
      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionDeleted(subscription);
        break;
      }
      
      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice;
        
        if (invoice.subscription) {
          await handleInvoicePaid(invoice);
        }
        
        break;
      }
      
      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice;
        
        if (invoice.subscription) {
          await handleInvoiceFailed(invoice);
        }
        
        break;
      }
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Erro ao processar webhook:', error);
    return NextResponse.json(
      { error: 'Erro ao processar webhook' },
      { status: 500 }
    );
  }
}

// Manipuladores de eventos

async function handleSubscriptionCreated(session: Stripe.Checkout.Session) {
  // Obter o ID do usuário e do plano dos metadados
  const userId = session.subscription_data?.metadata?.userId;
  const planId = session.subscription_data?.metadata?.planId;
  
  if (!userId || !session.subscription) {
    console.error('Dados de assinatura incompletos');
    return;
  }
  
  // Obter detalhes da assinatura
  const stripe = getStripe();
  const subscription = await stripe.subscriptions.retrieve(
    session.subscription as string
  );
  
  // Criar ou atualizar o documento de assinatura no Firestore
  const db = getAdminFirestore();
  const subscriptionRef = db.collection('subscriptions').doc(subscription.id);
  
  await subscriptionRef.set({
    userId,
    planId,
    status: subscription.status,
    priceId: subscription.items.data[0].price.id,
    quantity: subscription.items.data[0].quantity,
    cancelAtPeriodEnd: subscription.cancel_at_period_end,
    cancelAt: subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : null,
    canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
    currentPeriodStart: new Date(subscription.current_period_start * 1000),
    currentPeriodEnd: new Date(subscription.current_period_end * 1000),
    createdAt: new Date(subscription.created * 1000),
    endedAt: subscription.ended_at ? new Date(subscription.ended_at * 1000) : null,
  });
  
  // Atualizar o documento do usuário
  const userRef = db.collection('users').doc(userId);
  
  await userRef.update({
    isAdvertiser: true,
    role: 'advertiser',
    subscriptionId: subscription.id,
    subscriptionStatus: subscription.status,
    subscriptionPriceId: subscription.items.data[0].price.id,
    subscriptionCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
  });
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  // Obter o ID do usuário dos metadados
  const userId = subscription.metadata.userId;
  const db = getAdminFirestore();
  
  if (!userId) {
    // Buscar o usuário pelo ID do cliente
    const usersSnapshot = await db
      .collection('users')
      .where('stripeCustomerId', '==', subscription.customer)
      .limit(1)
      .get();
    
    if (usersSnapshot.empty) {
      console.error('Usuário não encontrado para a assinatura:', subscription.id);
      return;
    }
    
    const userDoc = usersSnapshot.docs[0];
    const userData = userDoc.data();
    
    // Atualizar o documento de assinatura no Firestore
    const subscriptionRef = db.collection('subscriptions').doc(subscription.id);
    
    await subscriptionRef.set(
      {
        userId: userDoc.id,
        status: subscription.status,
        priceId: subscription.items.data[0].price.id,
        quantity: subscription.items.data[0].quantity,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        cancelAt: subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : null,
        canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        createdAt: new Date(subscription.created * 1000),
        endedAt: subscription.ended_at ? new Date(subscription.ended_at * 1000) : null,
        updatedAt: new Date(),
      },
      { merge: true }
    );
    
    // Atualizar o documento do usuário
    const userRef = db.collection('users').doc(userDoc.id);
    
    await userRef.update({
      isAdvertiser: subscription.status === 'active',
      subscriptionId: subscription.id,
      subscriptionStatus: subscription.status,
      subscriptionPriceId: subscription.items.data[0].price.id,
      subscriptionCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
    });
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  // Obter o ID do usuário dos metadados
  const userId = subscription.metadata.userId;
  const db = getAdminFirestore();
  
  if (!userId) {
    // Buscar o usuário pelo ID do cliente
    const usersSnapshot = await db
      .collection('users')
      .where('stripeCustomerId', '==', subscription.customer)
      .limit(1)
      .get();
    
    if (usersSnapshot.empty) {
      console.error('Usuário não encontrado para a assinatura:', subscription.id);
      return;
    }
    
    const userDoc = usersSnapshot.docs[0];
    
    // Atualizar o documento de assinatura no Firestore
    const subscriptionRef = db.collection('subscriptions').doc(subscription.id);
    
    await subscriptionRef.set(
      {
        status: subscription.status,
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        cancelAt: subscription.cancel_at ? new Date(subscription.cancel_at * 1000) : null,
        canceledAt: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        endedAt: subscription.ended_at ? new Date(subscription.ended_at * 1000) : new Date(),
        updatedAt: new Date(),
      },
      { merge: true }
    );
    
    // Atualizar o documento do usuário
    const userRef = db.collection('users').doc(userDoc.id);
    
    await userRef.update({
      isAdvertiser: false,
      subscriptionStatus: subscription.status,
    });
  }
}

async function handleInvoicePaid(invoice: Stripe.Invoice) {
  if (!invoice.subscription) return;
  
  // Obter a assinatura
  const stripe = getStripe();
  const subscription = await stripe.subscriptions.retrieve(
    invoice.subscription as string
  );
  
  // Atualizar o documento de assinatura no Firestore
  const db = getAdminFirestore();
  const subscriptionRef = db.collection('subscriptions').doc(subscription.id);
  
  await subscriptionRef.set(
    {
      status: subscription.status,
      currentPeriodStart: new Date(subscription.current_period_start * 1000),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000),
      updatedAt: new Date(),
    },
    { merge: true }
  );
  
  // Buscar o usuário associado à assinatura
  const subscriptionDoc = await subscriptionRef.get();
  
  if (!subscriptionDoc.exists) {
    console.error('Assinatura não encontrada:', subscription.id);
    return;
  }
  
  const subscriptionData = subscriptionDoc.data();
  
  if (!subscriptionData?.userId) {
    console.error('Usuário não encontrado para a assinatura:', subscription.id);
    return;
  }
  
  // Atualizar o documento do usuário
  const userRef = db.collection('users').doc(subscriptionData.userId);
  
  await userRef.update({
    isAdvertiser: subscription.status === 'active',
    subscriptionStatus: subscription.status,
    subscriptionCurrentPeriodEnd: new Date(subscription.current_period_end * 1000),
  });
}

async function handleInvoiceFailed(invoice: Stripe.Invoice) {
  if (!invoice.subscription) return;
  
  // Obter a assinatura
  const stripe = getStripe();
  const subscription = await stripe.subscriptions.retrieve(
    invoice.subscription as string
  );
  
  // Atualizar o documento de assinatura no Firestore
  const db = getAdminFirestore();
  const subscriptionRef = db.collection('subscriptions').doc(subscription.id);
  
  await subscriptionRef.set(
    {
      status: subscription.status,
      updatedAt: new Date(),
    },
    { merge: true }
  );
  
  // Buscar o usuário associado à assinatura
  const subscriptionDoc = await subscriptionRef.get();
  
  if (!subscriptionDoc.exists) {
    console.error('Assinatura não encontrada:', subscription.id);
    return;
  }
  
  const subscriptionData = subscriptionDoc.data();
  
  if (!subscriptionData?.userId) {
    console.error('Usuário não encontrado para a assinatura:', subscription.id);
    return;
  }
  
  // Atualizar o documento do usuário
  const userRef = db.collection('users').doc(subscriptionData.userId);
  
  await userRef.update({
    subscriptionStatus: subscription.status,
  });
}
