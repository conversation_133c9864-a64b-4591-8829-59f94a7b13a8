// src/app/api/subscription/create-portal/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { getStripe } from '@/lib/stripe';
import { absoluteUrl } from '@/lib/utils';
import { LogService } from '@/lib/services/log-service';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    await LogService.logToFile('portal_session', session, 'debug');

    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Buscar o usuário no Firestore
    const db = getAdminFirestore();
    const userRef = db.collection('users').doc(session.user.id);
    const userDoc = await userRef.get();

    if (!userDoc.exists) {
      await LogService.logError('portal_error', new Error('Usuário não encontrado'), {
        userId: session.user.id,
      });
      return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
    }

    const userData = userDoc.data();
    await LogService.logToFile('portal_user_data', userData, 'debug');
    const stripeCustomerId = userData?.stripeCustomerId;

    if (!stripeCustomerId) {
      await LogService.logError('portal_error', new Error('Cliente Stripe não encontrado'), {
        userId: session.user.id,
      });
      return NextResponse.json({ error: 'Cliente Stripe não encontrado' }, { status: 404 });
    }

    // Criar uma sessão do portal de clientes do Stripe
    const stripe = getStripe();

    // Configurar os parâmetros da sessão do portal
    const portalParams = {
      customer: stripeCustomerId,
      return_url: absoluteUrl('/dashboard/subscription'),
    };

    await LogService.logToFile('portal_params', portalParams, 'debug');

    // Criar a sessão do portal
    const portalSession = await stripe.billingPortal.sessions.create(portalParams);
    await LogService.logToFile('portal_session_response', portalSession, 'debug');

    return NextResponse.json({ url: portalSession.url });
  } catch (error) {
    console.error('Erro ao criar sessão do portal:', error);
    await LogService.logError('portal_error', error, { req: { method: req.method, url: req.url } });

    // Retornar mensagem de erro mais específica quando possível
    const errorMessage =
      error instanceof Error ? error.message : 'Erro ao acessar portal de gerenciamento';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
