// src/app/api/webhooks/stripe/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { Stripe } from 'stripe';
import { getStripe } from '@/lib/stripe';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { LogService } from '@/lib/services/log-service';

// Função para verificar a assinatura do webhook
const verifyStripeSignature = (payload: string, signature: string, endpointSecret: string) => {
  const stripe = getStripe();
  try {
    return stripe.webhooks.constructEvent(payload, signature, endpointSecret);
  } catch (err) {
    console.error('Erro na verificação da assinatura do webhook:', err);
    throw new Error(`Webhook Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
  }
};

// Função para processar eventos de assinatura
const handleSubscriptionEvent = async (event: Stripe.Event) => {
  const db = getAdminFirestore();

  // Obter o objeto de assinatura do evento
  const subscription = event.data.object as Stripe.Subscription;
  const customerId = subscription.customer as string;
  const subscriptionId = subscription.id;
  const subscriptionStatus = subscription.status;
  const planId = subscription.metadata.planId;

  // Buscar o usuário pelo customerId do Stripe
  const usersRef = db.collection('users');
  const userQuery = await usersRef.where('stripeCustomerId', '==', customerId).get();

  if (userQuery.empty) {
    console.error(`Usuário não encontrado para o customerId: ${customerId}`);
    return;
  }

  const userDoc = userQuery.docs[0];
  const userId = userDoc.id;

  // Atualizar a assinatura do usuário no Firestore
  // Verificar se há itens na assinatura
  let currentPeriodStart = null;
  let currentPeriodEnd = null;

  // Verificar se há itens na assinatura e extrair os períodos
  if (subscription.items && subscription.items.data && subscription.items.data.length > 0) {
    const item = subscription.items.data[0];
    currentPeriodStart = item.current_period_start;
    currentPeriodEnd = item.current_period_end;
    console.log(`Períodos encontrados no item: ${currentPeriodStart} - ${currentPeriodEnd}`);
  } else {
    // Tentar obter dos campos principais da assinatura
    currentPeriodStart = (subscription as any).current_period_start;
    currentPeriodEnd = (subscription as any).current_period_end;
    console.log(`Períodos encontrados na assinatura: ${currentPeriodStart} - ${currentPeriodEnd}`);
  }

  // Obter informações do plano
  const plan = (subscription as any).plan;
  const planName = subscription.metadata?.planName || (plan ? plan.nickname : null);
  const planInterval = subscription.metadata?.planInterval || (plan ? plan.interval : null);
  const amount = plan ? plan.amount : null;

  const subscriptionData = {
    id: subscriptionId,
    status: subscriptionStatus,
    planId: planId,
    planName: planName,
    planInterval: planInterval,
    amount: amount,
    customerId: customerId,
    currentPeriodStart: currentPeriodStart ? new Date(currentPeriodStart * 1000) : null,
    currentPeriodEnd: currentPeriodEnd ? new Date(currentPeriodEnd * 1000) : null,
    cancelAtPeriodEnd: (subscription as any).cancel_at_period_end || false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Atualizar o documento do usuário
  await db.collection('users').doc(userId).update({
    subscription: subscriptionData,
    updatedAt: new Date(),
  });

  // Registrar o evento de assinatura
  await db.collection('subscriptionEvents').add({
    userId,
    subscriptionId,
    eventType: event.type,
    eventId: event.id,
    // Converter para objeto simples para evitar problemas com o Firestore
    data: {
      id: subscription.id,
      status: subscription.status,
      customer: customerId,
      metadata: subscription.metadata || {},
      created: subscription.created,
      // Adicionar outros campos relevantes conforme necessário
    },
    createdAt: new Date(),
  });

  console.log(`Assinatura ${event.type} processada para o usuário ${userId}`);
};

// Função para processar eventos de pagamento
const handlePaymentEvent = async (event: Stripe.Event) => {
  const db = getAdminFirestore();

  // Obter o objeto de pagamento do evento
  const paymentIntent = event.data.object as Stripe.PaymentIntent;
  const customerId = paymentIntent.customer as string;

  // Buscar o usuário pelo customerId do Stripe
  const usersRef = db.collection('users');
  const userQuery = await usersRef.where('stripeCustomerId', '==', customerId).get();

  if (userQuery.empty) {
    console.error(`Usuário não encontrado para o customerId: ${customerId}`);
    return;
  }

  const userDoc = userQuery.docs[0];
  const userId = userDoc.id;

  // Registrar o evento de pagamento
  await db.collection('paymentEvents').add({
    userId,
    paymentIntentId: paymentIntent.id,
    eventType: event.type,
    eventId: event.id,
    amount: paymentIntent.amount,
    currency: paymentIntent.currency,
    status: paymentIntent.status,
    // Converter para objeto simples para evitar problemas com o Firestore
    data: {
      id: paymentIntent.id,
      status: paymentIntent.status,
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      customer: customerId,
      // Adicionar outros campos relevantes conforme necessário
    },
    createdAt: new Date(),
  });

  console.log(`Pagamento ${event.type} processado para o usuário ${userId}`);
};

export async function POST(req: NextRequest) {
  console.log('WEBHOOK DO STRIPE RECEBIDO!');
  try {
    const body = await req.text();
    await LogService.logToFile('webhook_body', body, 'debug');

    // Obter o cabeçalho de assinatura do Stripe
    const signature = req.headers.get('stripe-signature') || '';
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET || '';

    await LogService.logToFile(
      'webhook_headers',
      {
        signature,
        endpointSecret: endpointSecret ? '[REDACTED]' : 'Not configured',
      },
      'debug',
    );

    if (!endpointSecret) {
      console.error('STRIPE_WEBHOOK_SECRET não configurado');
      return NextResponse.json({ error: 'Webhook secret não configurado' }, { status: 500 });
    }

    // Verificar a assinatura do webhook
    let event: Stripe.Event;
    try {
      event = verifyStripeSignature(body, signature, endpointSecret);
      await LogService.logToFile(
        'webhook_event',
        {
          id: event.id,
          type: event.type,
          created: event.created,
          data: event.data.object,
        },
        'debug',
      );
    } catch (err) {
      console.error('Erro na verificação da assinatura:', err);
      await LogService.logError('webhook_signature_error', err);
      return NextResponse.json({ error: 'Assinatura inválida' }, { status: 400 });
    }

    // Processar diferentes tipos de eventos
    switch (event.type) {
      // Eventos de assinatura
      case 'customer.subscription.created':
        console.log('EVENTO DE ASSINATURA CRIADA RECEBIDO!');
        await LogService.logToFile(
          'webhook_subscription_created',
          {
            type: event.type,
            id: event.id,
            data: event.data.object,
          },
          'debug',
        );
        await handleSubscriptionEvent(event);
        break;
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
      case 'customer.subscription.trial_will_end':
        await LogService.logToFile(
          'webhook_subscription_event',
          {
            type: event.type,
            id: event.id,
            data: event.data.object,
          },
          'debug',
        );
        await handleSubscriptionEvent(event);
        break;

      // Eventos de pagamento
      case 'payment_intent.succeeded':
      case 'payment_intent.payment_failed':
      case 'payment_intent.canceled':
        await LogService.logToFile(
          'webhook_payment_event',
          {
            type: event.type,
            id: event.id,
            data: event.data.object,
          },
          'debug',
        );
        await handlePaymentEvent(event);
        break;

      // Eventos de fatura
      case 'invoice.payment_succeeded':
        console.log('EVENTO DE FATURA PAGA RECEBIDO!');
        await LogService.logToFile(
          'webhook_invoice_succeeded',
          {
            type: event.type,
            id: event.id,
            data: event.data.object,
          },
          'debug',
        );
        // Atualizar o status da assinatura para ativa
        const invoice = event.data.object as any;
        if (invoice.subscription) {
          console.log(`Fatura paga para assinatura: ${invoice.subscription}`);
          // Aqui podemos atualizar o status da assinatura no Firestore
        }
        break;

      case 'invoice.payment_failed':
        console.log('EVENTO DE FATURA NÃO PAGA RECEBIDO!');
        await LogService.logToFile(
          'webhook_invoice_failed',
          {
            type: event.type,
            id: event.id,
            data: event.data.object,
          },
          'debug',
        );
        // Atualizar o status da assinatura para problema de pagamento
        const failedInvoice = event.data.object as any;
        if (failedInvoice.subscription) {
          console.log(
            `Falha no pagamento da fatura para assinatura: ${failedInvoice.subscription}`,
          );
          // Aqui podemos atualizar o status da assinatura no Firestore
        }
        break;

      // Eventos de checkout
      case 'checkout.session.completed':
        console.log('EVENTO DE CHECKOUT COMPLETADO RECEBIDO!');
        await LogService.logToFile(
          'webhook_checkout_completed',
          {
            type: event.type,
            id: event.id,
            data: event.data.object,
          },
          'debug',
        );

        // Obter os dados da sessão de checkout
        const checkoutSession = event.data.object as Stripe.Checkout.Session;
        console.log(`Checkout session completada: ${checkoutSession.id}`);
        console.log(`Cliente: ${checkoutSession.customer}`);
        console.log(`Status: ${checkoutSession.status}`);
        console.log(`Modo: ${checkoutSession.mode}`);

        // Se for uma assinatura, registrar informações adicionais
        if (checkoutSession.mode === 'subscription' && checkoutSession.subscription) {
          console.log(`Assinatura criada: ${checkoutSession.subscription}`);

          // Registrar a assinatura no log
          await LogService.logToFile(
            'webhook_checkout_subscription',
            {
              checkoutId: checkoutSession.id,
              subscriptionId: checkoutSession.subscription,
              customerId: checkoutSession.customer,
            },
            'debug',
          );

          // Buscar o usuário pelo customerId do Stripe
          if (checkoutSession.customer) {
            const db = getAdminFirestore();
            const customerId = checkoutSession.customer as string;
            const usersRef = db.collection('users');
            const userQuery = await usersRef.where('stripeCustomerId', '==', customerId).get();

            if (!userQuery.empty) {
              const userDoc = userQuery.docs[0];
              const userId = userDoc.id;

              // Atualizar o status da assinatura no Firestore
              await db.collection('users').doc(userId).update({
                'subscription.status': 'active',
                'subscription.id': checkoutSession.subscription,
                updatedAt: new Date(),
              });

              console.log(`Assinatura atualizada para o usuário ${userId}`);
            } else {
              console.error(`Usuário não encontrado para o customerId: ${customerId}`);
            }
          }
        }
        break;

      // Outros eventos
      default:
        await LogService.logToFile(
          'webhook_unhandled_event',
          {
            type: event.type,
            id: event.id,
          },
          'debug',
        );
        console.log(`Evento não processado: ${event.type}`);
    }

    await LogService.logToFile(
      'webhook_success',
      { event_id: event.id, event_type: event.type },
      'debug',
    );
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Erro no webhook do Stripe:', error);
    await LogService.logError('webhook_error', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
