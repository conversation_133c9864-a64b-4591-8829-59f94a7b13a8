import { NextRequest, NextResponse } from 'next/server';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { Timestamp } from 'firebase/firestore';
import { ZipCode } from '@/types/models/location';

/**
 * Endpoint para obter dados de CEP
 * GET /api/zipcode?code=01310100
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const zipCode = searchParams.get('code');

    if (!zipCode) {
      return NextResponse.json({ error: 'CEP não informado' }, { status: 400 });
    }

    // Remover caracteres não numéricos
    const cleanZipCode = zipCode.replace(/\D/g, '');

    // Validar formato do CEP (8 dígitos)
    if (cleanZipCode.length !== 8) {
      return NextResponse.json({ error: 'Formato de CEP inválido' }, { status: 400 });
    }

    return await getZipCodeData(cleanZipCode);
  } catch (error) {
    console.error('Erro ao processar requisição de CEP:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

/**
 * Obter dados de CEP
 */
async function getZipCodeData(zipCode: string) {
  try {
    // Obter instância do Firestore Admin
    const db = getAdminFirestore();

    // Verificar se já temos esse CEP no cache
    const zipCodeRef = db.collection('zipcodes').doc(zipCode);
    const zipCodeDoc = await zipCodeRef.get();

    if (zipCodeDoc.exists) {
      const zipCodeData = zipCodeDoc.data() as ZipCode;

      // Verificar se o cache expirou
      const expiresAt =
        zipCodeData.expiresAt instanceof Timestamp
          ? zipCodeData.expiresAt.toDate()
          : new Date(zipCodeData.expiresAt);

      if (expiresAt > new Date()) {
        // Cache válido, retornar dados do cache
        return NextResponse.json(zipCodeData);
      }
    }

    // Cache não existe ou expirou, buscar na API ViaCEP
    const response = await fetch(`https://viacep.com.br/ws/${zipCode}/json/`);

    if (!response.ok) {
      throw new Error(`Erro ao buscar CEP: ${response.statusText}`);
    }

    const data = await response.json();

    // Verificar se a API retornou erro
    if (data.erro) {
      return NextResponse.json({ error: 'CEP não encontrado' }, { status: 404 });
    }

    // Formatar CEP com hífen
    const formattedZipCode = zipCode.replace(/^(\d{5})(\d{3})$/, '$1-$2');

    // Preparar dados para salvar no cache
    const now = new Date();
    const expiresAt = new Date();
    expiresAt.setFullYear(now.getFullYear() + 1); // Cache válido por 1 ano

    const zipCodeData: ZipCode = {
      id: zipCode,
      zipCode: formattedZipCode,
      street: data.logradouro,
      neighborhood: data.bairro,
      city: data.localidade,
      state: data.estado,
      stateCode: data.uf,
      complement: data.complemento,
      ibgeCode: data.ibge,
      createdAt: now,
      updatedAt: now,
      expiresAt: expiresAt,
      source: 'viacep',
      raw: data,
    };

    // Salvar no cache
    await zipCodeRef.set(zipCodeData);

    return NextResponse.json(zipCodeData);
  } catch (error) {
    console.error('Erro ao obter dados do CEP:', error);
    return NextResponse.json({ error: 'Erro ao obter dados do CEP' }, { status: 500 });
  }
}
