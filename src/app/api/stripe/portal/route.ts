// src/app/api/stripe/portal/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getStripe } from '@/lib/stripe';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { LogService } from '@/lib/services/log-service';

export async function POST(req: NextRequest) {
  try {
    // Verificar se o usuário está autenticado
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Log dos dados recebidos
    await LogService.logToFile('portal_create_input', { userId: session.user.id }, 'debug');

    try {
      // 1. Buscar o usuário no Firestore
      const db = getAdminFirestore();
      const userDoc = await db.collection('users').doc(session.user.id).get();

      if (!userDoc.exists) {
        return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
      }

      const userData = userDoc.data();

      // 2. Verificar se o usuário tem um cliente no Stripe
      if (!userData.stripeCustomerId) {
        return NextResponse.json(
          { error: 'Usuário não tem um cliente no Stripe' },
          { status: 400 },
        );
      }

      // 3. Criar uma sessão de portal
      const stripe = getStripe();
      const returnUrl = `${req.nextUrl.origin}/dashboard`;

      const portalSession = await stripe.billingPortal.sessions.create({
        customer: userData.stripeCustomerId,
        return_url: returnUrl,
      });

      await LogService.logToFile('portal_create_session', portalSession, 'debug');

      // 4. Retornar a URL da sessão de portal
      return NextResponse.json({ url: portalSession.url });
    } catch (error) {
      await LogService.logError('portal_create_error', error);
      console.error('Erro ao criar sessão de portal:', error);
      return NextResponse.json({ error: 'Erro ao criar sessão de portal' }, { status: 500 });
    }
  } catch (error) {
    console.error('Erro na API de portal:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
