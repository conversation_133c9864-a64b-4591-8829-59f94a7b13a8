// src/app/api/stripe/checkout/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getStripe } from '@/lib/stripe';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { LogService } from '@/lib/services/log-service';

export async function POST(req: NextRequest) {
  try {
    // Verificar se o usuário está autenticado
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Obter os dados da requisição
    const { planId } = await req.json();

    if (!planId) {
      return NextResponse.json({ error: 'ID do plano é obrigatório' }, { status: 400 });
    }

    // Log dos dados recebidos
    await LogService.logToFile(
      'checkout_create_input',
      { planId, userId: session.user.id },
      'debug',
    );

    try {
      // 1. Buscar o plano no Firestore
      const db = getAdminFirestore();
      const planDoc = await db.collection('plans').doc(planId).get();

      if (!planDoc.exists) {
        return NextResponse.json({ error: 'Plano não encontrado' }, { status: 404 });
      }

      const plan = planDoc.data();
      await LogService.logToFile('checkout_create_plan', plan, 'debug');

      // 2. Verificar se o plano está ativo
      if (!plan.active) {
        return NextResponse.json({ error: 'Plano não está ativo' }, { status: 400 });
      }

      // 3. Verificar se o usuário já tem um cliente no Stripe
      const userDoc = await db.collection('users').doc(session.user.id).get();
      const userData = userDoc.exists ? userDoc.data() : null;

      let stripeCustomerId = userData?.stripeCustomerId;

      // 4. Se o usuário não tiver um cliente no Stripe, criar um
      const stripe = getStripe();

      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: session.user.email,
          name: session.user.name,
          metadata: {
            userId: session.user.id,
          },
        });

        stripeCustomerId = customer.id;

        // Atualizar o usuário no Firestore
        await db.collection('users').doc(session.user.id).set(
          {
            stripeCustomerId,
            updatedAt: new Date(),
          },
          { merge: true },
        );

        await LogService.logToFile('checkout_create_customer', customer, 'debug');
      }

      // 5. Criar uma sessão de checkout
      const successUrl = `${req.nextUrl.origin}/dashboard/subscription/success?session_id={CHECKOUT_SESSION_ID}`;
      const cancelUrl = `${req.nextUrl.origin}/dashboard/subscription/cancel`;

      const checkoutSession = await stripe.checkout.sessions.create({
        customer: stripeCustomerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: plan.stripePriceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: {
          userId: session.user.id,
          planId: planId,
        },
      });

      await LogService.logToFile('checkout_create_session', checkoutSession, 'debug');

      // 6. Retornar a URL da sessão de checkout
      return NextResponse.json({ url: checkoutSession.url });
    } catch (error) {
      await LogService.logError('checkout_create_error', error);
      console.error('Erro ao criar sessão de checkout:', error);
      return NextResponse.json({ error: 'Erro ao criar sessão de checkout' }, { status: 500 });
    }
  } catch (error) {
    console.error('Erro na API de checkout:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
