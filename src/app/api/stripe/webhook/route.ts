// src/app/api/stripe/webhook/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { Stripe } from 'stripe';
import { stripe } from '@/lib/stripe';
import {
  createSubscription,
  getSubscriptionByStripeId,
  updateSubscriptionFromStripe,
} from '@/services/subscriptions';
import { getPlanByStripePriceId } from '@/services/plans';
import { SubscriptionStatus } from '@/types/subscription';

// This is your Stripe webhook secret for testing your endpoint locally.
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET as string;

// Set the runtime to edge for better performance
export const runtime = 'edge';

// Disable body parsing, as we need the raw body for Stripe signature verification
export const dynamic = 'force-dynamic';

export async function POST(req: NextRequest) {
  try {
    // Get the raw request body for Stripe signature verification
    const body = await req.text();

    // Get the Stripe signature from the headers
    const headersList = await headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      console.error('Missing Stripe signature');
      return NextResponse.json({ error: 'Missing Stripe signature' }, { status: 400 });
    }

    // Verify the Stripe webhook signature
    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
      console.log(`✅ Webhook verified: ${event.type}`);
    } catch (err) {
      console.error(`⚠️ Webhook signature verification failed:`, err);
      return NextResponse.json({ error: 'Webhook signature verification failed' }, { status: 400 });
    }

    // Log the event type for debugging
    console.log(`Received Stripe webhook event: ${event.type}`);

    // Handle the event based on its type
    try {
      switch (event.type) {
        // Handle checkout session completion
        case 'checkout.session.completed': {
          await handleCheckoutSessionCompleted(event);
          break;
        }

        // Handle successful payment
        case 'invoice.payment_succeeded': {
          await handleInvoicePaymentSucceeded(event);
          break;
        }

        // Handle failed payment
        case 'invoice.payment_failed': {
          await handleInvoicePaymentFailed(event);
          break;
        }

        // Handle subscription updates
        case 'customer.subscription.updated': {
          await handleSubscriptionUpdated(event);
          break;
        }

        // Handle subscription deletions
        case 'customer.subscription.deleted': {
          await handleSubscriptionDeleted(event);
          break;
        }

        // Handle subscription creation
        case 'customer.subscription.created': {
          await handleSubscriptionCreated(event);
          break;
        }

        // Log unhandled event types
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      // Return a 200 response to acknowledge receipt of the event
      return NextResponse.json({ received: true, type: event.type });
    } catch (error) {
      console.error(`Error handling event ${event.type}:`, error);
      // Still return 200 to Stripe to prevent retries, but log the error
      return NextResponse.json(
        { received: true, error: `Error processing ${event.type}` },
        { status: 200 },
      );
    }
  } catch (error) {
    console.error('Critical error handling webhook:', error);
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 });
  }
}

// Handler for checkout.session.completed event
async function handleCheckoutSessionCompleted(event: Stripe.Event) {
  const session = event.data.object as Stripe.Checkout.Session;

  // Make sure this is a subscription checkout
  if (session.mode !== 'subscription') {
    console.log('Not a subscription checkout, skipping');
    return;
  }

  // Get the subscription ID from the session
  const subscriptionId = session.subscription as string;
  if (!subscriptionId) {
    console.error('No subscription ID in checkout session');
    return;
  }

  // Get metadata from the session
  const userId = session.metadata?.userId;
  const planId = session.metadata?.planId;

  if (!userId || !planId) {
    console.error('Missing metadata in checkout session', { userId, planId });
    return;
  }

  // Retrieve the subscription details from Stripe
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);

  // Convert Stripe subscription to our format
  const subscriptionData = {
    status: subscription.status as SubscriptionStatus,
    currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
    currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
    cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
  };

  // Check if subscription already exists in our database
  const existingSubscription = await getSubscriptionByStripeId(subscription.id);
  if (existingSubscription) {
    console.log(`Subscription ${subscription.id} already exists, updating instead`);
    await updateSubscriptionFromStripe(subscription.id, subscriptionData);
    return;
  }

  // Create a new subscription in our database
  console.log(`Creating new subscription for user ${userId} with plan ${planId}`);
  await createSubscription({
    userId,
    planId,
    ...subscriptionData,
    stripeSubscriptionId: subscription.id,
    stripeCustomerId: (subscription as any).customer as string,
  });

  console.log(`Subscription ${subscription.id} created successfully`);
}

// Handler for invoice.payment_succeeded event
async function handleInvoicePaymentSucceeded(event: Stripe.Event) {
  const invoice = event.data.object as Stripe.Invoice;

  // Make sure this is a subscription invoice
  if (!(invoice as any).subscription) {
    console.log('Not a subscription invoice, skipping');
    return;
  }

  // Get the subscription ID from the invoice
  const subscriptionId = (invoice as any).subscription as string;

  // Retrieve the subscription details from Stripe
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);

  // Convert Stripe subscription to our format
  const subscriptionData = {
    status: subscription.status as SubscriptionStatus,
    currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
    currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
    cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
  };

  // Get the subscription from our database
  const existingSubscription = await getSubscriptionByStripeId(subscriptionId);

  if (!existingSubscription) {
    console.error(`Subscription ${subscriptionId} not found in database`);
    return;
  }

  // Update subscription in our database
  console.log(`Updating subscription ${subscriptionId} after successful payment`);
  await updateSubscriptionFromStripe(subscriptionId, subscriptionData);

  console.log(`Subscription ${subscriptionId} updated successfully`);
}

// Handler for invoice.payment_failed event
async function handleInvoicePaymentFailed(event: Stripe.Event) {
  const invoice = event.data.object as Stripe.Invoice;

  // Make sure this is a subscription invoice
  if (!(invoice as any).subscription) {
    console.log('Not a subscription invoice, skipping');
    return;
  }

  // Get the subscription ID from the invoice
  const subscriptionId = (invoice as any).subscription as string;

  // Get the subscription from our database
  const existingSubscription = await getSubscriptionByStripeId(subscriptionId);

  if (!existingSubscription) {
    console.error(`Subscription ${subscriptionId} not found in database`);
    return;
  }

  // Update subscription status in our database
  console.log(`Updating subscription ${subscriptionId} after failed payment`);
  await updateSubscriptionFromStripe(subscriptionId, {
    status: 'past_due', // Mark as past due when payment fails
  });

  console.log(`Subscription ${subscriptionId} marked as past_due`);
}

// Handler for customer.subscription.updated event
async function handleSubscriptionUpdated(event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;

  // Get the subscription from our database
  const existingSubscription = await getSubscriptionByStripeId(subscription.id);

  if (!existingSubscription) {
    console.error(`Subscription ${subscription.id} not found in database`);
    return;
  }

  // Update subscription in our database
  console.log(`Updating subscription ${subscription.id} after update event`);
  await updateSubscriptionFromStripe(subscription.id, {
    status: subscription.status as SubscriptionStatus,
    currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
    currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
    cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
  });

  console.log(`Subscription ${subscription.id} updated successfully`);
}

// Handler for customer.subscription.deleted event
async function handleSubscriptionDeleted(event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;

  // Get the subscription from our database
  const existingSubscription = await getSubscriptionByStripeId(subscription.id);

  if (!existingSubscription) {
    console.error(`Subscription ${subscription.id} not found in database`);
    return;
  }

  // Update subscription in our database
  console.log(`Marking subscription ${subscription.id} as canceled`);
  await updateSubscriptionFromStripe(subscription.id, {
    status: 'canceled',
  });

  console.log(`Subscription ${subscription.id} marked as canceled`);
}

// Handler for customer.subscription.created event
async function handleSubscriptionCreated(event: Stripe.Event) {
  const subscription = event.data.object as Stripe.Subscription;

  // Check if subscription already exists in our database
  const existingSubscription = await getSubscriptionByStripeId(subscription.id);

  if (existingSubscription) {
    console.log(`Subscription ${subscription.id} already exists, skipping creation`);
    return;
  }

  // Get customer ID from subscription
  const customerId = (subscription as any).customer as string;

  // Get customer details to find user ID
  const customer = await stripe.customers.retrieve(customerId);

  if (!customer || customer.deleted) {
    console.error(`Customer ${customerId} not found or deleted`);
    return;
  }

  // Get user ID from customer metadata
  const userId = customer.metadata?.userId;

  if (!userId) {
    console.error(`No userId in customer metadata for ${customerId}`);
    return;
  }

  // Get price ID from subscription items
  const priceId = subscription.items.data[0]?.price.id;

  if (!priceId) {
    console.error(`No price ID found in subscription ${subscription.id}`);
    return;
  }

  // Get plan by Stripe price ID
  const plan = await getPlanByStripePriceId(priceId);

  if (!plan) {
    console.error(`No plan found for price ID ${priceId}`);
    return;
  }

  // Create subscription in our database
  console.log(`Creating subscription for user ${userId} with plan ${plan.id}`);
  await createSubscription({
    userId,
    planId: plan.id,
    status: subscription.status as SubscriptionStatus,
    currentPeriodStart: new Date((subscription as any).current_period_start * 1000),
    currentPeriodEnd: new Date((subscription as any).current_period_end * 1000),
    cancelAtPeriodEnd: (subscription as any).cancel_at_period_end,
    stripeSubscriptionId: subscription.id,
    stripeCustomerId: customerId,
  });

  console.log(`Subscription ${subscription.id} created successfully`);
}
