// src/app/api/stripe/check/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getStripe } from '@/lib/stripe';

export async function GET(req: NextRequest) {
  try {
    const stripe = getStripe();
    
    // Verificar se o Stripe está inicializado
    const initialized = !!stripe;
    
    // Tentar obter a configuração do Stripe
    let config = null;
    try {
      config = await stripe.balance.retrieve();
    } catch (error) {
      console.error('Erro ao obter configuração do Stripe:', error);
      return NextResponse.json({
        initialized,
        error: {
          message: error.message,
          type: error.type,
          code: error.code,
        },
      });
    }
    
    return NextResponse.json({
      initialized,
      config,
    });
  } catch (error) {
    console.error('Erro ao verificar Stripe:', error);
    return NextResponse.json({ error: 'Erro ao verificar Stripe' }, { status: 500 });
  }
}
