// src/app/api/admin/seed-plans/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { LogService } from '@/lib/services/log-service';

// Planos mockados para teste
const mockPlans = [
  {
    id: 'basic-monthly',
    name: 'Básico',
    description: 'Ideal para quem está começando',
    price: 4990,
    interval: 'month',
    popular: false,
    active: true,
    order: 1,
    stripeProductId: 'prod_mock1',
    stripePriceId: 'price_mock1',
    modules: {
      services: true,
      properties: false,
      vehicles: false,
      products: false,
    },
    limits: {
      services: 5,
      properties: 0,
      vehicles: 0,
      products: 0,
      featured: 1,
    },
    features: [
      { name: 'Até 5 serviços ativos', included: true },
      { name: '1 anúncio em destaque', included: true },
      { name: 'Estatísticas básicas', included: true },
      { name: 'Suporte por email', included: true },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', included: false },
      { name: 'Veículos', included: false },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin',
  },
  {
    id: 'pro-monthly',
    name: 'Profissional',
    description: 'Para profissionais e pequenas empresas',
    price: 9990,
    interval: 'month',
    popular: true,
    active: true,
    order: 2,
    stripeProductId: 'prod_mock2',
    stripePriceId: 'price_mock2',
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: false,
    },
    limits: {
      services: 20,
      properties: 10,
      vehicles: 10,
      products: 0,
      featured: 5,
    },
    features: [
      { name: 'Até 20 serviços ativos', included: true },
      { name: 'Até 10 imóveis ativos', included: true },
      { name: 'Até 10 veículos ativos', included: true },
      { name: '5 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte por email e chat', included: true },
      { name: 'Produtos', included: false },
      { name: 'Suporte prioritário', included: false },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin',
  },
  {
    id: 'business-monthly',
    name: 'Empresarial',
    description: 'Para empresas e grandes anunciantes',
    price: 19990,
    interval: 'month',
    popular: false,
    active: true,
    order: 3,
    stripeProductId: 'prod_mock3',
    stripePriceId: 'price_mock3',
    modules: {
      services: true,
      properties: true,
      vehicles: true,
      products: true,
    },
    limits: {
      services: 50,
      properties: 30,
      vehicles: 30,
      products: 100,
      featured: 15,
    },
    features: [
      { name: 'Até 50 serviços ativos', included: true },
      { name: 'Até 30 imóveis ativos', included: true },
      { name: 'Até 30 veículos ativos', included: true },
      { name: 'Até 100 produtos ativos', included: true },
      { name: '15 anúncios em destaque', included: true },
      { name: 'Estatísticas avançadas', included: true },
      { name: 'Suporte prioritário', included: true },
      { name: 'API de integração', included: true },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: 'admin',
  },
];

export async function GET(req: NextRequest) {
  try {
    // NOTA: Em ambiente de produção, esta API deve verificar se o usuário é admin
    // Comentado para fins de teste
    // const session = await getServerSession(authOptions);
    //
    // if (!session?.user || session.user.role !== 'admin') {
    //   return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    // }

    const db = getAdminFirestore();
    const plansCollection = db.collection('plans');

    // Registrar os planos que serão criados
    await LogService.logToFile('seed_plans', mockPlans, 'debug');

    // Criar os planos no Firestore
    const results = await Promise.all(
      mockPlans.map(async (plan) => {
        const { id, ...planData } = plan;
        await plansCollection.doc(id).set(planData);
        return id;
      }),
    );

    return NextResponse.json({
      success: true,
      message: `${results.length} planos criados com sucesso`,
      plans: results,
    });
  } catch (error) {
    console.error('Erro ao criar planos:', error);
    await LogService.logError('seed_plans_error', error);

    return NextResponse.json({ error: 'Erro ao criar planos de teste' }, { status: 500 });
  }
}
