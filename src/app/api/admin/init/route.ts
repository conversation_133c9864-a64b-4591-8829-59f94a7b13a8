// src/app/api/admin/init/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { initializeAdmin } from '@/lib/admin/init-admin';

// Variável para controlar se a inicialização já foi executada
let initialized = false;

export async function GET(req: NextRequest) {
  try {
    // Executar a inicialização apenas uma vez
    if (!initialized || true) {
      console.log('Initializing admin...');
      await initializeAdmin();
      initialized = true;
    }

    return NextResponse.json({ success: true, message: 'Admin initialization completed' });
  } catch (error) {
    console.error('Error in admin initialization API:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to initialize admin' },
      { status: 500 },
    );
  }
}
