// src/app/api/admin/plans/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getStripe } from '@/lib/stripe';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { LogService } from '@/lib/services/log-service';

// Verificar se o usuário é um administrador
async function isAdmin(session: any) {
  if (!session?.user) return false;
  
  // Lista de emails de administradores
  const adminEmails = [
    process.env.NEXT_PUBLIC_ADMIN_EMAIL,
    '<EMAIL>',
    '<EMAIL>',
  ].filter(Boolean);
  
  return adminEmails.includes(session.user.email);
}

export async function POST(req: NextRequest) {
  try {
    // Verificar se o usuário está autenticado e é um administrador
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }
    
    const admin = await isAdmin(session);
    
    if (!admin) {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }
    
    // Obter os dados do plano
    const planData = await req.json();
    
    // Log dos dados recebidos
    await LogService.logToFile('plan_create_api_input', planData, 'debug');
    
    try {
      // 1. Criar o produto no Stripe
      const stripe = getStripe();
      
      const stripeProduct = await stripe.products.create({
        name: planData.name,
        description: planData.description,
        active: planData.active,
        metadata: {
          modules: JSON.stringify(planData.modules),
          limits: JSON.stringify(planData.limits),
        },
      });
      
      await LogService.logToFile('plan_create_api_stripe_product', stripeProduct, 'debug');
      
      // 2. Criar o preço no Stripe
      const stripePrice = await stripe.prices.create({
        product: stripeProduct.id,
        unit_amount: Math.round(planData.price * 100), // Converter para centavos
        currency: 'brl',
        recurring: {
          interval: planData.interval,
        },
        metadata: {
          planName: planData.name,
        },
      });
      
      await LogService.logToFile('plan_create_api_stripe_price', stripePrice, 'debug');
      
      // 3. Preparar os dados para o Firestore
      const plan = {
        name: planData.name,
        description: planData.description,
        price: Math.round(planData.price * 100), // Converter para centavos
        interval: planData.interval,
        features: planData.features,
        popular: planData.popular,
        modules: planData.modules,
        limits: planData.limits,
        stripeProductId: stripeProduct.id,
        stripePriceId: stripePrice.id,
        active: planData.active,
        order: planData.order,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: session.user.id,
      };
      
      await LogService.logToFile('plan_create_api_firestore_data', plan, 'debug');
      
      // 4. Salvar no Firestore
      const db = getAdminFirestore();
      const docRef = await db.collection('plans').add(plan);
      
      await LogService.logToFile('plan_create_api_firestore_result', { id: docRef.id }, 'debug');
      
      // 5. Retornar o plano criado
      return NextResponse.json({
        id: docRef.id,
        ...plan,
      });
    } catch (error) {
      await LogService.logError('plan_create_api_error', error);
      console.error('Erro ao criar plano:', error);
      return NextResponse.json({ error: 'Erro ao criar plano' }, { status: 500 });
    }
  } catch (error) {
    console.error('Erro na API de criação de plano:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  try {
    // Verificar se o usuário está autenticado e é um administrador
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }
    
    const admin = await isAdmin(session);
    
    if (!admin) {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }
    
    // Buscar todos os planos
    const db = getAdminFirestore();
    const plansSnapshot = await db.collection('plans').orderBy('order', 'asc').get();
    
    const plans = plansSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    }));
    
    return NextResponse.json(plans);
  } catch (error) {
    console.error('Erro ao buscar planos:', error);
    return NextResponse.json({ error: 'Erro ao buscar planos' }, { status: 500 });
  }
}
