// src/app/api/admin/plans/[id]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { getStripe } from '@/lib/stripe';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { LogService } from '@/lib/services/log-service';

// Verificar se o usuário é um administrador
async function isAdmin(session: any) {
  if (!session?.user) return false;
  
  // Lista de emails de administradores
  const adminEmails = [
    process.env.NEXT_PUBLIC_ADMIN_EMAIL,
    '<EMAIL>',
    '<EMAIL>',
  ].filter(Boolean);
  
  return adminEmails.includes(session.user.email);
}

export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    
    // Verificar se o usuário está autenticado e é um administrador
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }
    
    const admin = await isAdmin(session);
    
    if (!admin) {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }
    
    // Obter os dados do plano
    const planData = await req.json();
    
    // Log dos dados recebidos
    await LogService.logToFile('plan_update_api_input', { id, planData }, 'debug');
    
    try {
      // 1. Buscar o plano atual
      const db = getAdminFirestore();
      const planDoc = await db.collection('plans').doc(id).get();
      
      if (!planDoc.exists) {
        return NextResponse.json({ error: 'Plano não encontrado' }, { status: 404 });
      }
      
      const currentPlan = planDoc.data();
      await LogService.logToFile('plan_update_api_current_plan', currentPlan, 'debug');
      
      // 2. Atualizar o produto no Stripe
      const stripe = getStripe();
      
      if (!currentPlan.stripeProductId) {
        return NextResponse.json({ error: 'ID do produto Stripe não encontrado' }, { status: 400 });
      }
      
      const updatedProduct = await stripe.products.update(currentPlan.stripeProductId, {
        name: planData.name,
        description: planData.description,
        active: planData.active,
        metadata: {
          modules: JSON.stringify(planData.modules),
          limits: JSON.stringify(planData.limits),
        },
      });
      
      await LogService.logToFile('plan_update_api_stripe_product', updatedProduct, 'debug');
      
      // 3. Verificar se o preço ou o intervalo mudou
      let stripePriceId = currentPlan.stripePriceId;
      
      if (
        Math.round(planData.price * 100) !== currentPlan.price ||
        planData.interval !== currentPlan.interval
      ) {
        await LogService.logToFile('plan_update_api_price_changed', {
          oldPrice: currentPlan.price,
          newPrice: Math.round(planData.price * 100),
          oldInterval: currentPlan.interval,
          newInterval: planData.interval,
        }, 'debug');
        
        // Criar um novo preço no Stripe
        const stripePrice = await stripe.prices.create({
          product: currentPlan.stripeProductId,
          unit_amount: Math.round(planData.price * 100),
          currency: 'brl',
          recurring: {
            interval: planData.interval,
          },
          metadata: {
            planName: planData.name,
          },
        });
        
        await LogService.logToFile('plan_update_api_new_stripe_price', stripePrice, 'debug');
        
        stripePriceId = stripePrice.id;
      }
      
      // 4. Preparar os dados para o Firestore
      const plan = {
        name: planData.name,
        description: planData.description,
        price: Math.round(planData.price * 100), // Converter para centavos
        interval: planData.interval,
        features: planData.features,
        popular: planData.popular,
        modules: planData.modules,
        limits: planData.limits,
        stripeProductId: currentPlan.stripeProductId,
        stripePriceId,
        active: planData.active,
        order: planData.order,
        createdAt: currentPlan.createdAt,
        updatedAt: new Date(),
        updatedBy: session.user.id,
      };
      
      await LogService.logToFile('plan_update_api_firestore_data', plan, 'debug');
      
      // 5. Atualizar no Firestore
      await db.collection('plans').doc(id).update(plan);
      await LogService.logToFile('plan_update_api_firestore_success', { id }, 'debug');
      
      // 6. Retornar o plano atualizado
      return NextResponse.json({
        id,
        ...plan,
      });
    } catch (error) {
      await LogService.logError('plan_update_api_error', error);
      console.error('Erro ao atualizar plano:', error);
      return NextResponse.json({ error: 'Erro ao atualizar plano' }, { status: 500 });
    }
  } catch (error) {
    console.error('Erro na API de atualização de plano:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    
    // Verificar se o usuário está autenticado
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }
    
    // Buscar o plano
    const db = getAdminFirestore();
    const planDoc = await db.collection('plans').doc(id).get();
    
    if (!planDoc.exists) {
      return NextResponse.json({ error: 'Plano não encontrado' }, { status: 404 });
    }
    
    return NextResponse.json({
      id: planDoc.id,
      ...planDoc.data(),
    });
  } catch (error) {
    console.error('Erro ao buscar plano:', error);
    return NextResponse.json({ error: 'Erro ao buscar plano' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const id = params.id;
    
    // Verificar se o usuário está autenticado e é um administrador
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }
    
    const admin = await isAdmin(session);
    
    if (!admin) {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }
    
    // Buscar o plano
    const db = getAdminFirestore();
    const planDoc = await db.collection('plans').doc(id).get();
    
    if (!planDoc.exists) {
      return NextResponse.json({ error: 'Plano não encontrado' }, { status: 404 });
    }
    
    const plan = planDoc.data();
    
    // Desativar o produto no Stripe
    const stripe = getStripe();
    
    if (plan.stripeProductId) {
      await stripe.products.update(plan.stripeProductId, {
        active: false,
      });
    }
    
    // Excluir o plano do Firestore
    await db.collection('plans').doc(id).delete();
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Erro ao excluir plano:', error);
    return NextResponse.json({ error: 'Erro ao excluir plano' }, { status: 500 });
  }
}
