import { NextRequest, NextResponse } from 'next/server';
import { getAdminFirestore } from '@/lib/firebase/admin';
import { Timestamp } from 'firebase/firestore';
import { GeoLocation, Country, State } from '@/types/models/location';

/**
 * Endpoint para obter localização por coordenadas
 * GET /api/location?lat=123&lng=456
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const lat = searchParams.get('lat');
    const lng = searchParams.get('lng');
    const country = searchParams.get('country');
    const state = searchParams.get('state');

    // Verificar qual tipo de consulta está sendo feita
    if (lat && lng) {
      return await getLocationByCoordinates(parseFloat(lat), parseFloat(lng));
    } else if (country && !state) {
      return await getCountryStates(country);
    } else if (country && state) {
      return await getStateCities(country, state);
    } else {
      return NextResponse.json({ error: 'Parâmetros inválidos' }, { status: 400 });
    }
  } catch (error) {
    console.error('Erro ao processar requisição de localização:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

/**
 * Obter localização por coordenadas
 */
async function getLocationByCoordinates(latitude: number, longitude: number) {
  try {
    // Arredondar coordenadas para reduzir variações mínimas
    const roundedLat = Math.round(latitude * 10000) / 10000;
    const roundedLng = Math.round(longitude * 10000) / 10000;
    const locationId = `${roundedLat}_${roundedLng}`;

    // Obter instância do Firestore Admin
    const db = getAdminFirestore();
    
    // Verificar se já temos essa localização no cache
    const locationRef = db.collection('locations').doc(locationId);
    const locationDoc = await locationRef.get();

    if (locationDoc.exists) {
      const locationData = locationDoc.data() as GeoLocation;
      
      // Verificar se o cache expirou
      const expiresAt = locationData.expiresAt instanceof Timestamp 
        ? locationData.expiresAt.toDate() 
        : new Date(locationData.expiresAt);
      
      if (expiresAt > new Date()) {
        // Cache válido, retornar dados do cache
        return NextResponse.json(locationData);
      }
    }

    // Cache não existe ou expirou, buscar na API Nominatim
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=18&addressdetails=1`,
      { 
        headers: { 'Accept-Language': 'pt-BR' },
        cache: 'no-store'
      }
    );

    if (!response.ok) {
      throw new Error(`Erro ao buscar localização: ${response.statusText}`);
    }

    const data = await response.json();

    // Preparar dados para salvar no cache
    const now = new Date();
    const expiresAt = new Date();
    expiresAt.setDate(now.getDate() + 30); // Cache válido por 30 dias

    const locationData: GeoLocation = {
      id: locationId,
      latitude: roundedLat,
      longitude: roundedLng,
      address: data.address || {},
      display_name: data.display_name,
      createdAt: now,
      updatedAt: now,
      expiresAt: expiresAt,
      source: 'nominatim',
      raw: data
    };

    // Salvar no cache
    await locationRef.set(locationData);

    return NextResponse.json(locationData);
  } catch (error) {
    console.error('Erro ao obter localização por coordenadas:', error);
    return NextResponse.json({ error: 'Erro ao obter localização' }, { status: 500 });
  }
}

/**
 * Obter estados de um país
 */
async function getCountryStates(countryCode: string) {
  try {
    // Obter instância do Firestore Admin
    const db = getAdminFirestore();
    
    // Verificar se já temos esse país no cache
    const countryRef = db.collection('countries').doc(countryCode);
    const countryDoc = await countryRef.get();

    if (countryDoc.exists) {
      const countryData = countryDoc.data() as Country;
      
      // Verificar se o cache expirou
      const expiresAt = countryData.expiresAt instanceof Timestamp 
        ? countryData.expiresAt.toDate() 
        : new Date(countryData.expiresAt);
      
      if (expiresAt > new Date()) {
        // Cache válido, retornar dados do cache
        return NextResponse.json(countryData);
      }
    }

    // Cache não existe ou expirou, buscar na API do IBGE (para o Brasil)
    if (countryCode.toUpperCase() === 'BR') {
      const response = await fetch(
        'https://servicodados.ibge.gov.br/api/v1/localidades/estados?orderBy=nome'
      );

      if (!response.ok) {
        throw new Error(`Erro ao buscar estados: ${response.statusText}`);
      }

      const data = await response.json();

      // Preparar dados para salvar no cache
      const now = new Date();
      const expiresAt = new Date();
      expiresAt.setFullYear(now.getFullYear() + 1); // Cache válido por 1 ano

      const countryData: Country = {
        id: 'BR',
        name: 'Brasil',
        name_en: 'Brazil',
        states: data.map((state: any) => ({
          code: state.sigla,
          name: state.nome
        })),
        createdAt: now,
        updatedAt: now,
        expiresAt: expiresAt,
        source: 'ibge'
      };

      // Salvar no cache
      await countryRef.set(countryData);

      return NextResponse.json(countryData);
    } else {
      // Para outros países, implementar outras APIs ou retornar erro
      return NextResponse.json({ error: 'País não suportado' }, { status: 400 });
    }
  } catch (error) {
    console.error('Erro ao obter estados do país:', error);
    return NextResponse.json({ error: 'Erro ao obter estados' }, { status: 500 });
  }
}

/**
 * Obter cidades de um estado
 */
async function getStateCities(countryCode: string, stateCode: string) {
  try {
    const stateId = `${countryCode.toUpperCase()}_${stateCode.toUpperCase()}`;
    
    // Obter instância do Firestore Admin
    const db = getAdminFirestore();
    
    // Verificar se já temos esse estado no cache
    const stateRef = db.collection('states').doc(stateId);
    const stateDoc = await stateRef.get();

    if (stateDoc.exists) {
      const stateData = stateDoc.data() as State;
      
      // Verificar se o cache expirou
      const expiresAt = stateData.expiresAt instanceof Timestamp 
        ? stateData.expiresAt.toDate() 
        : new Date(stateData.expiresAt);
      
      if (expiresAt > new Date()) {
        // Cache válido, retornar dados do cache
        return NextResponse.json(stateData);
      }
    }

    // Cache não existe ou expirou, buscar na API do IBGE (para o Brasil)
    if (countryCode.toUpperCase() === 'BR') {
      // Primeiro, obter o nome completo do estado
      const stateResponse = await fetch(
        `https://servicodados.ibge.gov.br/api/v1/localidades/estados/${stateCode}`
      );

      if (!stateResponse.ok) {
        throw new Error(`Erro ao buscar estado: ${stateResponse.statusText}`);
      }

      const stateData = await stateResponse.json();
      const stateName = stateData.nome;

      // Agora, buscar as cidades do estado
      const citiesResponse = await fetch(
        `https://servicodados.ibge.gov.br/api/v1/localidades/estados/${stateCode}/municipios?orderBy=nome`
      );

      if (!citiesResponse.ok) {
        throw new Error(`Erro ao buscar cidades: ${citiesResponse.statusText}`);
      }

      const citiesData = await citiesResponse.json();

      // Função para normalizar texto (remover acentos, converter para minúsculas)
      const normalizeText = (text: string): string => {
        return text
          .normalize('NFD')
          .replace(/[\u0300-\u036f]/g, '')
          .toLowerCase()
          .trim();
      };

      // Preparar dados para salvar no cache
      const now = new Date();
      const expiresAt = new Date();
      expiresAt.setFullYear(now.getFullYear() + 1); // Cache válido por 1 ano

      const stateDataToSave: State = {
        id: stateId,
        country: countryCode.toUpperCase(),
        state: stateCode.toUpperCase(),
        state_full: stateName,
        cities: citiesData.map((city: any) => ({
          id: city.id.toString(),
          name: city.nome,
          normalized_name: normalizeText(city.nome)
        })),
        createdAt: now,
        updatedAt: now,
        expiresAt: expiresAt,
        source: 'ibge'
      };

      // Salvar no cache
      await stateRef.set(stateDataToSave);

      return NextResponse.json(stateDataToSave);
    } else {
      // Para outros países, implementar outras APIs ou retornar erro
      return NextResponse.json({ error: 'País não suportado' }, { status: 400 });
    }
  } catch (error) {
    console.error('Erro ao obter cidades do estado:', error);
    return NextResponse.json({ error: 'Erro ao obter cidades' }, { status: 500 });
  }
}
