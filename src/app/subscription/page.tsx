// src/app/subscription/page.tsx
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { PlanService } from '@/lib/services/plan-service';
import { getUserActiveSubscription } from '@/services/subscriptions';
import SubscriptionPlans from '@/components/subscription/subscription-plans';

export const metadata: Metadata = {
  title: 'Assinatura | JáVai',
  description: 'Escolha o plano ideal para o seu negócio',
};

export default async function SubscriptionPage({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  const session = await getServerSession(authOptions);

  // Redirecionar para login se não estiver autenticado
  if (!session?.user) {
    redirect('/auth/login?callbackUrl=/subscription');
  }

  // Verificar se há um parâmetro de sucesso na URL
  if (searchParams?.success) {
    // Redirecionar para a página de assinatura confirmada
    redirect('/success');
  }

  // Verificar se o usuário já tem uma assinatura ativa
  const activeSubscription = await getUserActiveSubscription(session.user.id);

  // Buscar planos ativos do banco
  const planService = new PlanService();
  const plans = await planService.getActive();
  
  // Se não houver planos no banco, mostrar mensagem de aviso
  if (plans.length === 0) {
    console.warn('Nenhum plano ativo encontrado no banco. Crie planos na área administrativa.');
  }

  return (
    <div className="space-y-6 py-10">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold tracking-tight">Planos de Assinatura</h1>
        <p className="mt-2 text-muted-foreground">
          Escolha o plano ideal para o seu negócio e comece a anunciar hoje mesmo.
        </p>
      </div>

      <SubscriptionPlans
        userId={session.user.id}
        activeSubscription={activeSubscription}
        plans={plans}
      />
    </div>
  );
}
