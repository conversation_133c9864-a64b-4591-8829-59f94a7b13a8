// src/app/subscription/success/page.tsx
import { Metadata } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { redirect } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { CheckCircle2 } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Assinatura Confirmada | JáVai',
  description: 'Sua assinatura foi confirmada com sucesso',
};

export default async function SubscriptionSuccessPage() {
  const session = await getServerSession(authOptions);

  // Redirecionar para login se não estiver autenticado
  if (!session?.user) {
    redirect('/auth/login?callbackUrl=/subscription/success');
  }

  return (
    <div className="py-10">
      <div className="max-w-md mx-auto">
        <Card className="border-green-100">
          <CardHeader className="text-center pb-2">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-50">
              <CheckCircle2 className="h-10 w-10 text-green-500" />
            </div>
            <CardTitle className="text-2xl">Assinatura Confirmada!</CardTitle>
            <CardDescription>Sua assinatura foi processada com sucesso.</CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-2 pb-2">
            <p>
              Obrigado por assinar o JáVai. Você agora tem acesso a todos os recursos do seu plano.
            </p>
            <p className="text-sm text-muted-foreground">
              Um e-mail de confirmação foi enviado para o seu endereço de e-mail cadastrado.
            </p>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <Button asChild className="w-full">
              <Link href="/advertiser/dashboard">Ir para o Dashboard</Link>
            </Button>
            <Button variant="outline" asChild className="w-full">
              <Link href="/subscription">Gerenciar Assinatura</Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
