// src/app/dashboard/subscription/success/page.tsx
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { redirect } from 'next/navigation';
import { getUserActiveSubscription } from '@/services/subscriptions';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import Link from 'next/link';

export default async function SubscriptionSuccessPage({
  searchParams,
}: {
  searchParams: { session_id?: string };
}) {
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    redirect('/auth/login');
  }
  
  // Check if user has an active subscription
  const subscription = await getUserActiveSubscription(session.user.id);
  
  // If no subscription and no session_id, redirect to plans page
  if (!subscription && !searchParams.session_id) {
    redirect('/dashboard/subscription/plans');
  }
  
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="flex justify-center mb-4">
            <CheckCircle className="h-16 w-16 text-green-500" />
          </div>
          <CardTitle className="text-2xl">Assinatura Confirmada!</CardTitle>
          <CardDescription>
            Sua assinatura foi processada com sucesso.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            Agora você tem acesso a todos os recursos do seu plano. Comece a explorar a plataforma e aproveite ao máximo sua assinatura.
          </p>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <Button asChild className="w-full">
            <Link href="/dashboard/subscription">
              Ver Detalhes da Assinatura
            </Link>
          </Button>
          <Button asChild variant="outline" className="w-full">
            <Link href="/dashboard">
              Ir para o Dashboard
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
