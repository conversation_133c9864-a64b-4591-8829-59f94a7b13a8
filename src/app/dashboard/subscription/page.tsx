// src/app/dashboard/subscription/page.tsx
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth/auth-options";
import { redirect } from "next/navigation";
import { getUserActiveSubscription } from "@/services/subscriptions";
import { getPlanById } from "@/services/plans";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import SubscriptionManagement from "@/components/subscription/subscription-management";

export default async function SubscriptionPage() {
  const session = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/auth/login");
  }

  const subscription = await getUserActiveSubscription(session.user.id);

  if (!subscription) {
    redirect("/dashboard/subscription/plans");
  }

  console.log('planId', subscription.planId);
  const plan = await getPlanById(subscription.planId);

  if (!plan) {
    // This should not happen, but just in case
    redirect("/dashboard/subscription/plans");
  }

  const formatDate = (date: Date) => {
    return format(date, "dd/MM/yyyy", { locale: ptBR });
  };

  const getStatusBadge = () => {
    switch (subscription.status) {
      case "active":
        return <Badge className="bg-green-500">Ativo</Badge>;
      case "trialing":
        return <Badge className="bg-blue-500">Período de teste</Badge>;
      case "past_due":
        return <Badge className="bg-yellow-500">Pagamento pendente</Badge>;
      case "canceled":
        return <Badge className="bg-red-500">Cancelado</Badge>;
      default:
        return <Badge className="bg-gray-500">{subscription.status}</Badge>;
    }
  };

  console.log(plan, subscription)

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Assinatura</h1>
        <p className="text-muted-foreground">
          Gerencie sua assinatura e veja os detalhes do seu plano atual.
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Detalhes da Assinatura</CardTitle>
            {getStatusBadge()}
          </div>
          <CardDescription>
            Informações sobre sua assinatura atual
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Plano
              </h3>
              <p className="text-lg font-semibold">{plan.name}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Preço
              </h3>
              <p className="text-lg font-semibold">
                {new Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(plan.price / 100)}
                /{plan.interval === "month" ? "mês" : "ano"}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Início do período atual
              </h3>
              <p className="text-lg font-semibold">
                {formatDate(subscription.currentPeriodStart)}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">
                Fim do período atual
              </h3>
              <p className="text-lg font-semibold">
                {formatDate(subscription.currentPeriodEnd)}
              </p>
            </div>
          </div>

          <div className="pt-4 border-t">
            <h3 className="text-sm font-medium text-muted-foreground mb-2">
              Módulos incluídos
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {plan.moduleAccess.services && (
                <Badge variant="outline" className="justify-center">
                  Serviços
                </Badge>
              )}
              {plan.moduleAccess.properties && (
                <Badge variant="outline" className="justify-center">
                  Imóveis
                </Badge>
              )}
              {plan.moduleAccess.vehicles && (
                <Badge variant="outline" className="justify-center">
                  Veículos
                </Badge>
              )}
              {plan.moduleAccess.products && (
                <Badge variant="outline" className="justify-center">
                  Produtos
                </Badge>
              )}
            </div>
          </div>

          <div className="pt-4 border-t">
            <h3 className="text-sm font-medium text-muted-foreground mb-2">
              Limites de anúncios
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {plan.moduleAccess.services && (
                <div className="text-center">
                  <p className="text-2xl font-bold">{plan.adLimits.services}</p>
                  <p className="text-sm text-muted-foreground">Serviços</p>
                </div>
              )}
              {plan.moduleAccess.properties && (
                <div className="text-center">
                  <p className="text-2xl font-bold">
                    {plan.adLimits.properties}
                  </p>
                  <p className="text-sm text-muted-foreground">Imóveis</p>
                </div>
              )}
              {plan.moduleAccess.vehicles && (
                <div className="text-center">
                  <p className="text-2xl font-bold">{plan.adLimits.vehicles}</p>
                  <p className="text-sm text-muted-foreground">Veículos</p>
                </div>
              )}
              {plan.moduleAccess.products && (
                <div className="text-center">
                  <p className="text-2xl font-bold">{plan.adLimits.products}</p>
                  <p className="text-sm text-muted-foreground">Produtos</p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <SubscriptionManagement
            subscription={subscription}
            cancelAtPeriodEnd={subscription.cancelAtPeriodEnd}
          />
        </CardFooter>
      </Card>
    </div>
  );
}
