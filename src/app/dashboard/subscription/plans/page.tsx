// src/app/dashboard/subscription/plans/page.tsx
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { redirect } from 'next/navigation';
import { getActivePlans } from '@/services/plans';
import { getUserActiveSubscription } from '@/services/subscriptions';
import PlanCard from '@/components/subscription/plan-card';

export default async function PlansPage() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    redirect('/auth/login');
  }
  
  // Check if user already has an active subscription
  const subscription = await getUserActiveSubscription(session.user.id);
  
  if (subscription) {
    redirect('/dashboard/subscription');
  }
  
  const plans = await getActivePlans();
  
  return (
    <div className="space-y-6">
      <div className="text-center max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold tracking-tight">Escolha seu plano</h1>
        <p className="text-muted-foreground mt-2">
          Selecione o plano que melhor atende às suas necessidades e comece a anunciar hoje mesmo.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
        {plans.map((plan) => (
          <PlanCard key={plan.id} plan={plan} />
        ))}
      </div>
      
      <div className="text-center text-sm text-muted-foreground mt-8">
        <p>
          Todos os planos incluem acesso à plataforma JáVai e suporte ao cliente.
          <br />
          Você pode cancelar ou alterar seu plano a qualquer momento.
        </p>
      </div>
    </div>
  );
}
