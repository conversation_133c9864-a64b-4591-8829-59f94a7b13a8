// src/app/advertiser/dashboard/page.tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  TrendingUp,
  Users,
  Calendar,
  Eye,
  MessageSquare,
  Star,
  Clock,
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { ServiceService } from '@/lib/services/service-service';
import { formatCurrency } from '@/lib/utils';
import RecentAppointments from '@/components/advertiser/dashboard/recent-appointments';
import ServicePerformance from '@/components/advertiser/dashboard/service-performance';

export default async function AdvertiserDashboardPage() {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  // Em uma implementação real, buscaríamos esses dados do banco de dados
  // Por enquanto, usamos dados mockados
  const stats = {
    totalViews: 1250,
    totalInquiries: 45,
    totalAppointments: 12,
    totalEarnings: 450000, // em centavos
    averageRating: 4.7,
    pendingAppointments: 3,
    activeServices: 5,
    conversionRate: 8.5,
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Bem-vindo de volta, {session?.user?.name?.split(' ')[0] || 'Anunciante'}! Aqui está um
          resumo da sua atividade.
        </p>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="appointments">Agendamentos</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Visualizações</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalViews.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  <TrendingUp className="inline h-3 w-3 mr-1 text-green-500" />
                  +12% em relação ao mês anterior
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Contatos</CardTitle>
                <MessageSquare className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalInquiries}</div>
                <p className="text-xs text-muted-foreground">
                  Taxa de conversão: {stats.conversionRate}%
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Agendamentos</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalAppointments}</div>
                <p className="text-xs text-muted-foreground">
                  <Clock className="inline h-3 w-3 mr-1 text-yellow-500" />
                  {stats.pendingAppointments} pendentes
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avaliação Média</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.averageRating.toFixed(1)}/5.0</div>
                <p className="text-xs text-muted-foreground">Baseado em 23 avaliações</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Desempenho dos Serviços</CardTitle>
                <CardDescription>Visualizações e contatos nos últimos 30 dias</CardDescription>
              </CardHeader>
              <CardContent>
                <ServicePerformance />
              </CardContent>
            </Card>

            <Card className="col-span-3">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Próximos Agendamentos</CardTitle>
                  <CardDescription>
                    Você tem {stats.pendingAppointments} agendamentos pendentes
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/advertiser/appointments">Ver todos</Link>
                </Button>
              </CardHeader>
              <CardContent>
                <RecentAppointments />
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Meus Serviços</CardTitle>
                <CardDescription>Você tem {stats.activeServices} serviços ativos</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Serviço de Encanamento Residencial</div>
                      <div className="text-sm text-muted-foreground">Publicado há 2 meses</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-green-600">{formatCurrency(15000)}</div>
                      <div className="text-sm text-muted-foreground">Por hora</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Eye className="h-3.5 w-3.5" />
                    <span>120 visualizações</span>
                    <MessageSquare className="h-3.5 w-3.5 ml-2" />
                    <span>8 contatos</span>
                    <Star className="h-3.5 w-3.5 ml-2 text-yellow-500 fill-yellow-500" />
                    <span>4.8</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Instalação de Ar Condicionado</div>
                      <div className="text-sm text-muted-foreground">Publicado há 1 mês</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-green-600">{formatCurrency(25000)}</div>
                      <div className="text-sm text-muted-foreground">Fixo</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Eye className="h-3.5 w-3.5" />
                    <span>85 visualizações</span>
                    <MessageSquare className="h-3.5 w-3.5 ml-2" />
                    <span>5 contatos</span>
                    <Star className="h-3.5 w-3.5 ml-2 text-yellow-500 fill-yellow-500" />
                    <span>4.5</span>
                  </div>
                </div>

                <Button variant="outline" size="sm" className="w-full" asChild>
                  <Link href="/advertiser/services">Ver todos os serviços</Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="col-span-4">
              <CardHeader>
                <CardTitle>Ganhos Estimados</CardTitle>
                <CardDescription>Ganhos nos últimos 6 meses</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px] w-full bg-slate-100 dark:bg-slate-800 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Gráfico de ganhos será exibido aqui</p>
                </div>
                <div className="mt-4 flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Total Estimado</div>
                    <div className="text-2xl font-bold">{formatCurrency(stats.totalEarnings)}</div>
                  </div>
                  <Button variant="outline" size="sm">
                    Exportar Relatório
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="appointments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Agendamentos</CardTitle>
              <CardDescription>Gerencie seus agendamentos e compromissos</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center bg-slate-100 dark:bg-slate-800 rounded-md">
                <p className="text-muted-foreground">
                  Calendário de agendamentos será exibido aqui
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Analytics</CardTitle>
              <CardDescription>Métricas detalhadas de desempenho dos seus serviços</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center bg-slate-100 dark:bg-slate-800 rounded-md">
                <p className="text-muted-foreground">Gráficos de analytics serão exibidos aqui</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
