// src/app/advertiser/services/new/page.tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import ServiceForm from '@/components/advertiser/services/service-form';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { checkResourceLimit, countUserResources } from '@/services/subscriptions';
import { redirect } from 'next/navigation';

export default async function NewServicePage() {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  if (!userId) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro de autenticação</AlertTitle>
          <AlertDescription>
            Você precisa estar autenticado para acessar esta página.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Verificar limites do plano
  const serviceCount = await countUserResources(userId, 'services');
  const limitInfo = await checkResourceLimit(userId, 'services', serviceCount);

  // Verificar se o usuário tem acesso ao módulo de serviços
  if (!limitInfo.hasAccess) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Acesso negado</AlertTitle>
          <AlertDescription>
            Seu plano atual não inclui acesso ao módulo de serviços.
            <div className="mt-2">
              <Button asChild variant="outline" size="sm">
                <Link href="/subscription">Fazer upgrade</Link>
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Verificar se o usuário atingiu o limite de serviços
  if (limitInfo.hasReachedLimit) {
    // Redirecionar para a página de serviços com mensagem de erro
    redirect('/advertiser/services');
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/advertiser/services">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Novo Serviço</h1>
            <p className="text-muted-foreground">
              Crie um novo serviço para oferecer aos seus clientes.
            </p>
          </div>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Informações do Serviço</CardTitle>
          <CardDescription>
            Preencha os detalhes do serviço que você deseja oferecer.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ServiceForm />
        </CardContent>
      </Card>
    </div>
  );
}
