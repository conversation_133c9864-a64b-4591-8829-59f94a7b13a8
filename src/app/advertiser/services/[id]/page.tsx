// src/app/advertiser/services/[id]/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, AlertCircle, Edit, BarChart2, Archive, ExternalLink } from 'lucide-react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ServiceService } from '@/lib/services/service-service';
import { notFound, useRouter } from 'next/navigation';
import { formatCurrency, formatDate } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { Service } from '@/types/models/service';

interface ServicePageProps {
  params: {
    id: string;
  };
}

export default function AdvertiserServicePage({ params }: ServicePageProps) {
  const { data: session, status } = useSession();
  const userId = session?.user?.id;
  const router = useRouter();

  const [service, setService] = useState<Service | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);

  // Buscar o serviço pelo ID
  useEffect(() => {
    const fetchService = async () => {
      if (status === 'loading') return;

      if (!userId) {
        return;
      }

      try {
        setLoading(true);
        const serviceService = new ServiceService();
        const fetchedService = await serviceService.getById(params.id);

        if (!fetchedService) {
          notFound();
          return;
        }

        // Verificar se o serviço pertence ao usuário logado
        if (fetchedService.advertiserId !== userId) {
          toast.error('Acesso negado', {
            description: 'Você não tem permissão para visualizar este serviço.',
          });
          router.push('/advertiser/services');
          return;
        }

        setService(fetchedService);
      } catch (error) {
        console.error('Erro ao buscar serviço:', error);
        toast.error('Erro', {
          description: 'Ocorreu um erro ao buscar o serviço. Tente novamente.',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchService();
  }, [params.id, userId, status, router, toast]);

  // Função para publicar o serviço
  const handlePublish = async () => {
    if (!service) return;

    try {
      setActionLoading(true);
      const serviceService = new ServiceService();
      const updatedService = await serviceService.publish(service.id);

      setService(updatedService);

      toast.success('Serviço publicado', {
        description: 'Seu serviço foi publicado com sucesso!',
      });
    } catch (error) {
      console.error('Erro ao publicar serviço:', error);
      toast.error('Erro ao publicar', {
        description: 'Ocorreu um erro ao publicar o serviço. Tente novamente.',
      });
    } finally {
      setActionLoading(false);
    }
  };

  // Função para arquivar o serviço
  const handleArchive = async () => {
    if (!service) return;

    try {
      setActionLoading(true);
      const serviceService = new ServiceService();
      const updatedService = await serviceService.archive(service.id);

      setService(updatedService);

      toast.success('Serviço arquivado', {
        description: 'Seu serviço foi arquivado com sucesso!',
      });
    } catch (error) {
      console.error('Erro ao arquivar serviço:', error);
      toast.error('Erro ao arquivar', {
        description: 'Ocorreu um erro ao arquivar o serviço. Tente novamente.',
      });
    } finally {
      setActionLoading(false);
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!userId) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro de autenticação</AlertTitle>
          <AlertDescription>
            Você precisa estar autenticado para acessar esta página.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!service) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Serviço não encontrado</AlertTitle>
          <AlertDescription>
            O serviço que você está procurando não existe ou foi removido.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Formatar preço com tipo
  const formatPriceWithType = (price: number, priceType: string) => {
    const formattedPrice = formatCurrency(price);

    const priceTypeMap: Record<string, string> = {
      fixed: '',
      hourly: '/hora',
      daily: '/dia',
      monthly: '/mês',
      negotiable: ' (negociável)',
    };

    return `${formattedPrice}${priceTypeMap[priceType] || ''}`;
  };

  // Imagem padrão caso o serviço não tenha imagem
  const mainImage =
    service.images && service.images.length > 0
      ? service.images[0]
      : '/images/service-placeholder.jpg';

  // Função para obter o badge de status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-500">Publicado</Badge>;
      case 'draft':
        return <Badge variant="outline">Rascunho</Badge>;
      case 'pending_review':
        return <Badge className="bg-yellow-500">Em revisão</Badge>;
      case 'archived':
        return <Badge variant="secondary">Arquivado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" asChild>
            <Link href="/advertiser/services">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Detalhes do Serviço</h1>
            <p className="text-muted-foreground">
              Visualize e gerencie as informações do seu serviço.
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link href={`/advertiser/services/${service.id}/edit`}>
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href={`/advertiser/services/${service.id}/stats`}>
              <BarChart2 className="h-4 w-4 mr-2" />
              Estatísticas
            </Link>
          </Button>
          {service.status === 'draft' && (
            <Button variant="default" onClick={handlePublish} disabled={actionLoading}>
              <ExternalLink className="h-4 w-4 mr-2" />
              {actionLoading ? 'Publicando...' : 'Publicar'}
            </Button>
          )}
          {service.status === 'published' && (
            <Button variant="destructive" onClick={handleArchive} disabled={actionLoading}>
              <Archive className="h-4 w-4 mr-2" />
              {actionLoading ? 'Arquivando...' : 'Arquivar'}
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Coluna principal */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-2xl font-bold">{service.title}</h2>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="text-muted-foreground">{service.categoryName}</span>
                    {service.subcategoryName && (
                      <>
                        <span className="text-muted-foreground">•</span>
                        <span className="text-muted-foreground">{service.subcategoryName}</span>
                      </>
                    )}
                  </div>
                </div>
                <div className="flex flex-col items-end">
                  {getStatusBadge(service.status)}
                  <span className="text-sm text-muted-foreground mt-1">
                    Última atualização: {formatDate(service.updatedAt)}
                  </span>
                </div>
              </div>

              <div className="aspect-video relative rounded-md overflow-hidden mb-6">
                <Image
                  src={mainImage}
                  alt={service.title}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
                  className="object-cover"
                  priority
                />
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Preço</h3>
                  <div className="text-2xl font-bold text-primary">
                    {formatPriceWithType(service.price, service.priceType)}
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-lg font-semibold mb-2">Descrição</h3>
                  <p className="text-muted-foreground whitespace-pre-line">{service.description}</p>
                </div>

                {service.tags && service.tags.length > 0 && (
                  <>
                    <Separator />
                    <div>
                      <h3 className="text-lg font-semibold mb-2">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {service.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Coluna lateral */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Estatísticas</CardTitle>
              <CardDescription>Desempenho do seu serviço</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Visualizações</span>
                <span className="font-medium">{service.views || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Avaliação</span>
                <span className="font-medium">
                  {service.rating?.toFixed(1) || 'N/A'} ({service.ratingCount || 0})
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Contatos</span>
                <span className="font-medium">0</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Favoritos</span>
                <span className="font-medium">{service.likes || 0}</span>
              </div>

              <Separator />

              <div className="pt-2">
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/advertiser/services/${service.id}/stats`}>
                    <BarChart2 className="h-4 w-4 mr-2" />
                    Ver estatísticas detalhadas
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Ações</CardTitle>
              <CardDescription>Gerencie seu serviço</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href={`/advertiser/services/${service.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Editar serviço
                </Link>
              </Button>

              {service.status === 'draft' && (
                <Button
                  variant="default"
                  className="w-full justify-start"
                  onClick={handlePublish}
                  disabled={actionLoading}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  {actionLoading ? 'Publicando...' : 'Publicar serviço'}
                </Button>
              )}

              {service.status === 'published' && (
                <Button
                  variant="destructive"
                  className="w-full justify-start"
                  onClick={handleArchive}
                  disabled={actionLoading}
                >
                  <Archive className="h-4 w-4 mr-2" />
                  {actionLoading ? 'Arquivando...' : 'Arquivar serviço'}
                </Button>
              )}

              <Button variant="outline" className="w-full justify-start" asChild>
                <Link href={`/public/services/${service.id}`} target="_blank">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Ver como cliente
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
