// src/app/advertiser/services/page.tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Filter, PlusCircle, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import AdvertiserServiceTable from '@/components/advertiser/services/advertiser-service-table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { checkResourceLimit, countUserResources } from '@/services/subscriptions';

export default async function AdvertiserServicesPage() {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;

  if (!userId) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro de autenticação</AlertTitle>
          <AlertDescription>
            Você precisa estar autenticado para acessar esta página.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Verificar limites do plano
  const serviceCount = await countUserResources(userId, 'services');
  const limitInfo = await checkResourceLimit(userId, 'services', serviceCount);

  // Verificar se o usuário tem acesso ao módulo de serviços
  if (!limitInfo.hasAccess) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Acesso negado</AlertTitle>
          <AlertDescription>
            Seu plano atual não inclui acesso ao módulo de serviços.
            <div className="mt-2">
              <Button asChild variant="outline" size="sm">
                <Link href="/subscription">Fazer upgrade</Link>
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Meus Serviços</h1>
          <p className="text-muted-foreground">Gerencie os serviços que você oferece.</p>
        </div>
        {limitInfo.hasReachedLimit ? (
          <Button asChild variant="outline" disabled>
            <Link href="#">
              <AlertCircle className="mr-2 h-4 w-4" />
              Limite atingido
            </Link>
          </Button>
        ) : (
          <Button asChild>
            <Link href="/advertiser/services/new">
              <PlusCircle className="mr-2 h-4 w-4" />
              Novo Serviço
            </Link>
          </Button>
        )}
      </div>

      {/* Alerta de limite */}
      {limitInfo.hasAccess && limitInfo.limit !== Infinity && (
        <Alert
          variant={
            limitInfo.hasReachedLimit
              ? 'destructive'
              : limitInfo.remaining && limitInfo.remaining <= 3
                ? 'default'
                : 'default'
          }
          className="mb-6"
        >
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>
            {limitInfo.hasReachedLimit ? 'Limite atingido' : 'Limite de serviços'}
          </AlertTitle>
          <AlertDescription className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <span>
              {limitInfo.hasReachedLimit
                ? `Você atingiu o limite de ${limitInfo.limit} serviços do seu plano ${limitInfo.planName}.`
                : `Você tem ${limitInfo.remaining} serviço${limitInfo.remaining !== 1 ? 's' : ''} restante${limitInfo.remaining !== 1 ? 's' : ''} de ${limitInfo.limit} do seu plano ${limitInfo.planName}.`}
            </span>
            <Button asChild size="sm" variant="outline">
              <Link href="/subscription">
                {limitInfo.hasReachedLimit ? 'Fazer Upgrade' : 'Ver planos'}
              </Link>
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader className="p-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <CardTitle className="text-xl">Serviços</CardTitle>
            <CardDescription>Lista de todos os seus serviços cadastrados.</CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <div className="relative flex-1 sm:flex-initial">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar serviços..."
                className="pl-8 w-full sm:w-[250px]"
              />
            </div>
            <div className="flex gap-2">
              <Select defaultValue="all">
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="published">Publicados</SelectItem>
                  <SelectItem value="draft">Rascunhos</SelectItem>
                  <SelectItem value="pending_review">Em Revisão</SelectItem>
                  <SelectItem value="archived">Arquivados</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <AdvertiserServiceTable />
        </CardContent>
      </Card>
    </div>
  );
}
