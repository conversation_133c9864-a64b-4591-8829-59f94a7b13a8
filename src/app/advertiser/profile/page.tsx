'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { AdvertiserProfileForm } from '@/components/advertiser/profile/advertiser-profile-form';
import { AdvertiserLocationForm } from '@/components/advertiser/profile/advertiser-location-form';
import { AdvertiserAvailabilityForm } from '@/components/advertiser/profile/advertiser-availability-form';
import { AdvertiserService } from '@/services/advertiser-service';
import { Advertiser } from '@/types/models/advertiser';
import { toast } from 'sonner';

export default function AdvertiserProfilePage() {
  const { user, loading: authLoading, isAuthenticated } = useAuth();
  const [advertiser, setAdvertiser] = useState<Advertiser | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('profile');

  useEffect(() => {
    const fetchAdvertiser = async () => {
      if (authLoading) return;
      
      if (!isAuthenticated || !user?.id) {
        setLoading(false);
        return;
      }
      
      try {
        const advertiserService = new AdvertiserService();
        const advertiserData = await advertiserService.getOrCreate(user.id, user);
        setAdvertiser(advertiserData);
      } catch (error) {
        console.error('Error fetching advertiser:', error);
        toast.error('Erro ao carregar dados do perfil');
      } finally {
        setLoading(false);
      }
    };
    
    fetchAdvertiser();
  }, [user, authLoading, isAuthenticated]);
  
  if (authLoading) {
    return <LoadingSpinner message="Verificando autenticação..." />;
  }
  
  if (!isAuthenticated) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Acesso Restrito</CardTitle>
          <CardDescription>
            Você precisa estar autenticado para acessar esta página.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }
  
  if (loading) {
    return <LoadingSpinner message="Carregando perfil..." />;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Perfil do Anunciante</h1>
        <p className="text-muted-foreground">
          Gerencie suas informações pessoais, localização e disponibilidade.
        </p>
      </div>
      
      <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="profile">Informações Pessoais</TabsTrigger>
          <TabsTrigger value="location">Localização</TabsTrigger>
          <TabsTrigger value="availability">Disponibilidade</TabsTrigger>
        </TabsList>
        
        <TabsContent value="profile" className="space-y-4">
          <AdvertiserProfileForm advertiser={advertiser} setAdvertiser={setAdvertiser} />
        </TabsContent>
        
        <TabsContent value="location" className="space-y-4">
          <AdvertiserLocationForm advertiser={advertiser} setAdvertiser={setAdvertiser} />
        </TabsContent>
        
        <TabsContent value="availability" className="space-y-4">
          <AdvertiserAvailabilityForm advertiser={advertiser} setAdvertiser={setAdvertiser} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
