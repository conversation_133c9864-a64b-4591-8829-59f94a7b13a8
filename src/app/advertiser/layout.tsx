// src/app/advertiser/layout.tsx
import { ReactNode } from 'react';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import AdvertiserSidebar from '@/components/advertiser/advertiser-sidebar';
import AdvertiserHeader from '@/components/advertiser/advertiser-header';
import { db } from '@/lib/firebase/client';
import { doc, getDoc } from 'firebase/firestore';

// Verificar se o usuário é um anunciante
async function isAdvertiser(userId: string): Promise<boolean> {
  try {
    // Verificar se o usuário existe
    const userDoc = await getDoc(doc(db, 'users', userId));

    if (!userDoc.exists()) {
      return false;
    }

    const userData = userDoc.data();

    // Verificar se o usuário tem uma assinatura ativa
    const subscription = userData.subscription;
    if (!subscription || !['active', 'trialing'].includes(subscription.status)) {
      return false;
    }

    // Se o usuário tem uma assinatura ativa, considerar como anunciante
    // Também podemos verificar campos adicionais se necessário
    return true;
  } catch (error) {
    console.error('Error checking advertiser status:', error);
    return false;
  }
}

export default async function AdvertiserLayout({ children }: { children: ReactNode }) {
  const session = await getServerSession(authOptions);

  // Redirecionar para login se não estiver autenticado
  if (!session?.user) {
    redirect('/auth/login?callbackUrl=/advertiser');
  }

  // Verificar se o usuário é um anunciante
  const advertiser = await isAdvertiser(session.user.id);

  // Redirecionar para página de assinatura se não for anunciante
  if (!advertiser) {
    redirect(
      '/subscription?message=Para acessar a área de anunciante, você precisa ter uma assinatura ativa.',
    );
  }

  return (
    <div className="flex min-h-screen h-screen">
      <AdvertiserSidebar />
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        <AdvertiserHeader user={session.user} />
        <main className="p-6 flex-1 overflow-auto">{children}</main>
      </div>
    </div>
  );
}
