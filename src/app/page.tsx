// Landing page components
import HeroSection from '@/components/home/<USER>';
import FeaturesSection from '@/components/home/<USER>';
import HowItWorksSection from '@/components/home/<USER>';
import TestimonialsSection from '@/components/home/<USER>';
import CTASection from '@/components/home/<USER>';
import Link from 'next/link';

export default function Home() {
  return (
    <div className="min-h-screen">
      <HeroSection />
      <FeaturesSection />
      <HowItWorksSection />
      <TestimonialsSection />
      <CTASection />

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">JáVai</h3>
              <p className="text-slate-300">
                Hub de ofertas e oportunidades que conecta pessoas a serviços, imó<PERSON>is, veículos e
                produtos.
              </p>
            </div>

            <div>
              <h4 className="text-lg font-bold mb-4">Categorias</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/public/services" className="text-slate-300 hover:text-white">
                    Serviços
                  </Link>
                </li>
                <li>
                  <Link href="/public/properties" className="text-slate-300 hover:text-white">
                    Imóveis
                  </Link>
                </li>
                <li>
                  <Link href="/public/vehicles" className="text-slate-300 hover:text-white">
                    Veículos
                  </Link>
                </li>
                <li>
                  <Link href="/public/products" className="text-slate-300 hover:text-white">
                    Produtos
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-bold mb-4">Conta</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/auth/login" className="text-slate-300 hover:text-white">
                    Entrar
                  </Link>
                </li>
                <li>
                  <Link href="/auth/register" className="text-slate-300 hover:text-white">
                    Cadastrar
                  </Link>
                </li>
                <li>
                  <Link href="/dashboard" className="text-slate-300 hover:text-white">
                    Área do Cliente
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-bold mb-4">Suporte</h4>
              <ul className="space-y-2">
                <li>
                  <Link href="/public/about" className="text-slate-300 hover:text-white">
                    Sobre Nós
                  </Link>
                </li>
                <li>
                  <Link href="/public/contact" className="text-slate-300 hover:text-white">
                    Contato
                  </Link>
                </li>
                <li>
                  <Link href="/public/faq" className="text-slate-300 hover:text-white">
                    FAQ
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-700 mt-8 pt-8 text-center text-slate-400">
            <p>&copy; {new Date().getFullYear()} JáVai. Todos os direitos reservados.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
