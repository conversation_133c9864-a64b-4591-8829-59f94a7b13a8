'use client';

import { useState } from 'react';
import ServiceFilters from '@/components/public/services/service-filters';
import ServiceList from '@/components/public/services/service-list';
// Removidas importações não utilizadas

interface ClientPageProps {
  searchParams: {
    category?: string;
    location?: string;
    q?: string;
  };
}

export default function ClientPage({ searchParams }: ClientPageProps) {
  const [detectedLocation, setDetectedLocation] = useState<string | null>(null);
  const [locationCoords, setLocationCoords] = useState<{ lat: number; lng: number } | null>(null);

  // Função para lidar com a detecção de localização
  const handleLocationDetected = (city: string, coords: { lat: number; lng: number }) => {
    setDetectedLocation(city);
    setLocationCoords(coords);
  };

  return (
    <div className="space-y-8">
      <ServiceFilters onLocationDetected={handleLocationDetected} />

      <ServiceList
        initialCategory={searchParams.category}
        initialLocation={searchParams.location || ''}
        initialCoords={locationCoords}
      />
    </div>
  );
}
