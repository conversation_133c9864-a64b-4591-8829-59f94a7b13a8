// src/app/public/services/[id]/page.tsx
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ServiceService } from '@/lib/services/service-service';
import { formatCurrency, formatDate } from '@/lib/utils';
import { 
  MapPin, 
  Calendar, 
  Clock, 
  DollarSign, 
  Star, 
  MessageSquare, 
  Share2, 
  Heart, 
  Phone, 
  Mail, 
  User,
  Award,
  CheckCircle,
  AlertCircle,
  FileText,
  HelpCircle
} from 'lucide-react';

interface ServicePageProps {
  params: {
    id: string;
  };
}

export async function generateMetadata({ params }: ServicePageProps): Promise<Metadata> {
  try {
    const serviceService = new ServiceService();
    const service = await serviceService.getById(params.id);
    
    if (!service) {
      return {
        title: 'Serviço não encontrado | JáVai',
      };
    }
    
    return {
      title: `${service.title} | JáVai`,
      description: service.description?.substring(0, 160),
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Detalhes do Serviço | JáVai',
    };
  }
}

export default async function ServicePage({ params }: ServicePageProps) {
  try {
    const serviceService = new ServiceService();
    const service = await serviceService.getById(params.id);
    
    if (!service) {
      notFound();
    }
    
    // Incrementar visualizações
    await serviceService.incrementViews(params.id);
    
    // Formatar preço com tipo
    const formatPriceWithType = (price: number, priceType: string) => {
      const formattedPrice = formatCurrency(price);
      
      const priceTypeMap: Record<string, string> = {
        'fixed': '',
        'hourly': '/hora',
        'daily': '/dia',
        'monthly': '/mês',
        'negotiable': ' (negociável)'
      };
      
      return `${formattedPrice}${priceTypeMap[priceType] || ''}`;
    };
    
    // Imagem padrão caso o serviço não tenha imagem
    const mainImage = service.images && service.images.length > 0
      ? service.images[0]
      : '/images/service-placeholder.jpg';
    
    return (
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Coluna principal */}
          <div className="lg:col-span-2 space-y-6">
            {/* Galeria de imagens */}
            <div className="relative rounded-lg overflow-hidden">
              <div className="aspect-video relative">
                <Image
                  src={mainImage}
                  alt={service.title}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
                  className="object-cover"
                  priority
                />
              </div>
              
              {/* Badges */}
              <div className="absolute top-4 left-4 flex flex-wrap gap-2">
                {service.featured && (
                  <Badge className="bg-yellow-500 hover:bg-yellow-600">Destaque</Badge>
                )}
                {service.status === 'published' && service.publishedAt && 
                  new Date().getTime() - new Date(service.publishedAt).getTime() < 7 * 24 * 60 * 60 * 1000 && (
                  <Badge className="bg-green-500 hover:bg-green-600">Novo</Badge>
                )}
              </div>
            </div>
            
            {/* Miniaturas (se houver mais imagens) */}
            {service.images && service.images.length > 1 && (
              <div className="grid grid-cols-5 gap-2">
                {service.images.map((image, index) => (
                  <div key={index} className="aspect-video relative rounded-md overflow-hidden">
                    <Image
                      src={image}
                      alt={`${service.title} - Imagem ${index + 1}`}
                      fill
                      sizes="(max-width: 768px) 20vw, 10vw"
                      className="object-cover cursor-pointer hover:opacity-80 transition-opacity"
                    />
                  </div>
                ))}
              </div>
            )}
            
            {/* Informações principais */}
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{service.title}</h1>
              
              <div className="flex flex-wrap items-center gap-x-4 gap-y-2 mt-2">
                {/* Categoria */}
                <div className="text-muted-foreground">
                  {service.categoryName}
                  {service.subcategoryName && ` › ${service.subcategoryName}`}
                </div>
                
                {/* Avaliação */}
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 mr-1" fill="currentColor" />
                  <span className="font-medium">{service.rating?.toFixed(1) || '0.0'}</span>
                  <span className="text-muted-foreground text-sm ml-1">
                    ({service.ratingCount || 0} avaliações)
                  </span>
                </div>
                
                {/* Visualizações */}
                <div className="text-muted-foreground text-sm">
                  {service.views || 0} visualizações
                </div>
                
                {/* Data de publicação */}
                <div className="text-muted-foreground text-sm">
                  Publicado em {service.publishedAt ? formatDate(new Date(service.publishedAt)) : 'N/A'}
                </div>
              </div>
              
              {/* Preço */}
              <div className="mt-4 flex items-center">
                <DollarSign className="h-5 w-5 text-primary mr-1" />
                <span className="text-2xl font-bold text-primary">
                  {formatPriceWithType(service.price, service.priceType)}
                </span>
              </div>
            </div>
            
            {/* Abas de conteúdo */}
            <Tabs defaultValue="description" className="mt-6">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="description">Descrição</TabsTrigger>
                <TabsTrigger value="details">Detalhes</TabsTrigger>
                <TabsTrigger value="portfolio">Portfólio</TabsTrigger>
                <TabsTrigger value="faq">Perguntas</TabsTrigger>
              </TabsList>
              
              {/* Descrição */}
              <TabsContent value="description" className="space-y-4">
                <div className="prose max-w-none">
                  <p>{service.description}</p>
                </div>
                
                {service.requirements && service.requirements.length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold mb-2">Requisitos</h3>
                    <ul className="list-disc pl-5 space-y-1">
                      {service.requirements.map((req, index) => (
                        <li key={index}>{req}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </TabsContent>
              
              {/* Detalhes */}
              <TabsContent value="details" className="space-y-4">
                {/* Qualificações */}
                {service.qualifications && service.qualifications.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Qualificações</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {service.qualifications.map((qual, index) => (
                        <div key={index} className="flex items-center">
                          <Award className="h-4 w-4 mr-2 text-primary" />
                          <span>{qual}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Experiência */}
                {service.experience && (
                  <div className="mt-4">
                    <h3 className="text-lg font-semibold mb-2">Experiência</h3>
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-primary" />
                      <span>{service.experience} anos de experiência</span>
                    </div>
                  </div>
                )}
                
                {/* Opções de serviço */}
                {service.options && service.options.length > 0 && (
                  <div className="mt-4">
                    <h3 className="text-lg font-semibold mb-2">Opções de Serviço</h3>
                    <div className="space-y-2">
                      {service.options.map((option) => (
                        <Card key={option.id}>
                          <CardContent className="p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-medium">{option.name}</h4>
                                {option.description && (
                                  <p className="text-sm text-muted-foreground">{option.description}</p>
                                )}
                                {option.duration && (
                                  <div className="flex items-center mt-1 text-sm">
                                    <Clock className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                                    <span>{option.duration} minutos</span>
                                  </div>
                                )}
                              </div>
                              <div className="font-bold text-primary">
                                {formatCurrency(option.price)}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Área de cobertura */}
                {service.coverageArea && (
                  <div className="mt-4">
                    <h3 className="text-lg font-semibold mb-2">Área de Cobertura</h3>
                    {service.coverageArea.cities && service.coverageArea.cities.length > 0 && (
                      <div className="mb-2">
                        <h4 className="font-medium text-sm mb-1">Cidades atendidas:</h4>
                        <div className="flex flex-wrap gap-1">
                          {service.coverageArea.cities.map((city, index) => (
                            <Badge key={index} variant="outline">{city}</Badge>
                          ))}
                        </div>
                      </div>
                    )}
                    {service.coverageArea.radius && (
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-2 text-primary" />
                        <span>Raio de {service.coverageArea.radius} km</span>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>
              
              {/* Portfólio */}
              <TabsContent value="portfolio" className="space-y-4">
                {service.portfolio && service.portfolio.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {service.portfolio.map((item, index) => (
                      <Card key={index}>
                        <div className="aspect-video relative">
                          <Image
                            src={item.imageUrl}
                            alt={item.title}
                            fill
                            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                            className="object-cover"
                          />
                        </div>
                        <CardContent className="p-4">
                          <h4 className="font-medium">{item.title}</h4>
                          {item.description && (
                            <p className="text-sm text-muted-foreground">{item.description}</p>
                          )}
                          {item.date && (
                            <div className="flex items-center mt-1 text-sm text-muted-foreground">
                              <Calendar className="h-3.5 w-3.5 mr-1" />
                              <span>{formatDate(new Date(item.date))}</span>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                    <h3 className="text-lg font-medium">Nenhum item no portfólio</h3>
                    <p className="text-muted-foreground">
                      Este prestador ainda não adicionou itens ao seu portfólio.
                    </p>
                  </div>
                )}
              </TabsContent>
              
              {/* Perguntas frequentes */}
              <TabsContent value="faq" className="space-y-4">
                {service.faq && service.faq.length > 0 ? (
                  <div className="space-y-3">
                    {service.faq.map((item, index) => (
                      <Card key={index}>
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            <HelpCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                            <div>
                              <h4 className="font-medium">{item.question}</h4>
                              <p className="text-muted-foreground mt-1">{item.answer}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <HelpCircle className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                    <h3 className="text-lg font-medium">Nenhuma pergunta frequente</h3>
                    <p className="text-muted-foreground">
                      Este prestador ainda não adicionou perguntas frequentes.
                    </p>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
          
          {/* Coluna lateral */}
          <div className="space-y-6">
            {/* Card do prestador */}
            <Card>
              <CardHeader>
                <CardTitle>Prestador de Serviço</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <User className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">{service.advertiserName}</h3>
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-3.5 w-3.5 mr-1" />
                      <span>Membro desde {formatDate(new Date(service.createdAt))}</span>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                {/* Informações de contato */}
                <div className="space-y-2">
                  {service.contactPhone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-primary" />
                      <span>{service.contactPhone}</span>
                    </div>
                  )}
                  
                  {service.contactEmail && (
                    <div className="flex items-center">
                      <Mail className="h-4 w-4 mr-2 text-primary" />
                      <span>{service.contactEmail}</span>
                    </div>
                  )}
                  
                  {service.address && (
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 mr-2 text-primary mt-0.5" />
                      <div>
                        <p>
                          {service.address.city}, {service.address.state}
                        </p>
                        {service.address.neighborhood && (
                          <p className="text-sm text-muted-foreground">
                            {service.address.neighborhood}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                
                <Separator />
                
                {/* Botões de ação */}
                <div className="space-y-2">
                  <Button className="w-full">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Enviar mensagem
                  </Button>
                  
                  <Button variant="outline" className="w-full">
                    <Phone className="mr-2 h-4 w-4" />
                    Ver telefone
                  </Button>
                  
                  <div className="flex gap-2 mt-2">
                    <Button variant="outline" className="flex-1">
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {/* Card de agendamento */}
            <Card>
              <CardHeader>
                <CardTitle>Agendar Serviço</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-4">
                  <Calendar className="h-12 w-12 mx-auto text-primary mb-2" />
                  <h3 className="text-lg font-medium">Agendamento Online</h3>
                  <p className="text-muted-foreground text-sm mt-1">
                    Agende este serviço diretamente pela plataforma
                  </p>
                </div>
                
                <Button className="w-full">
                  <Calendar className="mr-2 h-4 w-4" />
                  Agendar agora
                </Button>
              </CardContent>
            </Card>
            
            {/* Card de localização */}
            {service.address && (
              <Card>
                <CardHeader>
                  <CardTitle>Localização</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="aspect-video relative rounded-md overflow-hidden bg-slate-100">
                    {/* Aqui seria renderizado um mapa real */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center p-4">
                        <MapPin className="h-8 w-8 mx-auto text-primary mb-2" />
                        <p className="font-medium">
                          {service.address.city}, {service.address.state}
                        </p>
                        {service.address.neighborhood && (
                          <p className="text-sm text-muted-foreground">
                            {service.address.neighborhood}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* Serviços relacionados */}
            <Card>
              <CardHeader>
                <CardTitle>Serviços Relacionados</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-4">
                  <p className="text-muted-foreground">
                    Carregando serviços relacionados...
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    );
  } catch (error) {
    console.error('Error loading service:', error);
    notFound();
  }
}
