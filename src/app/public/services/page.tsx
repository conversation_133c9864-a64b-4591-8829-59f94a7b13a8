// src/app/public/services/page.tsx
import { Metadata } from 'next';
import ClientPage from './client';

export const metadata: Metadata = {
  title: 'Serviços | JáVai',
  description:
    'Encontre os melhores serviços próximos a você. Profissionais qualificados para atender suas necessidades.',
};

export default function ServicesPage({
  searchParams,
}: {
  searchParams: { category?: string; location?: string; q?: string };
}) {
  return (
    <main className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight mb-2">Serviços</h1>
          <p className="text-muted-foreground">
            Encontre os melhores profissionais para atender suas necessidades
          </p>
        </div>

        <ClientPage searchParams={searchParams} />
      </div>
    </main>
  );
}
