// src/app/admin/analytics/page.tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  CreditCard,
  Calendar
} from 'lucide-react';

export default async function AdminAnalyticsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
        <p className="text-muted-foreground">
          Análise detalhada de métricas e desempenho do sistema.
        </p>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="users">Usu<PERSON>rios</TabsTrigger>
          <TabsTrigger value="subscriptions">Assinaturas</TabsTrigger>
          <TabsTrigger value="content">Conteúdo</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Crescimento de Usuários
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+12.5%</div>
                <p className="text-xs text-muted-foreground">
                  Em relação ao mês anterior
                </p>
                <div className="mt-4 h-[80px] w-full bg-slate-100 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Gráfico de crescimento</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Taxa de Conversão
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8.7%</div>
                <p className="text-xs text-muted-foreground">
                  +1.2% em relação ao mês anterior
                </p>
                <div className="mt-4 h-[80px] w-full bg-slate-100 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Gráfico de conversão</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Receita Recorrente Mensal
                </CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">R$ 45.000,00</div>
                <p className="text-xs text-muted-foreground">
                  +15% em relação ao mês anterior
                </p>
                <div className="mt-4 h-[80px] w-full bg-slate-100 rounded-md flex items-center justify-center">
                  <p className="text-muted-foreground">Gráfico de receita</p>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Métricas ao Longo do Tempo</CardTitle>
              <CardDescription>
                Evolução das principais métricas nos últimos 12 meses.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] w-full bg-slate-100 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Gráfico de métricas ao longo do tempo</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Análise de Usuários</CardTitle>
              <CardDescription>
                Métricas detalhadas sobre aquisição e retenção de usuários.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Novos Usuários</h3>
                  <p className="text-2xl font-bold">120</p>
                  <p className="text-xs text-muted-foreground">Último mês</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Usuários Ativos</h3>
                  <p className="text-2xl font-bold">850</p>
                  <p className="text-xs text-muted-foreground">Último mês</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Taxa de Retenção</h3>
                  <p className="text-2xl font-bold">78%</p>
                  <p className="text-xs text-muted-foreground">Último mês</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Tempo Médio de Sessão</h3>
                  <p className="text-2xl font-bold">8:45</p>
                  <p className="text-xs text-muted-foreground">Minutos</p>
                </div>
              </div>
              
              <div className="h-[300px] w-full bg-slate-100 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Gráfico de análise de usuários</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="subscriptions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Análise de Assinaturas</CardTitle>
              <CardDescription>
                Métricas detalhadas sobre assinaturas e receita.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">MRR</h3>
                  <p className="text-2xl font-bold">R$ 45.000</p>
                  <p className="text-xs text-muted-foreground">Receita Recorrente Mensal</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">ARPU</h3>
                  <p className="text-2xl font-bold">R$ 99,50</p>
                  <p className="text-xs text-muted-foreground">Receita Média por Usuário</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Churn Rate</h3>
                  <p className="text-2xl font-bold">3.2%</p>
                  <p className="text-xs text-muted-foreground">Taxa de Cancelamento</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">LTV</h3>
                  <p className="text-2xl font-bold">R$ 1.250</p>
                  <p className="text-xs text-muted-foreground">Valor do Tempo de Vida</p>
                </div>
              </div>
              
              <div className="h-[300px] w-full bg-slate-100 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Gráfico de análise de assinaturas</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="content" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Análise de Conteúdo</CardTitle>
              <CardDescription>
                Métricas detalhadas sobre o conteúdo publicado no sistema.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Total de Anúncios</h3>
                  <p className="text-2xl font-bold">1.250</p>
                  <p className="text-xs text-muted-foreground">Anúncios ativos</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Novos Anúncios</h3>
                  <p className="text-2xl font-bold">85</p>
                  <p className="text-xs text-muted-foreground">Último mês</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Taxa de Conversão</h3>
                  <p className="text-2xl font-bold">5.8%</p>
                  <p className="text-xs text-muted-foreground">Visualizações para contatos</p>
                </div>
                
                <div className="bg-slate-100 p-4 rounded-md">
                  <h3 className="text-sm font-medium mb-1">Tempo Médio de Listagem</h3>
                  <p className="text-2xl font-bold">18</p>
                  <p className="text-xs text-muted-foreground">Dias até conversão</p>
                </div>
              </div>
              
              <div className="h-[300px] w-full bg-slate-100 rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Gráfico de análise de conteúdo</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
