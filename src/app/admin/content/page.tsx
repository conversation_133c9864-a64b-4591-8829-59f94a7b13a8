// src/app/admin/content/page.tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Filter, AlertTriangle, ArrowRight } from 'lucide-react';
import ContentTable from '@/components/admin/content-table';
import Link from 'next/link';

export default async function AdminContentPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gerenciamento de Conteúdo</h1>
          <p className="text-muted-foreground">
            Modere e gerencie o conteúdo publicado no sistema.
          </p>
        </div>
        <Button variant="destructive">
          <AlertTriangle className="mr-2 h-4 w-4" />
          <PERSON>úncias (12)
        </Button>
      </div>

      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <Link href="/admin/content/services">
          <Card className="hover:bg-slate-50 transition-colors cursor-pointer">
            <CardHeader>
              <CardTitle>Serviços</CardTitle>
              <CardDescription>Gerenciar serviços publicados</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">127</div>
              <ArrowRight className="h-5 w-5 text-muted-foreground" />
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/content/properties">
          <Card className="hover:bg-slate-50 transition-colors cursor-pointer">
            <CardHeader>
              <CardTitle>Imóveis</CardTitle>
              <CardDescription>Gerenciar imóveis publicados</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">85</div>
              <ArrowRight className="h-5 w-5 text-muted-foreground" />
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/content/vehicles">
          <Card className="hover:bg-slate-50 transition-colors cursor-pointer">
            <CardHeader>
              <CardTitle>Veículos</CardTitle>
              <CardDescription>Gerenciar veículos publicados</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">64</div>
              <ArrowRight className="h-5 w-5 text-muted-foreground" />
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/content/products">
          <Card className="hover:bg-slate-50 transition-colors cursor-pointer">
            <CardHeader>
              <CardTitle>Produtos</CardTitle>
              <CardDescription>Gerenciar produtos publicados</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-between items-center">
              <div className="text-2xl font-bold">93</div>
              <ArrowRight className="h-5 w-5 text-muted-foreground" />
            </CardContent>
          </Card>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Conteúdo Recente</CardTitle>
          <CardDescription>Conteúdo publicado recentemente em todas as categorias</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between items-center mb-4">
            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Buscar conteúdo..."
                  className="pl-8 w-full sm:w-[250px]"
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <ContentTable />
        </CardContent>
      </Card>
    </div>
  );
}
