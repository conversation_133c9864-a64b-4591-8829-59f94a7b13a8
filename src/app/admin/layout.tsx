// src/app/admin/layout.tsx
import { ReactNode } from 'react';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { AdminSidebar } from '@/components/admin/admin-sidebar';
import { AdminHeader } from '@/components/admin/admin-header';
import { db } from '@/lib/firebase/client';
import { doc, getDoc } from 'firebase/firestore';

// Verificar se o usuário é um administrador usando apenas o Firebase Client SDK
async function isAdmin(userId: string): Promise<boolean> {
  try {
    // Lista de emails de administradores
    const adminEmails = [
      process.env.NEXT_PUBLIC_ADMIN_EMAIL,
      '<EMAIL>',
      '<EMAIL>',
    ].filter(Boolean);

    if (adminEmails.length === 0) {
      console.warn('No admin emails configured');
      return false;
    }

    // Verificar no Firestore se o usuário é administrador
    const userDoc = await getDoc(doc(db, 'users', userId));

    if (userDoc.exists()) {
      const userData = userDoc.data();
      // Verificar se o usuário tem a role 'admin' ou se o email está na lista de admins
      return userData.role === 'admin' || adminEmails.includes(userData.email);
    }

    // Se o usuário não existir no Firestore, verificar se o email do usuário na sessão está na lista de admins
    const session = await getServerSession(authOptions);
    if (session?.user?.email && adminEmails.includes(session.user.email)) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

export default async function AdminLayout({ children }: { children: ReactNode }) {
  const session = await getServerSession(authOptions);

  // Redirecionar para login se não estiver autenticado
  if (!session?.user) {
    redirect('/auth/login?callbackUrl=/admin');
  }

  // Verificar se o usuário é um administrador
  // Lista de emails de administradores
  const adminEmails = [
    process.env.NEXT_PUBLIC_ADMIN_EMAIL,
    '<EMAIL>',
    '<EMAIL>',
  ].filter(Boolean);

  // Verificar diretamente se o email do usuário está na lista de administradores
  if (session.user.email && adminEmails.includes(session.user.email)) {
    // É o admin, continuar
  } else {
    // Se não for um dos emails de admin, verificar no Firestore
    const admin = await isAdmin(session.user.id);

    // Redirecionar para dashboard se não for administrador
    // if (!admin) {
    //   redirect('/dashboard');
    // }
  }

  return (
    <div className="flex min-h-screen h-screen">
      <AdminSidebar />
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        <AdminHeader user={session.user} />
        <main className="p-6 flex-1 overflow-auto">{children}</main>
      </div>
    </div>
  );
}
