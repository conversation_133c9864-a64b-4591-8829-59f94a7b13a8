// src/app/admin/users/page.tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Search,
  PlusCircle,
  UserCheck,
  UserX,
  Filter
} from 'lucide-react';
import UserTable from '@/components/admin/user-table';

export default async function AdminUsersPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gerenciamento de Usuários</h1>
          <p className="text-muted-foreground">
            Visualize e gerencie os usuários do sistema.
          </p>
        </div>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Novo Usuário
        </Button>
      </div>

      <Tabs defaultValue="all" className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <TabsList>
            <TabsTrigger value="all">Todos</TabsTrigger>
            <TabsTrigger value="active">Ativos</TabsTrigger>
            <TabsTrigger value="blocked">Bloqueados</TabsTrigger>
            <TabsTrigger value="admin">Administradores</TabsTrigger>
          </TabsList>
          
          <div className="flex gap-2">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Buscar usuários..."
                className="pl-8 w-full sm:w-[250px]"
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <TabsContent value="all">
          <Card>
            <CardHeader className="p-4">
              <CardTitle className="text-xl">Todos os Usuários</CardTitle>
              <CardDescription>
                Lista completa de todos os usuários registrados no sistema.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <UserTable />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="active">
          <Card>
            <CardHeader className="p-4">
              <CardTitle className="text-xl">Usuários Ativos</CardTitle>
              <CardDescription>
                Usuários com contas ativas no sistema.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <UserTable filter="active" />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="blocked">
          <Card>
            <CardHeader className="p-4">
              <CardTitle className="text-xl">Usuários Bloqueados</CardTitle>
              <CardDescription>
                Usuários com contas bloqueadas ou suspensas.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <UserTable filter="blocked" />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="admin">
          <Card>
            <CardHeader className="p-4">
              <CardTitle className="text-xl">Administradores</CardTitle>
              <CardDescription>
                Usuários com privilégios administrativos.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <UserTable filter="admin" />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
