// src/app/admin/plans/page.tsx
import { Metadata } from 'next';
import { PlanService } from '@/lib/services/plan-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { PlanTable } from '@/components/admin/plans/plan-table';

export const metadata: Metadata = {
  title: 'Gerenciar Planos | JáVai Admin',
  description: 'Gerencie os planos de assinatura disponíveis para os anunciantes',
};

export default async function AdminPlansPage() {
  const planService = new PlanService();
  const plans = await planService.getAll();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Gerenciamento de Planos</h1>
          <p className="text-muted-foreground">
            Configure os planos de assinatura disponíveis no sistema.
          </p>
        </div>
        <Button asChild>
          <Link href="/admin/plans/new">
            <PlusCircle className="mr-2 h-4 w-4" />
            Novo Plano
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader className="p-4">
          <CardTitle className="text-xl">Planos de Assinatura</CardTitle>
          <CardDescription>Gerencie os planos disponíveis para os usuários.</CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <PlanTable plans={plans} />
        </CardContent>
      </Card>
    </div>
  );
}
