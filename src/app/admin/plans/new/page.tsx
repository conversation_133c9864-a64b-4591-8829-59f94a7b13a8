// src/app/admin/plans/new/page.tsx
import { Metadata } from 'next';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PlanForm } from '@/components/admin/plans/plan-form';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Novo Plano | JáVai Admin',
  description: 'Crie um novo plano de assinatura para os anunciantes',
};

export default function NewPlanPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Novo Plano</h1>
          <p className="text-muted-foreground">
            Crie um novo plano de assinatura para os anunciantes.
          </p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/admin/plans">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Detalhes do Plano</CardTitle>
          <CardDescription>Preencha os detalhes do novo plano de assinatura.</CardDescription>
        </CardHeader>
        <CardContent>
          <PlanForm />
        </CardContent>
      </Card>
    </div>
  );
}
