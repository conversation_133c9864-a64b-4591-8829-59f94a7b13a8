// src/app/admin/plans/[id]/page.tsx
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PlanForm } from '@/components/admin/plans/plan-form';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { PlanService } from '@/lib/services/plan-service';
import { json } from 'stream/consumers';

export const metadata: Metadata = {
  title: 'Editar Plano | JáVai Admin',
  description: 'Edite um plano de assinatura existente',
};

interface EditPlanPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function EditPlanPage({ params }: EditPlanPageProps) {
  const planService = new PlanService();
  const plan = await planService.getById((await params).id);

  if (!plan) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Editar Plano</h1>
          <p className="text-muted-foreground">Edite os detalhes do plano de assinatura.</p>
        </div>
        <Button variant="outline" asChild>
          <Link href="/admin/plans">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Detalhes do Plano</CardTitle>
          <CardDescription>Atualize os detalhes do plano de assinatura.</CardDescription>
        </CardHeader>
        <CardContent>
          <PlanForm plan={JSON.parse(JSON.stringify(plan))} />
        </CardContent>
      </Card>
    </div>
  );
}
