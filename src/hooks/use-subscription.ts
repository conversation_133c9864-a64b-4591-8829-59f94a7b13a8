// src/hooks/use-subscription.ts
"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { Subscription, Plan } from '@/types/subscription';

interface UseSubscriptionProps {
  module?: keyof ModuleAccess;
  redirectUrl?: string;
  showToast?: boolean;
}

export function useSubscription({
  module,
  redirectUrl = '/dashboard/subscription/plans',
  showToast = true,
}: UseSubscriptionProps = {}) {
  const router = useRouter();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [plan, setPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        setLoading(true);
        
        // Fetch user's active subscription
        const response = await fetch('/api/subscription');
        const data = await response.json();
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch subscription');
        }
        
        if (!data.subscription) {
          if (showToast) {
            toast.error('Você precisa ter uma assinatura ativa para acessar este recurso.');
          }
          router.push(redirectUrl);
          return;
        }
        
        setSubscription(data.subscription);
        setPlan(data.plan);
        
        // Check module access if specified
        if (module && !data.plan.moduleAccess[module]) {
          if (showToast) {
            toast.error(`Seu plano atual não inclui acesso ao módulo de ${getModuleName(module)}.`);
          }
          router.push('/dashboard/subscription');
          return;
        }
      } catch (error) {
        console.error('Error fetching subscription:', error);
        if (showToast) {
          toast.error('Ocorreu um erro ao verificar sua assinatura. Tente novamente.');
        }
      } finally {
        setLoading(false);
      }
    };
    
    fetchSubscription();
  }, [module, redirectUrl, router, showToast]);
  
  return { subscription, plan, loading };
}

// Helper function to get module name in Portuguese
function getModuleName(module: string): string {
  switch (module) {
    case 'services':
      return 'Serviços';
    case 'properties':
      return 'Imóveis';
    case 'vehicles':
      return 'Veículos';
    case 'products':
      return 'Produtos';
    default:
      return module;
  }
}
