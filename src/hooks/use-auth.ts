// src/hooks/use-auth.ts
"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import { useEffect, useState } from "react";
import { auth as firebaseAuth } from "@/lib/firebase/client";
import { signInWithCustomToken } from "firebase/auth";

export function useAuth() {
  const { data: session, status } = useSession();
  const [firebaseUser, setFirebaseUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const syncFirebaseAuth = async () => {
      if (status === "loading") return;

      if (!session) {
        setFirebaseUser(null);
        setLoading(false);
        return;
      }

      try {
        // If we have a Firebase token from Google sign-in
        if (session.user.firebaseToken) {
          await signInWithCustomToken(firebaseAuth, session.user.firebaseToken);
        }
        
        // Firebase auth state will update automatically
        const unsubscribe = firebaseAuth.onAuthStateChanged((user) => {
          setFirebaseUser(user);
          setLoading(false);
        });

        return () => unsubscribe();
      } catch (error) {
        console.error("Error syncing Firebase auth:", error);
        setLoading(false);
      }
    };

    syncFirebaseAuth();
  }, [session, status]);

  return {
    user: session?.user,
    firebaseUser,
    loading: status === "loading" || loading,
    isAuthenticated: !!session,
    signIn,
    signOut,
  };
}
