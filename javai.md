# JáVai - Hub de Ofertas e Oportunidades

## Visão Geral do Projeto

O JáVai é um hub de ofertas e oportunidades que conecta pessoas a:

- Prestadores de serviços
- Imóveis para venda ou aluguel
- Veículos à venda
- Produtos diversos

## Stack Tecnológica

### Frontend

- **Framework**: Next.js 14+ com React 18+
- **Linguagem**: TypeScript
- **Estilização**: Tailwind CSS
- **Componentes**: Shadcn UI
- **Gerenciamento de Estado**: React Context API + SWR/React Query
- **Formulários**: React Hook Form + Zod
- **Autenticação**: NextAuth.js

### Backend

- **API Routes**: Next.js API Routes (serverless)
- **Banco de Dados**: Firebase Firestore
- **Autenticação**: Firebase Auth + NextAuth.js
- **Storage**: Firebase Storage
- **Notificações**: Firebase Cloud Messaging (FCM)
- **Email**: Resend
- **Mensagens**: Socket.io para chat em tempo real

### DevOps

- **Hospedagem**: Vercel
- **CI/CD**: GitHub Actions
- **Monitoramento**: Vercel Analytics

## Arquitetura do Sistema

### Estrutura de Pastas (Next.js App Router)

```
src/
├── app/                    # App Router
│   ├── (auth)/             # Rotas de autenticação
│   ├── (dashboard)/        # Área logada
│   ├── (public)/           # Páginas públicas
│   ├── api/                # API Routes
│   └── layout.tsx          # Layout raiz
├── components/             # Componentes React
│   ├── ui/                 # Componentes de UI
│   ├── forms/              # Componentes de formulário
│   ├── layout/             # Componentes de layout
│   └── [módulos]/          # Componentes específicos
├── lib/                    # Utilitários e helpers
│   ├── firebase/           # Configuração do Firebase
│   ├── auth/               # Lógica de autenticação
│   └── utils/              # Funções utilitárias
├── hooks/                  # Custom hooks
├── types/                  # Definições de tipos
├── services/               # Serviços de API
└── styles/                 # Estilos globais
```

### Módulos Principais

1. **Core** - Funcionalidades compartilhadas
2. **Serviços** - Prestadores de serviços e agendamentos
3. **Imóveis** - Listagem e detalhes de imóveis
4. **Veículos** - Listagem e detalhes de veículos
5. **Produtos** - Marketplace de produtos
6. **Mensagens** - Sistema de chat entre usuários
7. **Usuários** - Perfis, autenticação e preferências

## Modelo de Dados

### Coleções no Firestore

- **users**: Perfis de usuários
- **services**: Serviços oferecidos
- **properties**: Imóveis
- **vehicles**: Veículos
- **products**: Produtos
- **appointments**: Agendamentos
- **reviews**: Avaliações
- **messages**: Mensagens entre usuários
- **notifications**: Notificações
- **favorites**: Itens favoritos

## Funcionalidades por Módulo

### Módulo de Serviços

- Cadastro de prestadores de serviços
- Busca de prestadores por categoria, localização
- Agendamento de serviços
- Avaliações e comentários
- Gerenciamento de agenda

### Módulo de Imóveis

- Listagem de imóveis para venda/aluguel
- Filtros avançados (preço, área, quartos, etc.)
- Galeria de fotos e vídeos
- Mapa de localização
- Agendamento de visitas

### Módulo de Veículos

- Listagem de veículos à venda
- Filtros por marca, modelo, ano, preço
- Especificações detalhadas
- Galeria de fotos
- Agendamento de test drive

### Módulo de Produtos

- Marketplace de produtos diversos
- Categorização
- Sistema de busca e filtros
- Perguntas e respostas
- Avaliações de produtos

### Sistema de Mensagens

- Chat em tempo real entre usuários
- Notificações de novas mensagens
- Histórico de conversas
- Envio de anexos

## Implementação com Next.js

### Páginas Principais

```typescript
// app/(public)/page.tsx - Página inicial
export default function HomePage() {
  return (
    <main>
      <HeroSection />
      <FeaturedServices />
      <FeaturedProperties />
      <FeaturedVehicles />
      <HowItWorks />
      <Testimonials />
    </main>
  );
}

// app/(public)/services/page.tsx - Listagem de serviços
export default function ServicesPage({
  searchParams,
}: {
  searchParams: { category?: string; location?: string };
}) {
  return (
    <main>
      <ServiceFilters />
      <ServicesList
        category={searchParams.category}
        location={searchParams.location}
      />
    </main>
  );
}

// app/(public)/properties/page.tsx - Listagem de imóveis
// app/(public)/vehicles/page.tsx - Listagem de veículos
// app/(public)/products/page.tsx - Listagem de produtos
```

### API Routes

```typescript
// app/api/services/route.ts
import { NextResponse } from "next/server";
import { getFirestore } from "firebase-admin/firestore";
import { initFirebaseAdmin } from "@/lib/firebase/admin";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const category = searchParams.get("category");
  const location = searchParams.get("location");

  initFirebaseAdmin();
  const db = getFirestore();

  let query = db.collection("services");

  if (category) {
    query = query.where("category", "==", category);
  }

  if (location) {
    query = query.where("location", "==", location);
  }

  const snapshot = await query.limit(20).get();
  const services = snapshot.docs.map((doc) => ({
    id: doc.id,
    ...doc.data(),
  }));

  return NextResponse.json({ services });
}
```

### Componentes Reutilizáveis

```typescript
// components/ui/search-bar.tsx
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Search } from "lucide-react";
import { Input } from "./input";
import { Button } from "./button";

type SearchBarProps = {
  placeholder?: string;
  searchPath: string;
};

export function SearchBar({
  placeholder = "Buscar...",
  searchPath,
}: SearchBarProps) {
  const [query, setQuery] = useState("");
  const router = useRouter();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      router.push(`${searchPath}?q=${encodeURIComponent(query)}`);
    }
  };

  return (
    <form onSubmit={handleSearch} className="flex w-full max-w-lg">
      <Input
        type="text"
        placeholder={placeholder}
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        className="rounded-r-none"
      />
      <Button type="submit" className="rounded-l-none">
        <Search className="h-4 w-4 mr-2" />
        Buscar
      </Button>
    </form>
  );
}
```

### Autenticação

```typescript
// lib/auth/auth-options.ts
import { NextAuthOptions } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { FirebaseError } from "firebase/app";
import { signInWithEmailAndPassword } from "firebase/auth";
import { auth } from "@/lib/firebase/client";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Senha", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const userCredential = await signInWithEmailAndPassword(
            auth,
            credentials.email,
            credentials.password
          );

          const idToken = await userCredential.user.getIdToken();

          return {
            id: userCredential.user.uid,
            email: userCredential.user.email,
            name: userCredential.user.displayName,
            image: userCredential.user.photoURL,
            idToken,
          };
        } catch (error) {
          if (error instanceof FirebaseError) {
            console.error("Firebase auth error:", error.code);
          }
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.idToken = (user as any).idToken;
      }
      return token;
    },
    async session({ session, token }) {
      if (session.user) {
        (session.user as any).id = token.id;
        (session.user as any).idToken = token.idToken;
      }
      return session;
    },
  },
  pages: {
    signIn: "/login",
    error: "/login",
  },
};
```

### Formulários com React Hook Form e Zod

```typescript
// components/forms/property-form.tsx
"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@/components/ui/select";
import { createProperty } from "@/services/properties";
import { useToast } from "@/hooks/use-toast";

const propertySchema = z.object({
  title: z.string().min(5, "Título deve ter pelo menos 5 caracteres"),
  description: z
    .string()
    .min(20, "Descrição deve ter pelo menos 20 caracteres"),
  price: z.number().positive("Preço deve ser positivo"),
  type: z.enum(["house", "apartment", "land", "commercial"]),
  bedrooms: z.number().int().min(0),
  bathrooms: z.number().int().min(0),
  area: z.number().positive("Área deve ser positiva"),
  address: z.string().min(5, "Endereço deve ter pelo menos 5 caracteres"),
  city: z.string().min(2, "Cidade é obrigatória"),
  state: z.string().length(2, "Estado deve ter 2 caracteres"),
  isForRent: z.boolean().default(false),
});

type PropertyFormData = z.infer<typeof propertySchema>;

export function PropertyForm() {
  const { toast } = useToast();
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<PropertyFormData>({
    resolver: zodResolver(propertySchema),
    defaultValues: {
      isForRent: false,
    },
  });

  const onSubmit = async (data: PropertyFormData) => {
    try {
      await createProperty(data);
      toast({
        title: "Imóvel cadastrado com sucesso!",
        description:
          "Seu imóvel foi publicado e já está disponível para visualização.",
        variant: "success",
      });
    } catch (error) {
      toast({
        title: "Erro ao cadastrar imóvel",
        description:
          "Ocorreu um erro ao processar sua solicitação. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Campos do formulário */}
      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? "Cadastrando..." : "Cadastrar Imóvel"}
      </Button>
    </form>
  );
}
```

## Recursos Avançados

### Server Components e Data Fetching

```typescript
// app/(public)/services/[id]/page.tsx
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getServiceById } from "@/services/services";
import ServiceDetails from "@/components/services/service-details";
import RelatedServices from "@/components/services/related-services";

type Props = {
  params: { id: string };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const service = await getServiceById(params.id);

  if (!service) {
    return {
      title: "Serviço não encontrado | JáVai",
    };
  }

  return {
    title: `${service.name} | JáVai`,
    description: service.description.substring(0, 160),
  };
}

export default async function ServicePage({ params }: Props) {
  const service = await getServiceById(params.id);

  if (!service) {
    notFound();
  }

  return (
    <main>
      <ServiceDetails service={service} />
      <RelatedServices
        categoryId={service.categoryId}
        currentServiceId={service.id}
      />
    </main>
  );
}
```

### Notificações em Tempo Real

```typescript
// hooks/use-notifications.ts
"use client";

import { useEffect, useState } from "react";
import { onMessage, getMessaging } from "firebase/messaging";
import { useSession } from "next-auth/react";
import { useToast } from "@/hooks/use-toast";
import { getFirebaseApp } from "@/lib/firebase/client";

export function useNotifications() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [permission, setPermission] = useState<NotificationPermission | null>(
    null
  );

  useEffect(() => {
    if (typeof window !== "undefined" && "Notification" in window) {
      setPermission(Notification.permission);
    }
  }, []);

  const requestPermission = async () => {
    if (!("Notification" in window)) {
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      setPermission(permission);
      return permission === "granted";
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      return false;
    }
  };

  useEffect(() => {
    if (!session?.user || permission !== "granted") return;

    const app = getFirebaseApp();
    const messaging = getMessaging(app);

    const unsubscribe = onMessage(messaging, (payload) => {
      console.log("Message received:", payload);

      const { notification } = payload;

      if (notification) {
        toast({
          title: notification.title || "Nova notificação",
          description: notification.body,
          duration: 5000,
        });
      }
    });

    return () => unsubscribe();
  }, [session, permission, toast]);

  return { permission, requestPermission };
}
```

### Agendamento de Serviços

```typescript
// components/services/booking-form.tsx
"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { createAppointment } from "@/services/appointments";
import { useToast } from "@/hooks/use-toast";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";

const bookingSchema = z.object({
  date: z.date({
    required_error: "Por favor selecione uma data",
  }),
  time: z.string({
    required_error: "Por favor selecione um horário",
  }),
  notes: z.string().optional(),
});

type BookingFormData = z.infer<typeof bookingSchema>;

type BookingFormProps = {
  serviceId: string;
  providerId: string;
  serviceName: string;
  availableSlots: Record<string, string[]>;
};

export function BookingForm({
  serviceId,
  providerId,
  serviceName,
  availableSlots,
}: BookingFormProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<BookingFormData>({
    resolver: zodResolver(bookingSchema),
  });

  const availableDates = Object.keys(availableSlots).map(
    (date) => new Date(date)
  );

  const onSubmit = async (data: BookingFormData) => {
    if (!session?.user) {
      toast({
        title: "Login necessário",
        description: "Você precisa estar logado para agendar um serviço",
        variant: "destructive",
      });
      router.push("/login");
      return;
    }

    try {
      await createAppointment({
        serviceId,
        providerId,
        serviceName,
        date: format(data.date, "yyyy-MM-dd"),
        time: data.time,
        notes: data.notes,
      });

      toast({
        title: "Agendamento realizado!",
        description: `Seu agendamento para ${format(data.date, "dd/MM/yyyy", {
          locale: ptBR,
        })} às ${data.time} foi solicitado.`,
        variant: "success",
      });

      router.push("/dashboard/appointments");
    } catch (error) {
      toast({
        title: "Erro ao agendar",
        description:
          "Ocorreu um erro ao processar seu agendamento. Tente novamente.",
        variant: "destructive",
      });
    }
  };

  const dateString = selectedDate ? format(selectedDate, "yyyy-MM-dd") : "";
  const timeSlots = dateString ? availableSlots[dateString] || [] : [];

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="space-y-2">
        <label className="text-sm font-medium">Selecione uma data</label>
        <Calendar
          mode="single"
          selected={selectedDate}
          onSelect={setSelectedDate}
          disabled={[
            { before: new Date() },
            (date) =>
              !availableDates.some(
                (d) =>
                  d.getDate() === date.getDate() &&
                  d.getMonth() === date.getMonth() &&
                  d.getFullYear() === date.getFullYear()
              ),
          ]}
          className="rounded-md border"
        />
        {errors.date && (
          <p className="text-sm text-red-500">{errors.date.message}</p>
        )}
      </div>

      {selectedDate && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Selecione um horário</label>
          <div className="grid grid-cols-3 gap-2">
            {timeSlots.length > 0 ? (
              timeSlots.map((time) => (
                <label key={time} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    value={time}
                    {...register("time")}
                    className="form-radio"
                  />
                  <span>{time}</span>
                </label>
              ))
            ) : (
              <p className="col-span-3 text-sm text-muted-foreground">
                Não há horários disponíveis para esta data.
              </p>
            )}
          </div>
          {errors.time && (
            <p className="text-sm text-red-500">{errors.time.message}</p>
          )}
        </div>
      )}

      <div className="space-y-2">
        <label className="text-sm font-medium">Observações (opcional)</label>
        <Textarea
          {...register("notes")}
          placeholder="Informe detalhes adicionais sobre sua necessidade..."
          className="min-h-[100px]"
        />
      </div>

      <Button type="submit" disabled={isSubmitting || !selectedDate}>
        {isSubmitting ? "Agendando..." : "Agendar Serviço"}
      </Button>
    </form>
  );
}
```

## Implantação e CI/CD

### Configuração do Vercel

```json
// vercel.json
{
  "buildCommand": "next build",
  "devCommand": "next dev",
  "installCommand": "npm install",
  "framework": "nextjs",
  "regions": ["sfo1", "gru1"],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### GitHub Actions para CI/CD

```yaml
# .github/workflows/ci.yml
name: CI/CD

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: "npm"
      - run: npm ci
      - run: npm run lint
      - run: npm run test

  deploy:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: vercel/actions/cli@master
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: "--prod"
```

## Considerações Finais

### Melhores Práticas

1. **Performance**

   - Utilizar Server Components para conteúdo estático
   - Implementar ISR (Incremental Static Regeneration) para páginas frequentemente acessadas
   - Otimizar imagens com next/image
   - Implementar lazy loading para componentes pesados

2. **SEO**

   - Utilizar metadados dinâmicos com generateMetadata
   - Implementar sitemap.xml e robots.txt
   - Garantir semântica HTML correta
   - Otimizar para Core Web Vitals

3. **Acessibilidade**

   - Seguir WCAG 2.1 AA
   - Implementar navegação por teclado
   - Garantir contraste adequado
   - Utilizar ARIA quando necessário

4. **Segurança**
   - Implementar CSP (Content Security Policy)
   - Validar inputs no servidor
   - Proteger rotas sensíveis
   - Implementar rate limiting

### Próximos Passos

1. **MVP Inicial**

   - Implementar módulo de serviços completo
   - Sistema de autenticação e perfis
   - Busca básica

2. **Expansão Gradual**

   - Adicionar módulo de imóveis
   - Adicionar módulo de veículos
   - Implementar sistema de mensagens

3. **Recursos Avançados**
   - Integração com pagamentos
   - Sistema de notificações avançado
   - Recursos de geolocalização
   - Análises e relatórios
