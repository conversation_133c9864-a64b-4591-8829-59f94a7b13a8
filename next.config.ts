import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  allowedDevOrigins: [
    'https://wendertech.com.br',
    'https://webhook.wendertech.com.br',
    // 'http://localhost:3000',
    'http://localhost:3001',
  ],
  images: {
    remotePatterns: [
      // Allow images from a specific domain
      {
        protocol: 'https',
        hostname: 'source.unsplash.com',
        port: '',
        pathname: '/**',
      },
      // new URL('https://images.unsplash.com/**'),
      // new URL('https://source.unsplash.com/**'),
      // new URL('https://source.unsplash.com/random/800x600?painting'),
    ],
  },
};

export default nextConfig;
