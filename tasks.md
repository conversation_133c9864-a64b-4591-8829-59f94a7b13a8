# JáVai - Checklist de Tarefas

## Visão Geral

Este documento lista os passos necessários para implementar o sistema JáVai, incluindo a área de administração e o sistema de assinatura mensal.

## Fase 1: Configuração Inicial e Estrutura Base

- [x] Inicializar projeto Next.js com TypeScript

  - [x] Configurar Next.js 14+ com App Router
  - [x] Configurar TypeScript
  - [x] Configurar ESLint e Prettier

- [x] Configurar Tailwind CSS e Shadcn UI

  - [x] Instalar e configurar Tailwind CSS
  - [x] Configurar componentes base do Shadcn UI
  - [x] Criar tema personalizado para o JáVai

- [x] Configurar Firebase

  - [x] Criar projeto no Firebase
  - [x] Configurar Firestore
  - [x] Configurar Firebase Auth
  - [x] Configurar Firebase Storage
  - [x] Configurar Firebase Cloud Messaging

- [x] Configurar NextAuth.js com Firebase Auth
  - [x] Implementar autenticação com email/senha
  - [x] Implementar autenticação com Google
  - [x] Configurar middleware de proteção de rotas

## Fase 2: Implementação do Sistema de Assinatura

- [x] Modelar estrutura de dados para assinaturas

  - [x] Criar coleção de planos no Firestore
  - [x] Criar coleção de assinaturas no Firestore
  - [x] Definir relacionamentos entre usuários e assinaturas

- [x] Implementar integração com gateway de pagamento

  - [x] Configurar Stripe para pagamentos recorrentes
  - [x] Implementar webhooks para processamento de eventos
    - [x] Melhorar webhook com tratamento de erros robusto
    - [x] Adicionar suporte para mais eventos do Stripe
    - [x] Criar script para testar webhook localmente
  - [x] Criar API routes para gerenciamento de assinaturas
    - [x] Implementar API para criar sessão de checkout
    - [x] Implementar API para portal de gerenciamento de assinatura
    - [x] Implementar webhook para processar eventos do Stripe

- [x] Desenvolver interface de assinatura

  - [x] Criar página de seleção de planos
  - [x] Implementar formulário de checkout
  - [x] Criar página de gerenciamento de assinatura para usuários
  - [x] Corrigir erro de importação do Firebase Admin no webhook
  - [x] Melhorar alinhamento dos planos na página de assinatura
  - [x] Implementar gestão de planos na área administrativa
  - [x] Integrar planos do Firebase com a página de assinatura
  - [x] Criar página de sucesso para assinatura
  - [x] Implementar planos mockados para testes

- [x] Implementar sistema de controle de acesso baseado em assinatura
  - [x] Criar middleware para verificação de assinatura ativa
  - [x] Implementar lógica de limitação de recursos baseada no plano
  - [x] Criar sistema de notificação para assinaturas prestes a expirar

## Fase 3: Área de Administração Principal

- [x] Projetar e implementar a área administrativa

  - [x] Avaliar opções (AdminJS vs. solução personalizada)
  - [x] Configurar estrutura base da área administrativa
  - [x] Implementar autenticação e autorização para administradores
  - [x] Implementar inicialização automática do usuário administrador

- [x] Implementar gerenciamento de usuários

  - [x] Criar interface para visualização de usuários
  - [x] Implementar funcionalidades de bloqueio/desbloqueio de usuários
  - [x] Criar dashboard com métricas de usuários

- [x] Implementar gerenciamento de planos de assinatura

  - [x] Criar interface para criação/edição de planos
  - [x] Implementar configuração de módulos por plano
  - [x] Implementar configuração de limites de anúncios por plano
  - [x] Adicionar funcionalidade de clonagem de planos
  - [x] Exibir planos do banco em vez de mocks

- [x] Implementar gerenciamento de conteúdo

  - [x] Criar interface para moderação de anúncios
  - [x] Implementar sistema de denúncias
  - [x] Criar ferramentas para gerenciamento de categorias

- [x] Desenvolver dashboard administrativo
  - [x] Criar visão geral de métricas do sistema
  - [x] Implementar gráficos de desempenho
  - [x] Criar relatórios de receita e conversão

## Fase 4: Módulos Principais do Sistema

- [x] Implementar módulo de Serviços

  - [x] Criar modelos e interfaces para serviços
  - [x] Implementar serviços base para operações CRUD
  - [x] Implementar serviços específicos para serviços
  - [x] Criar interface administrativa para gerenciamento de serviços
  - [x] Criar formulários de cadastro de serviços
  - [x] Implementar verificação de limites do plano
  - [x] Salvar dados no banco em vez de usar mocks
  - [x] Implementar busca e filtros
  - [x] Criar página pública de listagem de serviços
  - [x] Implementar visualização de serviços próximos ao usuário
  - [ ] Desenvolver sistema de agendamento
  - [ ] Implementar avaliações e comentários

- [ ] Implementar módulo de Imóveis

  - [ ] Criar formulários de cadastro de imóveis
  - [ ] Implementar busca com filtros avançados
  - [ ] Desenvolver galeria de fotos e vídeos
  - [ ] Implementar mapa de localização
  - [ ] Criar sistema de agendamento de visitas

- [ ] Implementar módulo de Veículos

  - [ ] Criar formulários de cadastro de veículos
  - [ ] Implementar busca com filtros específicos
  - [ ] Desenvolver galeria de fotos
  - [ ] Criar sistema de agendamento de test drive

- [ ] Implementar módulo de Produtos

  - [ ] Criar formulários de cadastro de produtos
  - [ ] Implementar marketplace com categorias
  - [ ] Desenvolver sistema de perguntas e respostas
  - [ ] Implementar avaliações de produtos

- [ ] Desenvolver sistema de mensagens
  - [ ] Implementar chat em tempo real com Socket.io
  - [ ] Criar notificações de novas mensagens
  - [ ] Desenvolver histórico de conversas
  - [ ] Implementar envio de anexos

## Fase 5: Dashboard para Anunciantes/Prestadores/Vendedores

- [x] Desenvolver painel de controle para anunciantes

  - [x] Criar dashboard com métricas de desempenho (visualizações, contatos, conversões)
  - [x] Implementar gráficos de atividade e tendências
  - [x] Desenvolver sistema de notificações personalizadas

- [x] Implementar gerenciamento de anúncios

  - [x] Criar interface para gerenciamento de serviços
  - [x] Implementar formulário de criação/edição de serviços
  - [x] Implementar página de detalhes do serviço para o anunciante
  - [x] Adicionar funcionalidades de publicação e arquivamento de serviços
  - [x] Implementar aba de mídia e detalhes no formulário de serviços
  - [x] Implementar upload de imagens para o Firebase Storage
  - [x] Implementar estatísticas por anúncio
  - [x] Desenvolver sistema de promoção de anúncios

- [x] Desenvolver sistema de agenda e disponibilidade

  - [x] Criar calendário de disponibilidade para serviços
  - [x] Implementar gerenciamento de horários de trabalho
  - [x] Desenvolver sistema de confirmação/recusa de agendamentos

- [x] Implementar gerenciamento de perfil profissional

  - [x] Criar editor de perfil avançado
  - [x] Implementar gestão de localização do anunciante
  - [x] Implementar gestão de disponibilidade do anunciante
  - [ ] Implementar upload de certificações e documentos
  - [ ] Desenvolver sistema de avaliações e respostas

- [ ] Criar sistema de relatórios para anunciantes
  - [ ] Implementar relatórios de desempenho
  - [ ] Desenvolver análise de público-alvo
  - [ ] Criar sugestões automáticas para melhorias

## Fase 6: Área de Cliente (Compradores/Contratantes)

- [x] Desenvolver painel de controle para clientes

  - [x] Criar dashboard com atividades recentes
  - [x] Implementar sistema de notificações
  - [x] Desenvolver acesso rápido a favoritos e histórico

- [ ] Implementar gerenciamento de preferências

  - [ ] Criar sistema de preferências de busca
  - [ ] Implementar alertas personalizados
  - [ ] Desenvolver recomendações baseadas em preferências

- [ ] Desenvolver gerenciamento de agendamentos

  - [ ] Criar calendário de agendamentos
  - [ ] Implementar sistema de lembretes
  - [ ] Desenvolver histórico de agendamentos

- [ ] Implementar gerenciamento de perfil pessoal
  - [ ] Criar editor de perfil
  - [ ] Implementar configurações de privacidade
  - [x] Desenvolver gerenciamento de métodos de pagamento

## Fase 7: Recursos Avançados, Testes e Lançamento

- [ ] Implementar sistema de notificações

  - [ ] Configurar Firebase Cloud Messaging
  - [ ] Implementar notificações por email com Resend
  - [ ] Criar centro de notificações no app

- [ ] Desenvolver sistema de favoritos e listas

  - [ ] Implementar funcionalidade de favoritar itens
  - [ ] Criar listas personalizadas
  - [ ] Desenvolver recomendações baseadas em favoritos

- [ ] Implementar recursos de SEO

  - [ ] Configurar metadados dinâmicos
  - [ ] Criar sitemap.xml e robots.txt
  - [ ] Otimizar para Core Web Vitals

- [x] Desenvolver recursos de geolocalização

  - [x] Implementar busca por proximidade
  - [x] Criar mapas interativos
  - [x] Desenvolver filtros baseados em localização
  - [x] Implementar seleção padronizada de localidades
  - [x] Adicionar campos normalizados para melhorar a busca
  - [x] Implementar cache de localização no backend para otimizar consultas
  - [x] Implementar preenchimento automático de endereço por CEP com cache
  - [x] Corrigir loop infinito no componente de CEP

- [ ] Implementar testes automatizados

  - [ ] Configurar Jest para testes unitários
  - [ ] Implementar testes de integração
  - [ ] Configurar Cypress para testes E2E

- [ ] Otimizar performance

  - [ ] Implementar Server Components para conteúdo estático
  - [ ] Configurar ISR para páginas frequentemente acessadas
  - [ ] Otimizar carregamento de imagens e assets

- [x] Configurar monitoramento e analytics

  - [x] Implementar Vercel Analytics
  - [x] Configurar Firebase Analytics
  - [x] Implementar logging de erros
    - [x] Criar serviço de logs para depuração
    - [x] Implementar endpoint para logs do cliente
    - [x] Integrar logs com Stripe e outras APIs

- [x] Implementar API para semear dados de teste

  - [x] Criar rota para semear categorias
  - [x] Criar rota para semear serviços

- [x] Corrigir problemas de layout

  - [x] Corrigir sidebar para ocupar toda a altura da tela
  - [x] Corrigir sidebar mobile no painel do anunciante
  - [x] Corrigir problema de autenticação na tela de gestão de serviços
  - [x] Adicionar loading spinner enquanto verifica a autenticação
  - [x] Corrigir erro de importação do storage no upload de imagens

- [ ] Preparar para lançamento
  - [ ] Configurar ambiente de produção
  - [ ] Implementar CI/CD com GitHub Actions
  - [ ] Realizar testes de carga
  - [ ] Criar documentação para usuários

## Detalhamento do Sistema de Assinatura

### Planos de Assinatura

Os planos de assinatura serão configuráveis pelo administrador e poderão incluir:

1. **Variações por Módulo**

   - Acesso a módulos específicos (Serviços, Imóveis, Veículos, Produtos)
   - Combinações personalizadas de módulos

2. **Limites de Anúncios**

   - Quantidade máxima de anúncios ativos por módulo
   - Opções para destacar anúncios (anúncios premium)

3. **Recursos Premium**
   - Estatísticas avançadas de visualização
   - Prioridade em resultados de busca
   - Recursos de comunicação avançados

### Processo de Assinatura

1. **Seleção de Plano** ✅

   - Página de comparação de planos ✅
   - Detalhamento de benefícios ✅

2. **Checkout** ✅

   - Integração com Stripe para pagamento recorrente ✅
   - Opções de periodicidade (mensal, anual) ✅

3. **Gerenciamento** ✅
   - Painel para visualizar status da assinatura ✅
   - Opções para upgrade/downgrade ✅
   - Histórico de pagamentos ✅

## Detalhamento das Áreas de Administração e Dashboards

### Funcionalidades da Área Administrativa

1. **Gestão de Usuários**

   - Visualização e edição de perfis
   - Gerenciamento de permissões
   - Bloqueio/desbloqueio de contas

2. **Gestão de Planos**

   - Criação e edição de planos de assinatura
   - Configuração de preços e recursos
   - Definição de limites por plano

3. **Moderação de Conteúdo**

   - Aprovação/rejeição de anúncios
   - Gerenciamento de denúncias
   - Bloqueio de conteúdo inadequado

4. **Analytics e Relatórios**
   - Dashboard com métricas principais
   - Relatórios de receita
   - Estatísticas de uso por módulo
   - Análise de conversão e retenção

### Segurança e Acesso (Administração Principal)

1. **Níveis de Acesso**

   - Super Admin (acesso total)
   - Moderadores (acesso limitado à moderação de conteúdo)
   - Analistas (acesso apenas a relatórios)

2. **Auditoria**
   - Log de ações administrativas
   - Histórico de alterações
   - Alertas de segurança

### Funcionalidades do Dashboard para Anunciantes

1. **Gestão de Anúncios**

   - Interface unificada para todos os tipos de anúncios
   - Editor de anúncios com preview em tempo real
   - Sistema de status (ativo, pausado, rascunho)
   - Estatísticas por anúncio (visualizações, contatos, conversão)

2. **Gestão de Agenda**

   - Calendário de disponibilidade configurável
   - Definição de horários de trabalho e exceções
   - Visualização de agendamentos pendentes e confirmados
   - Sistema de confirmação/recusa com notificação automática

3. **Gestão Financeira** ✅

   - Visão geral de faturamento ✅
   - Histórico de transações ✅
   - Status da assinatura e recursos disponíveis ✅
   - Opções de upgrade/downgrade de plano ✅

4. **Comunicação com Clientes**

   - Inbox unificado de mensagens
   - Histórico de conversas por cliente
   - Templates de respostas rápidas
   - Notificações de novas mensagens

5. **Analytics para Anunciantes**
   - Dashboard personalizado com KPIs relevantes
   - Análise de desempenho por período
   - Insights sobre público-alvo
   - Recomendações para otimização de anúncios

### Funcionalidades da Área de Cliente

1. **Gestão de Preferências**

   - Configuração de buscas salvas
   - Alertas personalizados por email/push
   - Configuração de recomendações
   - Preferências de privacidade e comunicação

2. **Gestão de Agendamentos**

   - Visualização de todos os agendamentos
   - Calendário integrado
   - Sistema de lembretes configuráveis
   - Histórico de serviços/visitas realizados

3. **Favoritos e Listas**

   - Organização de itens favoritos por categoria
   - Criação de listas personalizadas
   - Comparação entre itens
   - Compartilhamento de listas

4. **Histórico e Avaliações**
   - Histórico de interações
   - Sistema de avaliação pós-serviço/compra
   - Gerenciamento de avaliações realizadas
   - Sugestões baseadas no histórico
