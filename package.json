{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.7", "@radix-ui/react-alert-dialog": "^1.1.8", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@stripe/stripe-js": "^7.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "framer-motion": "^12.7.4", "leaflet": "^1.9.4", "lucide-react": "^0.488.0", "next": "15.3.1", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-loader-spinner": "^6.1.6", "resend": "^4.3.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.3", "stripe": "^18.0.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "typewriter-effect": "^2.21.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}